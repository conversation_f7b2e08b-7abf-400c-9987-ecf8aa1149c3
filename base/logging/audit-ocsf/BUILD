# TODO: Uncomment when Python/Rust implementations are ready
# load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

# load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
# load("//tools/bzl:python.bzl", "py_library")
# load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
# load("//tools/bzl/pytest:defs.bzl", "pytest_test")

# Go library for OCSF audit logging
go_library(
    name = "audit_ocsf_go",
    srcs = [
        "api_activity_event.go",
        "audit_ocsf.go",
        "authentication_event.go",
        "authorize_session_event.go",
        "constants.go",
        "test_utils.go",
    ],
    importpath = "github.com/augmentcode/augment/base/logging/audit-ocsf",
    visibility = BASE_VISIBILITY,
)

# Go tests for OCSF audit logging
go_test(
    name = "audit_ocsf_go_test",
    srcs = [
        "api_activity_event_test.go",
        "audit_ocsf_test.go",
        "authentication_event_test.go",
        "authorize_session_event_test.go",
        "builder_test.go",
        "constants_test.go",
        "example_api_activity_test.go",
        "example_authorize_session_test.go",
        "example_test.go",
    ],
    embed = [
        ":audit_ocsf_go",
    ],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)

# Python library for OCSF audit logging (placeholder for future implementation)
# TODO: Uncomment when Python implementation is ready
# py_library(
#     name = "audit_ocsf_py",
#     srcs = ["audit_ocsf.py"],
#     visibility = BASE_VISIBILITY,
#     deps = [
#         requirement("pydantic"),
#     ],
# )

# Python tests for OCSF audit logging (placeholder for future implementation)
# TODO: Uncomment when Python implementation is ready
# pytest_test(
#     name = "audit_ocsf_test",
#     srcs = ["audit_ocsf_test.py"],
#     deps = [":audit_ocsf_py"],
# )

# Rust library for OCSF audit logging (placeholder for future implementation)
# TODO: Uncomment when Rust implementation is ready
# rust_library(
#     name = "audit_ocsf_rs",
#     srcs = ["audit_ocsf.rs"],
#     aliases = aliases(),
#     crate_name = "audit_ocsf",
#     edition = "2021",
#     proc_macro_deps = all_crate_deps(
#         proc_macro = True,
#     ),
#     visibility = BASE_VISIBILITY,
#     deps = all_crate_deps(
#         normal = True,
#     ),
# )

# Rust tests for OCSF audit logging (placeholder for future implementation)
# TODO: Uncomment when Rust implementation is ready
# rust_test(
#     name = "audit_ocsf_test_rs",
#     crate = ":audit_ocsf_rs",
# )
