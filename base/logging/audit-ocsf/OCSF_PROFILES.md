# OCSF Profile System Documentation

## Overview

The Open Cybersecurity Schema Framework (OCSF) uses a **profile system** to organize and extend event schemas. This system allows for modular field definitions where:

- **Base classes** define core fields required for specific event types
- **Profile extensions** add specialized fields for specific use cases or environments
- **Field groups** organize fields by their purpose (classification, context, occurrence, primary)

## Understanding the OCSF Specification Interface

When viewing the OCSF specification online at https://schema.ocsf.io/, the interface shows different field sets based on selected profiles and tabs:

### Profile Tabs
- **Base Event Attributes**: Core fields inherited from the base event class
- **Classification**: Event categorization fields
- **Context**: Additional context like confidence, risk assessment
- **Occurrence**: Timing and occurrence fields
- **Primary**: Main event data fields
- **Optional Attributes**: Non-required fields
- **Recommended Attributes**: Suggested fields for completeness

### Profile Extensions
The API Activity class supports these profile extensions:
- **Cloud Profile**: Cloud environment details
- **Container Profile**: Container-specific fields
- **Data Classification Profile**: Data sensitivity classification
- **Date/Time Profile**: Enhanced date/time handling
- **Host Profile**: Host/endpoint information
- **OSINT Profile**: Open Source Intelligence data
- **Security Control Profile**: Security control outcomes
- **Trace Profile**: Distributed tracing information

## API Activity Event Field Mapping

### Base API Activity Class Fields

#### Required Fields
- `actor` - The user/role/process that performed the activity
- `api` - Details about the API call
- `src_endpoint` - Source network endpoint

#### Recommended Fields
- `dst_endpoint` - Destination network endpoint
- `http_request` - HTTP request details
- `http_response` - HTTP response details
- `resources` - Affected resources
- `observables` - Observable indicators
- `action_id` - Action taken
- `disposition_id` - Outcome disposition
- `status_id` - Event status
- `message` - Event description

#### Optional Fields
- `attacks` - MITRE ATT&CK information
- `authorizations` - Authorization details
- `enrichments` - External data enrichments
- `firewall_rule` - Related firewall rules
- `malware` - Malware information
- `policy` - Related policies
- `unmapped` - Unmapped source data

### Profile Extension Fields

#### Cloud Profile
- `cloud` (Required) - Cloud environment details

#### OSINT Profile  
- `osint` (Required) - Open Source Intelligence data

#### Security Control Profile
- `is_alert` (Recommended) - Whether event is alertable

#### Trace Profile
- `trace` (Recommended) - Distributed tracing information

#### Context Group
- `confidence_id` (Recommended) - Confidence level
- `confidence` (Optional) - Confidence description
- `confidence_score` (Optional) - Confidence score
- `risk_level_id` (Optional) - Risk level
- `risk_level` (Optional) - Risk level description
- `risk_score` (Optional) - Risk score
- `risk_details` (Optional) - Risk details

## Implementation Notes

### Field Inclusion Strategy
Our implementation includes fields from multiple profiles to provide comprehensive API activity logging capabilities. Each field is commented with its profile source:

```go
// Base API Activity Class fields
Actor       Actor           `json:"actor"`        // Base: Required
API         API             `json:"api"`          // Base: Required  
SrcEndpoint NetworkEndpoint `json:"src_endpoint"` // Base: Required

// Profile Extension fields
Cloud   Cloud   `json:"cloud"`           // Cloud Profile: Required
OSINT   []OSINT `json:"osint,omitempty"` // OSINT Profile: Required
IsAlert *bool   `json:"is_alert,omitempty"` // Security Control Profile: Recommended
Trace   *Trace  `json:"trace,omitempty"`    // Trace Profile: Recommended
```

### Profile Selection
When implementing OCSF events, you can choose which profiles to support based on your use case:

- **Minimal Implementation**: Only base class fields
- **Cloud-Focused**: Base + Cloud profile
- **Security-Focused**: Base + Security Control + OSINT profiles
- **Comprehensive**: All profiles (our approach)

### Validation Considerations
- Base class fields should be validated according to OCSF requirements
- Profile extension fields are typically optional but may be required within their profile context
- Our implementation validates base fields and allows profile fields to be validated at the application level

## Usage Examples

### Base Fields Only
```go
event := NewAPIActivityEventBuilder(ActivityAPICreate).
    WithActor(actor).           // Base: Required
    WithAPI(api).               // Base: Required
    WithSrcEndpoint(endpoint).  // Base: Required
    Build()
```

### With Cloud Profile
```go
event := NewAPIActivityEventBuilder(ActivityAPICreate).
    WithActor(actor).           // Base: Required
    WithAPI(api).               // Base: Required
    WithSrcEndpoint(endpoint).  // Base: Required
    WithCloud(cloud).           // Cloud Profile: Required
    Build()
```

### With Security Control Profile
```go
event := NewAPIActivityEventBuilder(ActivityAPICreate).
    WithActor(actor).           // Base: Required
    WithAPI(api).               // Base: Required
    WithSrcEndpoint(endpoint).  // Base: Required
    WithIsAlert(true).          // Security Control Profile: Recommended
    WithDisposition(DispositionAllowed). // Security Control Profile: Recommended
    Build()
```

## Future Extensibility

The profile system makes it easy to:
1. Add new profile support by extending the struct with new fields
2. Implement profile-specific validation logic
3. Support different OCSF specification versions
4. Maintain backward compatibility while adding new capabilities

When adding new OCSF event types, follow the same pattern of clearly documenting which fields come from which profiles to maintain clarity and consistency.
