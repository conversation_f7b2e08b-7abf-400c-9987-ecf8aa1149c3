package auditocsf

import (
	"encoding/json"
	"strings"
	"testing"
)

func TestAuthenticationEventWithEndpoints(t *testing.T) {
	// Test that Authentication events can include src_endpoint and dst_endpoint
	srcEndpoint := NetworkEndpoint{
		IP:       "*************",
		Hostname: "client.example.com",
		Port:     443,
	}

	dstEndpoint := NetworkEndpoint{
		IP:       "*********",
		Hostname: "auth.example.com",
		Port:     443,
	}

	event := NewAuthenticationEventBuilder(ActivityLogon).
		WithUserUID("user123", "INTERNAL").
		WithSrcEndpoint(srcEndpoint).
		WithDstEndpoint(dstEndpoint).
		WithStatus(StatusSuccess).
		WithMessage("User authentication with endpoint information").
		Build()

	// Verify the endpoints are set correctly
	if event.SrcEndpoint == nil {
		t.Error("Expected SrcEndpoint to be set")
	} else {
		if event.SrcEndpoint.IP != "*************" {
			t.<PERSON><PERSON><PERSON>("Expected SrcEndpoint IP to be '*************', got '%s'", event.SrcEndpoint.IP)
		}
		if event.SrcEndpoint.Hostname != "client.example.com" {
			t.Errorf("Expected SrcEndpoint Hostname to be 'client.example.com', got '%s'", event.SrcEndpoint.Hostname)
		}
	}

	if event.DstEndpoint == nil {
		t.Error("Expected DstEndpoint to be set")
	} else {
		if event.DstEndpoint.IP != "*********" {
			t.Errorf("Expected DstEndpoint IP to be '*********', got '%s'", event.DstEndpoint.IP)
		}
	}

	// Test JSON serialization includes the endpoint fields
	jsonData, err := json.Marshal(event)
	if err != nil {
		t.Fatalf("Failed to marshal event to JSON: %v", err)
	}

	jsonStr := string(jsonData)
	if !strings.Contains(jsonStr, "src_endpoint") {
		t.Error("Expected JSON to contain 'src_endpoint' field")
	}
	if !strings.Contains(jsonStr, "dst_endpoint") {
		t.Error("Expected JSON to contain 'dst_endpoint' field")
	}
	if !strings.Contains(jsonStr, "*************") {
		t.Error("Expected JSON to contain source IP address")
	}
}

func TestAuthorizeSessionEventWithEndpoints(t *testing.T) {
	// Test that AuthorizeSession events can include src_endpoint and dst_endpoint
	srcEndpoint := NetworkEndpoint{
		IP:       "***********",
		Hostname: "admin.example.com",
		Port:     22,
	}

	event := NewAuthorizeSessionEventBuilder(ActivityAssignPrivileges).
		WithUserUID("admin123", "INTERNAL").
		WithPrivileges([]string{"read", "write", "admin"}).
		WithSrcEndpoint(srcEndpoint).
		WithStatus(StatusSuccess).
		WithMessage("Session authorization with endpoint information").
		Build()

	// Verify the endpoint is set correctly
	if event.SrcEndpoint == nil {
		t.Error("Expected SrcEndpoint to be set")
	} else {
		if event.SrcEndpoint.IP != "***********" {
			t.Errorf("Expected SrcEndpoint IP to be '***********', got '%s'", event.SrcEndpoint.IP)
		}
		if event.SrcEndpoint.Port != 22 {
			t.Errorf("Expected SrcEndpoint Port to be 22, got %d", event.SrcEndpoint.Port)
		}
	}

	// Test JSON serialization includes the endpoint field
	jsonData, err := json.Marshal(event)
	if err != nil {
		t.Fatalf("Failed to marshal event to JSON: %v", err)
	}

	jsonStr := string(jsonData)
	if !strings.Contains(jsonStr, "src_endpoint") {
		t.Error("Expected JSON to contain 'src_endpoint' field")
	}
	if !strings.Contains(jsonStr, "***********") {
		t.Error("Expected JSON to contain source IP address")
	}
}
