package auditocsf

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAPIActivityEvent_SetDefaults(t *testing.T) {
	event := APIActivityEvent{
		BaseEvent: BaseEvent{
			ActivityID: ActivityAPICreate,
		},
	}

	event.SetDefaults()

	assert.Equal(t, CategoryApplicationActivity, event.CategoryUID)
	assert.Equal(t, ClassAPIActivity, event.ClassUID)
	assert.Equal(t, int64(600301), event.TypeUID) // 6003 * 100 + 1
	assert.False(t, event.Time.IsZero())
	assert.Equal(t, SeverityInformational, event.SeverityID)
}

func TestAPIActivityEvent_CalculateTypeUID(t *testing.T) {
	tests := []struct {
		name       string
		classUID   int
		activityID ActivityID
		expected   int64
	}{
		{"Create", ClassAPIActivity, ActivityAPICreate, 600301},
		{"Read", ClassAPIActivity, ActivityAPIRead, 600302},
		{"Update", ClassAPIActivity, ActivityAPIUpdate, 600303},
		{"Delete", ClassAPIActivity, ActivityAPIDelete, 600304},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := APIActivityEvent{
				BaseEvent: BaseEvent{
					ClassUID:   tt.classUID,
					ActivityID: tt.activityID,
				},
			}
			assert.Equal(t, tt.expected, event.CalculateTypeUID())
		})
	}
}

func TestAPIActivityEvent_Validate(t *testing.T) {
	validEvent := APIActivityEvent{
		BaseEvent: BaseEvent{
			ActivityID:  ActivityAPICreate,
			CategoryUID: CategoryApplicationActivity,
			ClassUID:    ClassAPIActivity,
			SeverityID:  SeverityInformational,
			Time:        time.Now(),
			TypeUID:     600301,
			Metadata: Metadata{
				Version: "1.5.0",
			},
		},
	}

	t.Run("Valid event", func(t *testing.T) {
		err := validEvent.Validate()
		assert.NoError(t, err)
	})

	t.Run("Missing activity_id", func(t *testing.T) {
		event := validEvent
		event.ActivityID = 0
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "activity_id is required")
	})

	t.Run("Missing metadata version", func(t *testing.T) {
		event := validEvent
		event.Metadata.Version = ""
		err := event.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "metadata.version is required")
	})
}

func TestOCSFAuditLogger_LogAPIActivity(t *testing.T) {
	mockPrinter := &MockPrinter{}
	logger := NewOCSFAuditLogger(mockPrinter)

	event := APIActivityEvent{
		BaseEvent: BaseEvent{
			ActivityID: ActivityAPICreate,
			Message:    "Custom API activity event",
			Metadata: Metadata{
				Version: "1.5.0",
				Product: &Product{
					Name:    "Test",
					Vendor:  "Test Vendor",
					Feature: "audit-ocsf",
				},
			},
		},
		Actor: Actor{
			User: &User{
				UID:  "actor123",
				Type: "INTERNAL_IAP",
			},
		},
		API: API{
			Operation: "CreateResource",
			Service: APIService{
				Name: "ResourceService",
				UID:  "service123",
			},
		},
		Cloud: Cloud{
			Provider: "AWS",
			Region:   "us-east-1",
			Account: CloudAccount{
				UID: "************",
			},
		},
		SrcEndpoint: NetworkEndpoint{
			IP:       "*************",
			Hostname: "client.example.com",
		},
	}

	err := logger.LogAPIActivity(event)
	require.NoError(t, err)

	output := mockPrinter.GetOutput()
	require.Len(t, output, 1)

	var auditLog OCSFAuditLog
	err = json.Unmarshal([]byte(output[0]), &auditLog)
	require.NoError(t, err)

	// Verify wrapper fields
	assert.Equal(t, "type.eng.augmentcode.com/OCSFAuditLog", auditLog.Type)
	assert.Equal(t, "", auditLog.Tenant.Name)

	// Extract the nested event
	eventBytes, err := json.Marshal(auditLog.OCSFEvent)
	require.NoError(t, err)

	var loggedEvent APIActivityEvent
	err = json.Unmarshal(eventBytes, &loggedEvent)
	require.NoError(t, err)

	// Verify defaults were set
	assert.Equal(t, CategoryApplicationActivity, loggedEvent.CategoryUID)
	assert.Equal(t, ClassAPIActivity, loggedEvent.ClassUID)
	assert.Equal(t, int64(600301), loggedEvent.TypeUID)
	assert.False(t, loggedEvent.Time.IsZero())
	assert.Equal(t, SeverityInformational, loggedEvent.SeverityID)
	assert.Equal(t, "1.5.0", loggedEvent.Metadata.Version)
}

func TestAPIActivityEventBuilder_Basic(t *testing.T) {
	event := NewAPIActivityEventBuilder(ActivityAPICreate).
		WithMessage("API create operation").
		Build()

	assert.Equal(t, ActivityAPICreate, event.ActivityID)
	assert.Equal(t, "API create operation", event.Message)
	assert.Equal(t, CategoryApplicationActivity, event.CategoryUID)
	assert.Equal(t, ClassAPIActivity, event.ClassUID)
	assert.Equal(t, int64(600301), event.TypeUID)
	assert.Equal(t, "1.5.0", event.Metadata.Version)
	assert.Equal(t, "Augment", event.Metadata.Product.Name)
	assert.Equal(t, "Augment Code", event.Metadata.Product.Vendor)
}

func TestAPIActivityEventBuilder_WithAPI(t *testing.T) {
	api := API{
		Operation: "CreateUser",
		Service: APIService{
			Name:    "UserService",
			UID:     "service123",
			Version: "v1",
		},
		Version: "1.0",
	}

	event := NewAPIActivityEventBuilder(ActivityAPICreate).
		WithAPI(api).
		Build()

	assert.Equal(t, "CreateUser", event.API.Operation)
	assert.Equal(t, "UserService", event.API.Service.Name)
	assert.Equal(t, "service123", event.API.Service.UID)
	assert.Equal(t, "v1", event.API.Service.Version)
	assert.Equal(t, "1.0", event.API.Version)
}

func TestAPIActivityEventBuilder_WithCloud(t *testing.T) {
	cloud := Cloud{
		Provider: "AWS",
		Region:   "us-west-2",
		Zone:     "us-west-2a",
		Account: CloudAccount{
			UID:  "************",
			Name: "production",
			Type: "AWS Account",
		},
	}

	event := NewAPIActivityEventBuilder(ActivityAPIRead).
		WithCloud(cloud).
		Build()

	assert.Equal(t, "AWS", event.Cloud.Provider)
	assert.Equal(t, "us-west-2", event.Cloud.Region)
	assert.Equal(t, "us-west-2a", event.Cloud.Zone)
	assert.Equal(t, "************", event.Cloud.Account.UID)
	assert.Equal(t, "production", event.Cloud.Account.Name)
	assert.Equal(t, "AWS Account", event.Cloud.Account.Type)
}

func TestAPIActivityEventBuilder_WithHTTPDetails(t *testing.T) {
	httpRequest := HTTPRequest{
		Method:    "POST",
		UserAgent: "MyApp/1.0",
		URL: &URL{
			Text:     "https://api.example.com/users",
			Hostname: "api.example.com",
			Path:     "/users",
			Scheme:   "https",
		},
	}

	httpResponse := HTTPResponse{
		Code:    201,
		Message: "Created",
		Length:  256,
		Latency: 150,
	}

	event := NewAPIActivityEventBuilder(ActivityAPICreate).
		WithHTTPRequest(httpRequest).
		WithHTTPResponse(httpResponse).
		Build()

	assert.Equal(t, "POST", event.HTTPRequest.Method)
	assert.Equal(t, "MyApp/1.0", event.HTTPRequest.UserAgent)
	assert.Equal(t, "https://api.example.com/users", event.HTTPRequest.URL.Text)
	assert.Equal(t, 201, event.HTTPResponse.Code)
	assert.Equal(t, "Created", event.HTTPResponse.Message)
	assert.Equal(t, 150, event.HTTPResponse.Latency)
}
