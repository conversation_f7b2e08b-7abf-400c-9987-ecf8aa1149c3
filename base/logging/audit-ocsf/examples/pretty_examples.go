package main

import (
	"encoding/json"
	"fmt"
	"time"

	auditocsf "github.com/augmentcode/augment/base/logging/audit-ocsf"
)

// PrettyPrinter formats JSON output nicely
type PrettyPrinter struct{}

func (p PrettyPrinter) Println(args ...any) {
	if len(args) > 0 {
		if jsonStr, ok := args[0].(string); ok {
			var prettyJSON map[string]any
			if err := json.Unmarshal([]byte(jsonStr), &prettyJSON); err == nil {
				if formatted, err := json.MarshalIndent(prettyJSON, "", "  "); err == nil {
					fmt.Println(string(formatted))
					return
				}
			}
		}
	}
	fmt.Println(args...)
}

func main() {
	// Create a logger with pretty printing
	logger := auditocsf.NewOCSFAuditLogger(PrettyPrinter{})

	fmt.Println("=== OCSF Audit Log Examples (Pretty Formatted) ===")
	fmt.Println()

	// Example 1: Simple Authentication Event
	fmt.Println("1. Simple Successful Logon:")
	fmt.Println("   Code: logger.LogUserLogon(\"user123\", \"INTERNAL_IAP\", \"tenant456\", \"User authenticated successfully\")")
	fmt.Println("   Output:")
	logger.LogUserLogon("user123", "INTERNAL_IAP", "tenant456", "User authenticated successfully")
	fmt.Println()

	// Example 2: Complex Authentication with Builder
	fmt.Println("2. Complex Authentication Event with MFA:")
	fmt.Println("   Code: NewSuccessfulLogonEvent().WithMFA(true).WithAuthProtocol().WithDevice()...")
	fmt.Println("   Output:")
	complexEvent := auditocsf.NewSuccessfulLogonEvent("admin123", "INTERNAL_IAP").
		WithMessage("Administrative logon with MFA").
		WithMFA(true).
		WithRemote(false).
		WithAuthProtocol(auditocsf.AuthProtocolKerberos).
		WithLogonType(auditocsf.LogonTypeInteractive).
		WithDevice(auditocsf.Device{
			Name:     "admin-workstation",
			Type:     "Computer",
			Hostname: "admin-ws.example.com",
			IP:       "*********",
			OS: &auditocsf.OS{
				Name:    "Windows",
				Type:    "Windows",
				Version: "10.0.19041",
			},
		}).
		WithSession(auditocsf.Session{
			UID:         "session-admin-123",
			UUID:        "550e8400-e29b-41d4-a716-************",
			Issuer:      "domain-controller",
			CreatedTime: time.Now().UTC(),
			IsRemote:    false,
			IsMFA:       true,
		}).
		Build()

	logger.LogAuthentication(complexEvent)
	fmt.Println()

	// Example 3: API Activity Event
	fmt.Println("3. API Activity - Create Resource (with Cloud Profile):")
	fmt.Println("   Code: NewAPIActivityEventBuilder().WithAPI().WithCloud().WithHTTPRequest()...")
	fmt.Println("   Output:")
	apiEvent := auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPICreate).
		WithMessage("User created a new resource").
		WithAPI(auditocsf.API{
			Operation: "CreateResource",
			Service: auditocsf.APIService{
				Name:    "ResourceService",
				UID:     "service-123",
				Version: "v1.2.0",
			},
			Version: "2.0",
		}).
		WithActor(auditocsf.Actor{
			User: &auditocsf.User{
				UID:    "user123",
				TypeID: 1,
			},
		}).
		WithCloud(auditocsf.Cloud{
			Provider: "AWS",
			Region:   "us-east-1",
			Account: auditocsf.CloudAccount{
				UID:  "************",
				Name: "production",
				Type: "AWS Account",
			},
		}).
		WithSrcEndpoint(auditocsf.NetworkEndpoint{
			IP:       "*************",
			Hostname: "client.example.com",
			Port:     443,
		}).
		WithHTTPRequest(auditocsf.HTTPRequest{
			Method:    "POST",
			UserAgent: "MyApp/1.0",
			URL: &auditocsf.URL{
				Text:     "https://api.example.com/resources",
				Hostname: "api.example.com",
				Path:     "/resources",
				Scheme:   "https",
				Port:     443,
			},
		}).
		WithHTTPResponse(auditocsf.HTTPResponse{
			Code:    201,
			Message: "Created",
			Length:  256,
			Latency: 150,
		}).
		WithSeverity(auditocsf.SeverityInformational).
		Build()

	logger.LogAPIActivity(apiEvent)
	fmt.Println()

	// Example 4: Failed API Activity with Security Control Profile
	fmt.Println("4. Failed API Activity (with Security Control Profile):")
	fmt.Println("   Code: NewAPIActivityEventBuilder().WithDisposition(DispositionBlocked)...")
	fmt.Println("   Output:")
	failedAPIEvent := auditocsf.NewAPIActivityEventBuilder(auditocsf.ActivityAPIDelete).
		WithMessage("Failed to delete resource - insufficient permissions").
		WithAPI(auditocsf.API{
			Operation: "DeleteResource",
			Service: auditocsf.APIService{
				Name: "ResourceService",
				UID:  "service-123",
			},
			Response: &auditocsf.APIResponse{
				Code:    "403",
				Error:   "PERMISSION_DENIED",
				Message: "User does not have permission to delete this resource",
			},
		}).
		WithActor(auditocsf.Actor{
			User: &auditocsf.User{
				Name:   "Jane Smith",
				UID:    "actor789",
				TypeID: 1,
			},
		}).
		WithCloud(auditocsf.Cloud{
			Provider: "Azure",
			Region:   "eastus",
			Account: auditocsf.CloudAccount{
				UID:  "azure-sub-789",
				Name: "development",
				Type: "Azure Subscription",
			},
		}).
		WithSrcEndpoint(auditocsf.NetworkEndpoint{
			IP:       "***********",
			Hostname: "admin-console.example.com",
		}).
		WithHTTPResponse(auditocsf.HTTPResponse{
			Code:    403,
			Message: "Forbidden",
			Length:  128,
			Latency: 75,
		}).
		WithSeverity(auditocsf.SeverityMedium).
		WithStatus(auditocsf.StatusFailure).
		WithAction(auditocsf.ActionDenied).
		WithDisposition(auditocsf.DispositionBlocked).
		Build()

	logger.LogAPIActivity(failedAPIEvent)
	fmt.Println()

	fmt.Println("=== End of Examples ===")
}
