# OCSF Audit Logging Examples

This directory contains examples demonstrating how to use the OCSF audit logging package and how to generate sample log messages for visual inspection and sharing.

## Example Files

### **Pretty Generator** (`pretty_examples.go`)
- **Purpose**: Generate formatted JSON output for sharing and documentation
- **Usage**: `go run pretty_examples.go`
- **Output**: Pretty-printed JSON with code examples
- **Use Case**: Documentation, sharing with stakeholders, visual inspection

## Running Examples

### Pretty Formatted Output
```bash
cd examples
go run pretty_examples.go
```



### Documentation Examples
```bash
cd .. # Go to audit-ocsf directory
go test -v -run "Example"
```

## Example Output Structure

All examples generate OCSF-compliant JSON with this structure:

```json
{
  "@type": "type.eng.augmentcode.com/OCSFAuditLog",
  "tenant": "optional-tenant-id",
  "ocsf_event": {
    "activity_id": 1,
    "category_uid": 3,
    "class_uid": 3002,
    "severity_id": 1,
    "time": "2025-07-23T17:31:26.505753259Z",
    "type_uid": 300201,
    "user": {
      "type": "INTERNAL_IAP",
      "uid": "user123"
    },
    "metadata": {
      "version": "1.5.0",
      "product": {
        "name": "Augment",
        "vendor": "Augment Code",
        "feature": "audit-ocsf"
      },
      "log_provider": "augment-audit-ocsf"
    },
    // ... event-specific fields
  }
}
```

## Event Types Demonstrated

### 1. **Authentication Events** (Class 3002)
- **Simple logon**: Basic user authentication
- **Complex logon**: MFA, device info, session details
- **Failed authentication**: Error handling and status codes

### 2. **Authorize Session Events** (Class 3003)
- **Assign privileges**: User privilege assignment
- **Assign groups**: User group assignment

### 3. **API Activity Events** (Class 6003)
- **Create operations**: Resource creation with HTTP details
- **Failed operations**: Permission denied scenarios
- **Cloud integration**: AWS/Azure cloud context
- **Security controls**: Disposition and alerting

## OCSF Profile Usage

The examples demonstrate fields from multiple OCSF profiles:

### **Base Event Fields**
- `activity_id`, `category_uid`, `class_uid`, `severity_id`
- `time`, `type_uid`, `user`, `metadata`
- `message`, `action_id`, `status_id`

### **Cloud Profile Fields**
- `cloud.provider`, `cloud.region`, `cloud.account`

### **Security Control Profile Fields**
- `disposition_id`, `is_alert`

### **HTTP Profile Fields**
- `http_request`, `http_response`

### **Network Profile Fields**
- `src_endpoint`, `dst_endpoint`

## Use Cases

## Using MockPrinter in Other Projects

The `MockPrinter` is available as a test utility for other projects to create mocked OCSF audit loggers:

### **Basic Usage:**
```go
// In your test files
import auditocsf "github.com/augmentcode/augment/base/logging/audit-ocsf"

func TestMyFunction(t *testing.T) {
    // Method 1: Direct usage
    mockPrinter := &auditocsf.MockPrinter{}
    logger := auditocsf.NewOCSFAuditLogger(mockPrinter)

    // Method 2: Convenience function
    logger, mockPrinter := auditocsf.NewMockOCSFAuditLogger()

    // Use logger in your code under test
    myFunction(logger)

    // Verify the output
    output := mockPrinter.GetOutput()
    assert.Len(t, output, 1)
    assert.Contains(t, output[0], "expected content")
}
```

### **MockPrinter Methods:**
- `GetOutput() []string` - Returns all captured log entries
- `GetLastOutput() string` - Returns the most recent log entry
- `Count() int` - Returns number of captured entries
- `Clear()` - Removes all captured entries

### **Testing Patterns:**
```go
// Test specific event content
lastOutput := mockPrinter.GetLastOutput()
assert.Contains(t, lastOutput, "user123")
assert.Contains(t, lastOutput, "tenant456")

// Test event count
assert.Equal(t, 3, mockPrinter.Count())

// Test with tenant
logger.LogAuthenticationWithTenant(event, "test-tenant")
output := mockPrinter.GetLastOutput()
assert.Contains(t, output, "test-tenant")
```
