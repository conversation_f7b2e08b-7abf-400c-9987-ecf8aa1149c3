package auditocsf

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestAuthenticationEventBuilder_Basic(t *testing.T) {
	builder := NewAuthenticationEventBuilder(ActivityLogon)

	event := builder.
		WithUserUID("user123", "INTERNAL_IAP").
		WithMessage("Test authentication event").
		Build()

	// Verify required fields are set
	assert.Equal(t, ActivityLogon, event.ActivityID)
	assert.Equal(t, CategoryIAM, event.CategoryUID)
	assert.Equal(t, ClassAuthentication, event.ClassUID)
	assert.Equal(t, SeverityInformational, event.SeverityID)
	assert.Equal(t, int64(300201), event.TypeUID) // 3002 * 100 + 1
	assert.Equal(t, "user123", event.User.UID)
	assert.Equal(t, "INTERNAL_IAP", event.User.Type)
	assert.Equal(t, "Test authentication event", event.Message)
	assert.Equal(t, "1.5.0", event.Metadata.Version)
	assert.False(t, event.Time.IsZero())
}

func TestAuthenticationEventBuilder_FluentInterface(t *testing.T) {
	now := time.Now().UTC()

	event := NewAuthenticationEventBuilder(ActivityLogon).
		WithUserUID("user123", "INTERNAL_IAP").
		WithSeverity(SeverityHigh).
		WithStatus(StatusSuccess).
		WithAction(ActionAllowed).
		WithMessage("Fluent interface test").
		WithTime(now).
		WithRemote(true).
		WithMFA(true).
		WithAuthProtocol(AuthProtocolKerberos).
		WithLogonType(LogonTypeInteractive).
		Build()

	assert.Equal(t, ActivityLogon, event.ActivityID)
	assert.Equal(t, "user123", event.User.UID)
	assert.Equal(t, "INTERNAL_IAP", event.User.Type)
	assert.Equal(t, SeverityHigh, event.SeverityID)
	assert.NotNil(t, event.StatusID)
	assert.Equal(t, StatusSuccess, *event.StatusID)
	assert.NotNil(t, event.ActionID)
	assert.Equal(t, ActionAllowed, *event.ActionID)
	assert.Equal(t, "Fluent interface test", event.Message)
	assert.Equal(t, now, event.Time)
	assert.NotNil(t, event.IsRemote)
	assert.True(t, *event.IsRemote)
	assert.NotNil(t, event.IsMFA)
	assert.True(t, *event.IsMFA)
	assert.Equal(t, "Kerberos", event.AuthProtocol)
	assert.NotNil(t, event.AuthProtocolID)
	assert.Equal(t, int(AuthProtocolKerberos), *event.AuthProtocolID)
	assert.Equal(t, "Interactive", event.LogonType)
	assert.NotNil(t, event.LogonTypeID)
	assert.Equal(t, int(LogonTypeInteractive), *event.LogonTypeID)
}

func TestAuthenticationEventBuilder_WithUser(t *testing.T) {
	user := User{
		UID:       "user123",
		Type:      "INTERNAL_IAP",
		Name:      "John Doe",
		Domain:    "example.com",
		EmailAddr: "<EMAIL>",
		FullName:  "John Doe",
	}

	event := NewAuthenticationEventBuilder(ActivityLogon).
		WithUser(user).
		Build()

	assert.Equal(t, user, event.User)
}

func TestAuthenticationEventBuilder_WithUserName(t *testing.T) {
	event := NewAuthenticationEventBuilder(ActivityLogon).
		WithUserName("john.doe", "INTERNAL_IAP").
		Build()

	assert.Equal(t, "john.doe", event.User.Name)
	assert.Equal(t, "INTERNAL_IAP", event.User.Type)
}

func TestAuthenticationEventBuilder_WithStatusDetail(t *testing.T) {
	event := NewAuthenticationEventBuilder(ActivityLogon).
		WithUserUID("user123", "INTERNAL_IAP").
		WithStatusDetail("0x18", "INVALID_CREDENTIALS").
		Build()

	assert.Equal(t, "0x18", event.StatusCode)
	assert.Equal(t, "INVALID_CREDENTIALS", event.StatusDetail)
}

func TestAuthenticationEventBuilder_WithDevice(t *testing.T) {
	device := Device{
		Name:     "workstation-01",
		Type:     "Computer",
		Hostname: "ws01.example.com",
		IP:       "*************",
	}

	event := NewAuthenticationEventBuilder(ActivityLogon).
		WithUserUID("user123", "INTERNAL_IAP").
		WithDevice(device).
		Build()

	assert.NotNil(t, event.Device)
	assert.Equal(t, device, *event.Device)
}

func TestAuthenticationEventBuilder_WithSession(t *testing.T) {
	session := Session{
		UID:         "session-123",
		UUID:        "550e8400-e29b-41d4-a716-************",
		Issuer:      "auth-service",
		CreatedTime: time.Now().UTC(),
		IsRemote:    true,
		IsMFA:       true,
	}

	event := NewAuthenticationEventBuilder(ActivityLogon).
		WithUserUID("user123", "INTERNAL_IAP").
		WithSession(session).
		Build()

	assert.NotNil(t, event.Session)
	assert.Equal(t, session, *event.Session)
}

func TestAuthenticationEventBuilder_WithTimezoneOffset(t *testing.T) {
	event := NewAuthenticationEventBuilder(ActivityLogon).
		WithUserUID("user123", "INTERNAL_IAP").
		WithTimezoneOffset(-480). // PST offset
		Build()

	assert.NotNil(t, event.TimezoneOffset)
	assert.Equal(t, -480, *event.TimezoneOffset)
}

func TestAuthenticationEventBuilder_WithRawData(t *testing.T) {
	rawData := `{"original": "event", "source": "system"}`

	event := NewAuthenticationEventBuilder(ActivityLogon).
		WithUserUID("user123", "INTERNAL_IAP").
		WithRawData(rawData).
		Build()

	assert.Equal(t, rawData, event.RawData)
}

func TestAuthenticationEventBuilder_TypeUIDCalculation(t *testing.T) {
	tests := []struct {
		activity ActivityID
		expected int64
	}{
		{ActivityLogon, 300201},
		{ActivityLogoff, 300202},
		{ActivityAuthenticationTicket, 300203},
		{ActivityServiceTicketRequest, 300204},
		{ActivityServiceTicketRenew, 300205},
		{ActivityPreauth, 300206},
	}

	for _, tt := range tests {
		t.Run(tt.activity.String(), func(t *testing.T) {
			event := NewAuthenticationEventBuilder(tt.activity).
				WithUserUID("user123", "INTERNAL_IAP").
				Build()

			assert.Equal(t, tt.expected, event.TypeUID)
		})
	}
}

// Test convenience builder functions

func TestBuilderChaining(t *testing.T) {
	// Test that we can chain convenience builders with additional methods
	event := NewSuccessfulLogonEvent("user123", "INTERNAL_IAP").
		WithMFA(true).
		WithRemote(true).
		WithAuthProtocol(AuthProtocolSAML).
		WithLogonType(LogonTypeRemoteInteractive).
		WithMessage("SAML SSO logon with MFA").
		Build()

	assert.Equal(t, ActivityLogon, event.ActivityID)
	assert.Equal(t, StatusSuccess, *event.StatusID)
	assert.True(t, *event.IsMFA)
	assert.True(t, *event.IsRemote)
	assert.Equal(t, "SAML", event.AuthProtocol)
	assert.Equal(t, "Remote Interactive", event.LogonType)
	assert.Equal(t, "SAML SSO logon with MFA", event.Message)
}
