"""Rules for creating executable JavaScript files with shebang."""

def js_executable(name, src, out, visibility = None):
    """Creates an executable JavaScript file with shebang.

    Args:
        name: Name of the target.
        src: Input JavaScript file.
        out: Output executable JavaScript file.
        visibility: Visibility of the target.
    """
    native.genrule(
        name = name,
        srcs = [src],
        outs = [out],
        cmd_bash = """
            echo '#!/usr/bin/env node' > "$@"
            cat "$<" >> "$@"
            chmod +x "$@"
        """,
        executable = True,
        visibility = visibility,
    )
