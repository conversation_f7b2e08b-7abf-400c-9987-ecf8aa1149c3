import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";

import { AgentLoop } from "../agent_loop/agent_loop";
import { SessionManager } from "../session-manager";
import { TuiOptions } from "./index";

export interface AppContext {
    sessionManager: SessionManager;
    agentLoop: AgentLoop;
    workspaceRoot: string;
    version: string;
    instruction?: string;
    toolsModel: ToolsModel;
    isLoggedIn: boolean;
    onUpdateAuth: (accessToken: string, tenantURL: string) => Promise<void>;
    onLogout: () => Promise<void>;
}

export let VERSION: string = "0.0.0";

export function loadContext(options: TuiOptions): AppContext {
    VERSION = options.version;

    return {
        sessionManager: options.sessionManager,
        agentLoop: options.agentLoop,
        workspaceRoot: options.workspaceRoot,
        version: options.version,
        instruction: options.instruction,
        toolsModel: options.toolsModel,
        isLoggedIn: options.isLoggedIn,
        onUpdateAuth: options.onUpdateAuth,
        onLogout: options.onLogout,
    };
}
