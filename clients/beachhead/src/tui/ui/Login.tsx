import { Box, Text } from "ink";
import { useApp } from "ink";
import Spinner from "ink-spinner";
import TextInput from "ink-text-input";
import React, { useCallback, useMemo, useState } from "react";

import { openBrowser } from "../../utils/open-browser";
import { useKeypress } from "../hooks/useKeypress";
import { Banner } from "./components/Banner";
import { Notification } from "./components/Notification";
import { createOAuthFlow, handleAuthResponse } from "./utils/auth";

type AuthState =
    | "idle" // Initial state, showing "Login to continue"
    | "awaiting_code" // <PERSON><PERSON><PERSON> opened, waiting for user to paste code
    | "validating" // Processing the authentication code
    | "success" // Authentication successful, ready to continue
    | "error"; // Authentication failed

interface LoginProps {
    onUpdateAuth: (accessToken: string, tenantURL: string) => Promise<void>;
    onSuccess: () => void;
}

interface AuthResponseData {
    code: string;
    state: string;
    tenant_url: string;
}

const validateAuthResponse = (
    value: string
): { isValid: boolean; error: string | null; data?: AuthResponseData } => {
    // Try to parse JSON
    let parsedValue;
    try {
        parsedValue = JSON.parse(value.trim());
    } catch (e) {
        return {
            isValid: false,
            error: "Invalid JSON format. Please paste the complete authentication response.",
        };
    }

    // Validate required fields
    if (!parsedValue.code || typeof parsedValue.code !== "string") {
        return {
            isValid: false,
            error: "Missing or invalid 'code' field in authentication response.",
        };
    }
    if (!parsedValue.state || typeof parsedValue.state !== "string") {
        return {
            isValid: false,
            error: "Missing or invalid 'state' field in authentication response.",
        };
    }
    if (!parsedValue.tenant_url || typeof parsedValue.tenant_url !== "string") {
        return {
            isValid: false,
            error: "Missing or invalid 'tenant_url' field in authentication response.",
        };
    }

    return { isValid: true, error: null, data: parsedValue };
};

export const Login: React.FC<LoginProps> = ({ onUpdateAuth, onSuccess }) => {
    const [authState, setAuthState] = useState<AuthState>("idle");
    const [authInput, setAuthInput] = useState("");
    const [regenerateOauth, setRegenerateOauth] = useState(false);
    const [notification, setNotification] = useState<{
        message: string;
        type: "error" | "success" | "info";
    } | null>(null);
    const { exit } = useApp();
    const { loginUrl, oauthFlow } = useMemo(() => createOAuthFlow(), [regenerateOauth]);

    const handleInputChange = useCallback((value: string) => {
        setAuthInput(value);
        // Clear notification when input is empty
        if (!value.trim()) {
            setNotification(null);
        }
    }, []);

    const handleAuthSubmit = async (value: string) => {
        // Don't process empty input
        if (!value.trim()) {
            return;
        }

        setNotification(null);
        const validation = validateAuthResponse(value);

        if (!validation.isValid && validation.error) {
            setNotification({ message: validation.error, type: "error" });
            return;
        }

        setAuthState("validating");
        setNotification(null);

        try {
            const accessToken = await handleAuthResponse(oauthFlow, value);

            // Extract tenant URL from validation data and call updateAuth directly
            if (validation.data) {
                const { tenant_url } = validation.data;
                await onUpdateAuth(accessToken, tenant_url);
            }

            setAuthInput("");
            setAuthState("success");
        } catch (error) {
            setAuthState("error");
            setNotification({
                message:
                    error instanceof Error
                        ? `Authentication failed: ${error.message}`
                        : "Authentication failed",
                type: "error",
            });
        }
    };

    useKeypress({
        onKeypress: (input, key) => {
            // Handle Ctrl+C for exit
            if (key.ctrl && input === "c") {
                exit();
                return;
            }

            if (key.return && authState === "idle") {
                setAuthState("awaiting_code");
                openBrowser(loginUrl);
            } else if (key.return && authState === "success") {
                onSuccess();
            } else if (key.escape && authState === "awaiting_code") {
                setAuthInput("");
                setNotification(null);
            } else if (key.return && authState === "error") {
                // Recreate OAuth flow and go back to idle state
                setRegenerateOauth(!regenerateOauth);
                setAuthState("idle");
                setNotification(null);
                setAuthInput("");
            }
        },
    });

    return (
        <Box flexDirection="column" paddingRight={1} paddingLeft={1} paddingTop={1}>
            <Banner />

            <Box>
                {authState === "idle" && (
                    <Box flexDirection="column">
                        <Box marginBottom={2}>
                            <Box paddingLeft={2}>
                                <Text color="magenta">
                                    Don't have an account? Sign up for a free trial when you login
                                </Text>
                            </Box>
                        </Box>
                        <Box flexDirection="column" gap={1}>
                            <Text>→ Login to continue</Text>
                            <Box paddingLeft={2}>
                                <Text color="grey">Press return to open your browser</Text>
                            </Box>
                        </Box>
                    </Box>
                )}

                {authState === "awaiting_code" && (
                    <Box flexDirection="column">
                        <Box flexDirection="column" marginBottom={1}>
                            <Text dimColor>{loginUrl}</Text>
                        </Box>

                        <Box flexDirection="column" paddingLeft={2} marginBottom={1}>
                            <Text>1. Visit the url above to continue to sign in</Text>
                            <Text>2. Follow the instructions in the browser</Text>
                            <Text>3. Paste the authentication JSON below</Text>
                        </Box>

                        <Box marginLeft={1}>
                            <Notification notification={notification} />
                        </Box>
                        <Box flexDirection="column" paddingLeft={2}>
                            <Text>Paste your login code:</Text>
                            <TextInput
                                value={authInput}
                                onChange={handleInputChange}
                                onSubmit={handleAuthSubmit}
                                placeholder="Paste JSON here..."
                            />
                        </Box>
                    </Box>
                )}

                {authState === "validating" && (
                    <Box flexDirection="column" paddingLeft={2}>
                        <Box flexDirection="column" gap={1}>
                            <Text>
                                <Spinner type="dots3" /> Signing in
                            </Text>
                            <Box paddingLeft={2}>
                                <Text color="grey">Please wait, or press ctrl+c to exit</Text>
                            </Box>
                        </Box>
                    </Box>
                )}

                {authState === "success" && (
                    <Box flexDirection="column">
                        <Box marginBottom={2}>
                            <Box borderStyle="round" borderColor="green" paddingX={1}>
                                <Text>Login successful</Text>
                            </Box>
                        </Box>
                        <Box flexDirection="column" gap={1}>
                            <Text>→ Continue</Text>
                            <Box paddingLeft={2}>
                                <Text color="grey">Press return to continue</Text>
                            </Box>
                        </Box>
                    </Box>
                )}

                {authState === "error" && (
                    <Box flexDirection="column" gap={1}>
                        <Box marginLeft={1}>
                            <Notification notification={notification} />
                        </Box>
                        <Box paddingLeft={2}>
                            <Text dimColor>Press return to try again</Text>
                        </Box>
                    </Box>
                )}
            </Box>
        </Box>
    );
};
