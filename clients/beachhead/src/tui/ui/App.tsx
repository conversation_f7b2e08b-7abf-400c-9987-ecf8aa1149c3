import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import React, { useState } from "react";

import { AgentLoop } from "../../agent_loop/agent_loop";
import { SessionManager } from "../../session-manager";
import { Onboarding } from "./components/Onboarding";
import { Login } from "./Login";
import { Repl } from "./Repl";

interface AppProps {
    workspaceRoot: string;
    agentLoop: AgentLoop;
    sessionManager: SessionManager;
    instruction?: string;
    toolsModel: ToolsModel;
    isLoggedIn: boolean;
    onUpdateAuth: (accessToken: string, tenantURL: string) => Promise<void>;
    onLogout: () => Promise<void>;
}

export const App: React.FC<AppProps> = ({
    workspaceRoot,
    agentLoop,
    sessionManager,
    instruction,
    toolsModel,
    isLoggedIn,
    onUpdateAuth,
    onLogout,
}) => {
    const [isAuthenticated, setIsAuthenticated] = useState(isLoggedIn);
    const [showOnboarding, setShowOnboarding] = useState(false);

    if (!isAuthenticated) {
        return (
            <Login
                onUpdateAuth={onUpdateAuth}
                onSuccess={() => {
                    setShowOnboarding(true);
                    setIsAuthenticated(true);
                }}
            />
        );
    }

    if (showOnboarding) {
        return <Onboarding onComplete={() => setShowOnboarding(false)} />;
    }

    return (
        <Repl
            workspaceRoot={workspaceRoot}
            sessionManager={sessionManager}
            instruction={instruction}
            agentLoop={agentLoop}
            toolsModel={toolsModel}
            onLogout={onLogout}
        />
    );
};
