## Segment Tips

For naming events and properties that get ingested into Segument, follow best practices in: https://segment.com/docs/protocols/tracking-plan/best-practices/

- Pick a casing convention. Segment recommends Title Case for event names and snake_case for property names. Make sure you pick a casing standard and enforce it across your events and properties.
- Pick an event name structure. As you may have noticed from the Segment specs, Segment uses the Object (Blog Post) + Action (Read) framework for event names. Pick a convention and stick to it.
- Don’t create event names dynamically. Avoid creating events that pull a dynamic value into the event name (like User Signed Up (11-01-2019)).
- Don’t create events to track properties. Avoid adding values to event names that could be a property. Instead, add values a property (like "blog_post_title":"Best Tracking Plans Ever").
- Don’t create property keys dynamically. Avoid creating property names like "feature_1":"true","feature_2":"false", as these are ambiguous and difficult to analyze.

# Analytics Events Documentation

This document provides comprehensive documentation for the analytics event tracking system in the sidecar, including typing information and instructions for adding new events.

## Overview

The analytics system provides type-safe event tracking with proper TypeScript interfaces. It uses Segment Analytics as the underlying tracking service and includes PII filtering for privacy protection.

## Architecture

The analytics system consists of several key components:

- **Event Names**: Defined as constants in `ANALYTICS_EVENTS`
- **Event Properties**: Strongly typed interfaces for each event
- **Type Mapping**: Maps event names to their corresponding property types
- **Type-Safe Tracking**: Ensures events are tracked with correct property types

## Event Structure

### Event Names

All event names are defined in the `ANALYTICS_EVENTS` constant object:

```typescript
export const ANALYTICS_EVENTS = {
  THREAD_CREATION_ATTEMPTED: "thread_creation_attempted",
  SEND_ACTION_TRIGGERED: "send_action_triggered",
  // ... more events
} as const;
```

### Event Properties

Each event has a corresponding TypeScript interface that defines its properties:

```typescript
export interface ThreadCreationAttemptedEventProperties
  extends BaseChatSourceEventProperties {}

export interface SendActionTriggeredEventProperties
  extends BaseChatSourceEventProperties {
  sendMode?: "send" | "addTask";
}
```

### Base Interfaces

Common properties are defined in base interfaces to promote reusability:

```typescript
interface BaseChatSourceEventProperties {
  chatMode: ChatModeType;
  source: "keybinding" | "button";
  agentExecutionMode?: "auto" | "manual";
}
```

## Type Safety

The system provides compile-time type safety through:

1. **Event Name Type**: `AnalyticsEventName` ensures only valid event names are used
2. **Property Union Type**: `AnalyticsEventProperties` includes all possible property types
3. **Event-to-Properties Mapping**: `EventNameToPropertiesMap` maps each event to its properties
4. **Type-Safe Tracking Function**: `trackEventWithTypes` enforces correct property types

## Adding a New Event

Follow these steps to add a new analytics event:

### Step 1: Add Event Name Constant

Add your new event to the `ANALYTICS_EVENTS` object:

```typescript
export const ANALYTICS_EVENTS = {
  // ... existing events
  YOUR_NEW_EVENT: "your_new_event",
} as const;
```

**Naming Convention**: Use `SCREAMING_SNAKE_CASE` for the constant and `snake_case` for the string value.

### Step 2: Define Event Properties Interface

Create a TypeScript interface for your event's properties:

```typescript
export interface YourNewEventProperties {
  // Required properties
  userId: string;
  action: "create" | "update" | "delete";

  // Optional properties
  metadata?: Record<string, any>;
  timestamp?: number;
}
```

**Guidelines**:

- Use descriptive property names
- Prefer union types over strings for enumerated values
- Mark optional properties with `?`
- Extend base interfaces when appropriate (e.g., `BaseChatSourceEventProperties`)
- Avoid including PII (personally identifiable information)

### Step 3: Add to Property Union Type

Add your new interface to the `AnalyticsEventProperties` union type:

```typescript
export type AnalyticsEventProperties =
  | ThreadCreationAttemptedEventProperties
  | SendActionTriggeredEventProperties
  // ... other existing types
  | YourNewEventProperties; // Add this line
```

### Step 4: Add to Event-Properties Mapping

Add your event to the `EventNameToPropertiesMap`:

```typescript
export type EventNameToPropertiesMap = {
  [ANALYTICS_EVENTS.THREAD_CREATION_ATTEMPTED]: ThreadCreationAttemptedEventProperties;
  // ... other existing mappings
  [ANALYTICS_EVENTS.YOUR_NEW_EVENT]: YourNewEventProperties; // Add this line
};
```

### Step 5: Track Your Event

Use the type-safe tracking function to track your event:

```typescript
import { trackEventWithTypes, ANALYTICS_EVENTS } from "./analytics";

// Type-safe tracking - TypeScript will enforce correct properties
trackEventWithTypes(ANALYTICS_EVENTS.YOUR_NEW_EVENT, {
  userId: "user123",
  action: "create",
  metadata: { source: "button" },
});
```

## Best Practices

### Property Design

1. **Be Specific**: Use specific property names and types rather than generic ones
2. **Avoid PII**: Never include personally identifiable information
3. **Use Enums**: Prefer union types over free-form strings for categorical data
4. **Keep It Flat**: Avoid deeply nested objects; prefer flat structures
5. **Include Context**: Add relevant context properties (chatMode, source, etc.)

### Event Naming

1. **Use Past Tense**: Event names should describe what happened (e.g., "button_clicked", not "click_button")
2. **Be Descriptive**: Names should clearly indicate what action occurred
3. **Group Related Events**: Use consistent prefixes for related events

### Performance Considerations

1. **Batch Events**: Consider batching related events when possible
2. **Limit Property Size**: Keep property objects reasonably small
3. **Use Optional Properties**: Make properties optional when they might not always be available

## Common Patterns

### Chat-Related Events

Most chat-related events extend `BaseChatSourceEventProperties`:

```typescript
export interface YourChatEventProperties extends BaseChatSourceEventProperties {
  // Additional properties specific to your event
  customProperty?: string;
}
```

### Performance Metrics

Performance events typically include timing information:

```typescript
export interface YourPerformanceEventProperties {
  requestId: string;
  durationMs: number;
  chatMode: ChatModeType;
  // Other relevant context
}
```

### Error Events

Error events should include error context without exposing sensitive information:

```typescript
export interface YourErrorEventProperties {
  errorType: string;
  errorMessagePreview?: string; // First 100 chars only
  requestId?: string;
  // Context about where the error occurred
}
```

## Testing

When adding new events, ensure you:

1. **Test Type Safety**: Verify TypeScript compilation catches type errors
2. **Test Event Tracking**: Confirm events are properly sent to analytics
3. **Test Property Validation**: Ensure required properties are enforced
4. **Test PII Filtering**: Verify no PII is accidentally included

## Related Files

- `analytics.ts` - Main analytics interface and type definitions
- `segment-analytics.ts` - Segment Analytics implementation
- `analytics-manager.ts` - VS Code extension analytics manager
- `types.ts` - Additional metrics and event types
