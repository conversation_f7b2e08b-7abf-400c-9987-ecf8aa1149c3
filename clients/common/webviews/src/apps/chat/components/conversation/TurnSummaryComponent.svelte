<script lang="ts">
  import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import {
    countUniquePaths,
    flattenGroupIntoNodes,
    isMemoryNode,
    isStrReplaceToolNode,
    isViewToolNode,
  } from "../../types/chat-message";
  import { getChatModel } from "../../chat-context";
  import { getGlobalMemoryModel } from "../../models/memory-model";
  import MemoryModalContent from "../chat-thread-body/MemoryModalContent.svelte";
  import FullSizeOverlayAugment from "$common-webviews/src/design-system/components/FullSizeOverlayAugment/FullSizeOverlayAugment.svelte";
  import { type GroupedChatItem } from "../../utils/message-list-context";
  import TurnSummary from "../turn-summary/TurnSummary.svelte";
  import { onMount } from "svelte";

  const chatModel = getChatModel();
  export let group: GroupedChatItem | undefined;

  // Get the global memory model instance
  const memoryModel = getGlobalMemoryModel();
  const { pendingMemoriesCount } = memoryModel;
  onMount(() => {
    memoryModel.refreshMemories();
  });

  $: allNodes = flattenGroupIntoNodes(group);
  $: strReplaceNodes = allNodes.filter(isStrReplaceToolNode);

  $: conversationMemoryCount = allNodes.filter(isMemoryNode).length;
  $: conversationToolUseCount = allNodes.filter(
    (node) => node.type === ChatResultNodeType.TOOL_USE,
  ).length;
  $: conversationStrReplaceFiles = countUniquePaths(strReplaceNodes);
  $: conversationViewFiles = countUniquePaths(allNodes.filter(isViewToolNode));

  // Hide the component if there are no memories to show
  $: shouldShowComponent = conversationMemoryCount > 0 || $pendingMemoriesCount > 0;

  $: entries = [
    {
      label: "Pending Memories",
      value: $pendingMemoriesCount,
      icon: "inventory_2",
      callback: () => navigateToMemoryTab(),
    },
    {
      label: "Memories Created",
      value: conversationMemoryCount,
      icon: "inventory",
    },
    { label: "Files Changed", value: conversationStrReplaceFiles, icon: "difference" },
    { label: "Files Examined", value: conversationViewFiles, icon: "document_search" },
    { label: "Tools Used", value: conversationToolUseCount, icon: "design_services" },
    // TODO: add in memories retrieved in when parsable
  ];

  let memoryModal: FullSizeOverlayAugment;
  function navigateToMemoryTab() {
    memoryModal.showModal();
  }
</script>

{#if shouldShowComponent}
  <FullSizeOverlayAugment bind:this={memoryModal}>
    <MemoryModalContent {chatModel} />
  </FullSizeOverlayAugment>

  <TurnSummary title="Turn Summary" {entries} isCardOpen={false} />
{/if}
