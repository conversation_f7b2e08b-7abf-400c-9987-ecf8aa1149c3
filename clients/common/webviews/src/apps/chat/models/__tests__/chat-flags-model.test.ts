/**
 * Unit tests for ChatFlagsModel class, specifically testing the new enableGroupedTools feature flag.
 */
import { vi, describe, test, expect } from "vitest";
import { ChatFlagsModel } from "../chat-flags-model";
import { type IChatFlags } from "../types";

describe("ChatFlagsModel - enableGroupedTools", () => {
  test("should have enableGroupedTools default to false", () => {
    const model = new ChatFlagsModel();
    expect(model.enableGroupedTools).toBe(false);
  });

  test("should update enableGroupedTools when provided in constructor", () => {
    const initialFlags: Partial<IChatFlags> = {
      enableGroupedTools: true,
    };
    const model = new ChatFlagsModel(initialFlags);
    expect(model.enableGroupedTools).toBe(true);
  });

  test("should update enableGroupedTools via update method", () => {
    const model = new ChatFlagsModel();
    expect(model.enableGroupedTools).toBe(false);

    model.update({ enableGroupedTools: true });
    expect(model.enableGroupedTools).toBe(true);

    model.update({ enableGroupedTools: false });
    expect(model.enableGroupedTools).toBe(false);
  });

  test("should maintain enableGroupedTools value when updating other flags", () => {
    const model = new ChatFlagsModel();
    model.update({ enableGroupedTools: true });
    expect(model.enableGroupedTools).toBe(true);

    // Update other flags, enableGroupedTools should remain true
    model.update({ enableDebugFeatures: true });
    expect(model.enableGroupedTools).toBe(true);
    expect(model.enableErgonomicsUpdate).toBe(true); // This should be true due to enableDebugFeatures
  });

  test("should be independent from enableErgonomicsUpdate", () => {
    const model = new ChatFlagsModel();

    // Test that enableGroupedTools can be true while enableErgonomicsUpdate is false
    model.update({ enableGroupedTools: true, enableDebugFeatures: false });
    expect(model.enableGroupedTools).toBe(true);
    expect(model.enableErgonomicsUpdate).toBe(false);

    // Test that enableErgonomicsUpdate can be true while enableGroupedTools is false
    model.update({ enableGroupedTools: false, enableDebugFeatures: true });
    expect(model.enableGroupedTools).toBe(false);
    expect(model.enableErgonomicsUpdate).toBe(true);

    // Test that both can be true
    model.update({ enableGroupedTools: true, enableDebugFeatures: true });
    expect(model.enableGroupedTools).toBe(true);
    expect(model.enableErgonomicsUpdate).toBe(true);
  });

  test("should notify subscribers when enableGroupedTools changes", () => {
    const model = new ChatFlagsModel();
    const mockSubscriber = vi.fn();

    model.subscribe(mockSubscriber);
    expect(mockSubscriber).toHaveBeenCalledTimes(1); // Initial call

    model.update({ enableGroupedTools: true });
    expect(mockSubscriber).toHaveBeenCalledTimes(2);
    expect(mockSubscriber).toHaveBeenLastCalledWith(model);
  });
});
