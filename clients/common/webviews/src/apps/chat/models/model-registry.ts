/**
 * Model registry for managing available models in the chat interface.
 * This implementation uses Svelte's store pattern for seamless integration
 * with Svelte's reactivity system.
 */

import { writable, type Readable, derived } from "svelte/store";
import type { ChatModel } from "./chat-model";

/**
 * Represents a model that can be selected in the model picker.
 */
export interface Model {
  /**
   * The unique identifier for the model, or null for the default model.
   */
  id: string | null;

  /**
   * The display name of the model.
   */
  name: string;
}

export const DEFAULT_NAME = "Default";
/**
 * The default model used when no specific model is selected.
 */
export const DEFAULT_MODEL: Model = {
  id: null,
  name: DEFAULT_NAME,
};

/**
 * Registry for managing available models using <PERSON><PERSON><PERSON>'s store pattern.
 * This allows components to use the $modelRegistry reactive syntax
 * and automatically re-render when models change.
 */
export class ModelRegistry implements Readable<Model[]> {
  private _store = writable<Model[]>([DEFAULT_MODEL]);
  private _chatModel: ChatModel;

  constructor(chatModel: ChatModel) {
    this._chatModel = chatModel;
  }

  /**
   * Get the default model store based on chatModel flags.
   */
  get defaultModel(): Readable<Model> {
    return derived([this._store, this._chatModel], ([$models, $chatModel]) => {
      const defaultId = $chatModel.flags.agentChatModel;
      return $models.find((model) => model.id === defaultId) ?? DEFAULT_MODEL;
    });
  }

  /**
   * Get the models store in a user formattable way based on chatModel flags.
   * Returns a formatted list that filters out the default model if present in modelRegistry
   * and adds it as first entry:
   *  as { id: null, name: <MODEL_NAME> } if it exists
   *  and { id: null, name: "Default" } if not.
   */
  get formattedModels(): Readable<Model[]> {
    return derived([this._store, this.defaultModel], ([$models, $defaultModel]) => {
      // Filter out null IDs but keep all valid models including the default
      let validModels = $models.filter((m) => m.id !== $defaultModel.id);
      validModels = [{ name: $defaultModel.name, id: null }, ...validModels];

      return validModels;
    });
  }

  /**
   * Get the selected model with fallback to DEFAULT_MODEL
   */
  get selectedModel(): Readable<Model> {
    return derived([this.formattedModels, this._chatModel], ([$models, $chatModel]) => {
      const selectedId = $chatModel.currentConversationModel?.selectedModelId;
      return $models.find((m) => m.id === selectedId) ?? DEFAULT_MODEL;
    });
  }

  /*
   * Get a reactive store for a specific model by its ID.
   * This returns a derived store that automatically updates when the model registry changes.
   * @param id The model ID to look up
   * @returns A readable store containing the matching model, or the default model if not found
   */
  public getModelStore(id: string | null): Readable<Model> {
    return derived(
      this._store,
      ($models) => $models.find((model) => model.id === id) ?? DEFAULT_MODEL,
    );
  }

  /**
   * Subscribe to model changes. This implements Svelte's store contract,
   * allowing components to use $modelRegistry reactive syntax.
   */
  subscribe = this._store.subscribe;

  /**
   * Register models to be available in the model picker.
   * This will trigger reactivity in any components subscribed to this store.
   * @param models The models to register
   */
  public registerModels(models: Model[]) {
    this._store.set(models);
  }
}
