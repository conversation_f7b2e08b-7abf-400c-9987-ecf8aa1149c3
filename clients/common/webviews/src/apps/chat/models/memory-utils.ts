import {
  type ChatResultN<PERSON>,
  ChatResultNodeType,
  type MemoriesInfo,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  AgentSessionEventName,
  ClassifyAndDistillData,
  ClassifyAndDistillDebugFlag,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import {
  MemoryWebViewMessageType,
  type MemoryCreatedMessage,
  type MemoryEntry,
  type MemoryCreationMetadata,
  MEMORY_EXPERIMENTS,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/memory-messages";
import { get } from "svelte/store";
import { marked } from "marked";
import { type ExchangeWithStatus } from "../types/chat-message";
import type { ConversationModel } from "./conversation-model";
import { isAgentConversation } from "./types";
import {
  getCurrentAgenticTurnExchanges,
  getLastUserExchange,
  isUserMessage,
} from "./agent-conversation-utils";
import { getChatModel } from "../chat-context";
import type { ChatModel } from "./chat-model";

export type AgentMemoryData = {
  memoriesRequestId: string;
  memory: string;
  isFlushed: boolean;
};

/**
 * Utility class for handling memory-related operations in agent conversations
 */
export class MemoryUtils {
  /**
   * Sends a silent exchange to classify and distill memories without context
   */
  static async classifyAndDistillMemories(
    conversationModel: ConversationModel,
    exchange: ExchangeWithStatus,
  ): Promise<void> {
    if (!isAgentConversation(conversationModel)) return;
    // Don't classify and distill memories for non-user messages
    if (!isUserMessage(exchange)) return;
    // No pending user message
    if (!exchange?.request_message) return;

    const trace = ClassifyAndDistillData.create();
    trace.setFlag(ClassifyAndDistillDebugFlag.start);

    try {
      await this._classifyAndDistillMemories(conversationModel, exchange, trace);
    } catch (e) {
      trace.setFlag(ClassifyAndDistillDebugFlag.exceptionThrown);
      console.error("Failed to classify and distill memories", e);

      // Report error to VSCode logs and backend for tracking
      conversationModel.extensionClient.reportError({
        originalRequestId: null,
        sanitizedMessage: "Memory classification and distillation failed",
        stackTrace: e instanceof Error ? e.stack || "" : "",
        diagnostics: [
          {
            key: "error_message",
            value: e instanceof Error ? e.message : String(e),
          },
          {
            key: "conversation_id",
            value: conversationModel.id,
          },
          {
            key: "exchange_request_message_length",
            value: String(exchange?.request_message?.length || 0),
          },
        ],
      });
    } finally {
      trace.setFlag(ClassifyAndDistillDebugFlag.end);
      conversationModel.extensionClient.reportAgentSessionEvent({
        eventName: AgentSessionEventName.classifyAndDistill,
        conversationId: conversationModel.id,
        eventData: {
          classifyAndDistillData: trace,
        },
      });
    }
  }

  public static getMemoryVersionFromFlags(
    chatModel?:
      | ChatModel
      | { flags: { memoriesParams?: { [key: string]: string | number | boolean } } },
  ): string | undefined {
    const model = chatModel || getChatModel();
    const memoriesParams = model?.flags?.memoriesParams || {};
    return memoriesParams.memory_retrieval_version as string | undefined;
  }

  /**
   * Parses the response from the classify and distill silent exchange
   * Extracts the content if it's worth remembering, otherwise returns undefined
   */
  private static async _parseClassifyAndDistillResponse(
    conversationModel: ConversationModel,
    exchange: ExchangeWithStatus,
    trace: ClassifyAndDistillData,
    memoriesInfo: MemoriesInfo,
  ): Promise<{ requestId?: string; content?: string }> {
    // Store this so we can check if conversation has changed
    const currConversationId = get(conversationModel).id;

    trace.setFlag(ClassifyAndDistillDebugFlag.startSendSilentExchange);

    // For success-based experiments, include both user message and agent response
    let messageToAnalyze = exchange.request_message;
    if (
      memoriesInfo.promptKey === "classify_and_distill_success_prompt" &&
      exchange.response_text
    ) {
      // Format as a conversation for success detection
      messageToAnalyze = `User: ${exchange.request_message}\n\nAssistant: ${exchange.response_text}`;
    }

    const { responseText: rawResponse, requestId } = await conversationModel.sendSilentExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      // Message is wrapped into a special prompt on extension side
      model_id: conversationModel.selectedModelId ?? undefined,
      request_message: messageToAnalyze,
      disableRetrieval: true,
      disableSelectedCodeDetails: true,
      memoriesInfo,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
    trace.setStringStats(ClassifyAndDistillDebugFlag.sendSilentExchangeResponseStats, rawResponse);
    if (requestId) {
      trace.setRequestId(ClassifyAndDistillDebugFlag.sendSilentExchangeRequestId, requestId);
    } else {
      trace.setFlag(ClassifyAndDistillDebugFlag.noRequestId);
    }
    // Log response statistics
    // If conversation has changed, do nothing
    if (get(conversationModel).id !== currConversationId) {
      trace.setFlag(ClassifyAndDistillDebugFlag.conversationChanged);
      return { requestId };
    }
    let json: { explanation: string; worthRemembering: boolean; content: string };
    try {
      let parsableResponse = rawResponse;
      try {
        const tokens = marked.lexer(rawResponse);
        if (tokens.length === 1 && tokens[0].type === "code" && tokens[0].text) {
          parsableResponse = tokens[0].text;
        }
        // If it's not a single code block, parsableResponse remains rawResponse
      } catch (lexError) {
        // If lexing fails, log it but proceed with rawResponse,
        // as it might be plain JSON that confused the lexer.
        console.warn(
          "Markdown lexing failed during response parsing, attempting to parse as raw string:",
          lexError,
        );
      }
      json = JSON.parse(parsableResponse);
    } catch (e) {
      trace.setFlag(ClassifyAndDistillDebugFlag.invalidResponse);
      throw new Error(`Invalid response from classify and distill: ${e}`);
    }

    if (
      typeof json.explanation !== "string" ||
      typeof json.content !== "string" ||
      typeof json.worthRemembering !== "boolean"
    ) {
      trace.setFlag(ClassifyAndDistillDebugFlag.invalidResponse);
      throw new Error(`Invalid response from classify and distill JSON: ${json}`);
    }
    trace.setStringStats(ClassifyAndDistillDebugFlag.explanationStats, json.explanation);
    trace.setStringStats(ClassifyAndDistillDebugFlag.contentStats, json.content);
    trace.setFlag(ClassifyAndDistillDebugFlag.worthRemembering, json.worthRemembering);

    return { requestId, content: json.worthRemembering ? json.content : undefined };
  }

  private static async _classifyAndDistillMemories(
    conversationModel: ConversationModel,
    exchange: ExchangeWithStatus,
    trace: ClassifyAndDistillData,
  ): Promise<void> {
    // Used to link the chain of classifyAndDistill => flush memories => remember-tool-call
    let defaultMemoriesRequestId = crypto.randomUUID();
    let response: string | undefined;
    // If feature flag is enabled, we want to submit multiple memories requests in parallel and write them to the memory-database.ts
    if (conversationModel.flags.enableMemoryRetrieval) {
      const memoryPromises = Object.entries(MEMORY_EXPERIMENTS).map(
        async ([version, experimentConfig]) => {
          const experimentMemoriesRequestId = crypto.randomUUID();
          // Create memories info with experiment-specific configuration
          const memoriesInfo: MemoriesInfo = {
            isClassifyAndDistill: experimentConfig.isClassifyAndDistill,
            promptKey: experimentConfig.promptKey,
          };
          return this._parseClassifyAndDistillResponse(
            conversationModel,
            exchange,
            trace,
            memoriesInfo,
          ).then(({ content: memoryString, requestId }) => ({
            memoriesRequestId: experimentMemoriesRequestId,
            requestId,
            version,
            memoryString,
          }));
        },
      );

      const allMemoryPromises = [...memoryPromises];

      const responses = await Promise.all(allMemoryPromises);
      // Send memory creation message to sidecar for processing for each response
      responses.forEach(({ requestId, memoriesRequestId, version, memoryString }) => {
        if (memoryString && requestId) {
          this._sendMemoryCreationMessage(
            conversationModel,
            memoryString,
            memoriesRequestId,
            requestId,
            version,
          );
          if (version === this.getMemoryVersionFromFlags(conversationModel)) {
            defaultMemoriesRequestId = memoriesRequestId;
            response = memoryString;
          }
        }
      });
    } else {
      trace.setRequestId(ClassifyAndDistillDebugFlag.memoriesRequestId, defaultMemoriesRequestId);

      // Memory request that gets persisted to chat history
      const defaultMemoriesInfoParams: MemoriesInfo = {
        isClassifyAndDistill: true,
      };
      const { content } = await this._parseClassifyAndDistillResponse(
        conversationModel,
        exchange,
        trace,
        defaultMemoriesInfoParams,
      );
      response = content;
    }

    if (response) {
      /* TODO: adding the memory node is a hack right now to make it show up
       * Move this to a separate process and dynamically insert it into UI
       */
      this.updateUserExchangeWithMemory(
        conversationModel,
        response,
        defaultMemoriesRequestId,
        trace,
      );
    }
  }

  /**
   * Updates the last user exchange with the given memory string
   */
  static updateUserExchangeWithMemory(
    conversationModel: ConversationModel,
    memoryString: string,
    memoriesRequestId: string,
    trace: ClassifyAndDistillData,
  ): void {
    const content = JSON.stringify({
      memoriesRequestId,
      memory: memoryString,
    });

    const lastUserExchange = getLastUserExchange(conversationModel);
    if (lastUserExchange?.request_id) {
      trace.setRequestId(
        ClassifyAndDistillDebugFlag.lastUserExchangeRequestId,
        lastUserExchange.request_id,
      );

      const memoryNode = {
        id: 0,
        type: ChatResultNodeType.AGENT_MEMORY,
        content,
        /* eslint-disable @typescript-eslint/naming-convention */
        agent_memory: {
          content,
          isFlushed: false,
        },
      };

      conversationModel.updateChatItem(lastUserExchange.request_id, {
        ...lastUserExchange,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        structured_output_nodes: [...(lastUserExchange.structured_output_nodes ?? []), memoryNode],
      });
    } else {
      trace.setFlag(ClassifyAndDistillDebugFlag.noLastUserExchangeRequestId);
    }
  }

  /**
   * Sends a memory creation message to the sidecar for processing
   * This triggers the new memory listener system
   */
  private static async _sendMemoryCreationMessage(
    conversationModel: ConversationModel,
    memoryString: string,
    memoriesRequestId: string,
    requestId: string,
    version: string,
  ) {
    try {
      // Create the memory entry with schema-compliant structure
      const memoryEntry: MemoryEntry = {
        id: `${conversationModel.id}_${requestId}`,
        content: memoryString,
        version: version,
      };

      // Create metadata for the memory creation event
      const metadata: MemoryCreationMetadata = {
        conversationId: conversationModel.id,
        requestId: requestId,
        memoriesRequestId: memoriesRequestId,
        timestamp: Date.now(),
      };

      // Create the memory creation message
      const memoryMessage: MemoryCreatedMessage = {
        type: MemoryWebViewMessageType.memoryCreated,
        data: {
          memory: memoryEntry,
          metadata: metadata,
        },
      };

      // Send the message to the sidecar using the extension client
      await conversationModel.extensionClient.sendMemoryCreated(memoryMessage);
    } catch (error) {
      console.warn("Error creating memory creation message:", error);
    }
  }

  /**
   * Memories data is stored somewhat hackily as a JSON string in the content of the node.
   * Parses the content of an agent memory node into a structured object.
   */
  static parseMemoryNode(node: ChatResultNode): AgentMemoryData | undefined {
    try {
      const { memoriesRequestId, memory } = JSON.parse(node.content);
      return {
        memoriesRequestId,
        memory,
        isFlushed: node.agent_memory?.isFlushed ?? false,
      };
    } catch (e) {
      console.error("Failed to parse JSON from agent memory node", e);
      return undefined;
    }
  }

  /**
   * Gets the last user exchange's memory, if it exists
   */
  static getLastUserExchangeMemory(conversationModel: ConversationModel) {
    const lastUserExchange = getLastUserExchange(conversationModel);
    if (lastUserExchange?.structured_output_nodes) {
      const agentMemoryNode = lastUserExchange.structured_output_nodes.find(
        (node) => node.type === ChatResultNodeType.AGENT_MEMORY,
      );
      if (agentMemoryNode) {
        return this.parseMemoryNode(agentMemoryNode);
      }
    }
  }

  /**
   * Checks if there was a remember tool call in the current agentive turn.
   */
  static currentAgenticTurnHasRemember(conversationModel: ConversationModel): boolean {
    const exchanges = getCurrentAgenticTurnExchanges(conversationModel, (exchange) => {
      return !!exchange.structured_output_nodes?.some(
        (node) =>
          node.type === ChatResultNodeType.TOOL_USE && node.tool_use?.tool_name === "remember",
      );
    });
    return exchanges.length > 0;
  }

  /**
   * Marks memory as flushed so that it is not flushed again.
   *
   * @param conversationModel The conversation model to update
   * @param requestId The request ID of the exchange to update
   * @returns true if memory was removed, false otherwise
   */
  static markUserExchangeMemoryComplete(
    conversationModel: ConversationModel,
    requestId: string,
  ): boolean {
    const lastUserExchange = getLastUserExchange(conversationModel);
    if (!lastUserExchange?.request_id || lastUserExchange.request_id !== requestId) {
      return false;
    }

    // Mark AGENT_MEMORY nodes as completed instead of removing them
    const updatedNodes = (lastUserExchange.structured_output_nodes || []).map((node) => {
      if (node.type === ChatResultNodeType.AGENT_MEMORY) {
        return {
          ...node,
          agent_memory: {
            /* eslint-disable @typescript-eslint/naming-convention */
            ...node.agent_memory,
            content: node.agent_memory?.content || node.content,
            isFlushed: true,
          },
        };
      }
      return node;
    });

    // Check if we actually updated any memory nodes
    const hasMemoryNodes = (lastUserExchange.structured_output_nodes || []).some(
      (node) => node.type === ChatResultNodeType.AGENT_MEMORY,
    );

    if (hasMemoryNodes) {
      conversationModel.updateChatItem(requestId, {
        ...lastUserExchange,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        structured_output_nodes: updatedNodes,
      });
      return true;
    }

    return false;
  }
}
