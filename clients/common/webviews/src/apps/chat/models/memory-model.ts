import { writable, derived, type Readable, type Writable } from "svelte/store";
import type {
  MemoryInfoWithState,
  MemoryState,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/memory-messages";
import { MEMORY_STATES } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/memory-messages";
import { getMemoryClient } from "../services/memory-client";
import { MemoryUtils } from "./memory-utils";
import type { ChatModel } from "./chat-model";
import { getChatModel } from "../chat-context";

export type VersionValueType = string | undefined;
export type StateValueType = MemoryState | undefined;

/**
 * Configuration for the memory model
 */
export interface MemoryModelConfig {
  /** Default state filter to apply */
  defaultState?: StateValueType;
  /** Default version filter to apply */
  defaultVersion?: VersionValueType;
  /** Chat model to use for determining default values */
  chatModel?: ChatModel;
}

/**
 * Memory model that manages shared memory state across components
 */
export class MemoryModel {
  /* eslint-disable-next-line @typescript-eslint/naming-convention */
  POLLING_INTERVAL_MS = 3000;
  private _memoryClient = getMemoryClient();
  private _refreshTimer: NodeJS.Timeout | null = null;
  private _messageHandler: ((event: MessageEvent) => void) | null = null;
  private _config: Required<Omit<MemoryModelConfig, "chatModel">> & { chatModel?: ChatModel };

  // Core state stores
  private _memories: Writable<MemoryInfoWithState[]> = writable([]);
  private _isLoading: Writable<boolean> = writable(false);
  private _isRefreshing: Writable<boolean> = writable(false);
  private _selectedState: Writable<StateValueType> = writable(undefined);
  private _selectedVersion: Writable<VersionValueType> = writable(undefined);
  private _error: Writable<string | null> = writable(null);

  // Derived stores
  public readonly memories: Readable<MemoryInfoWithState[]>;
  public readonly isLoading: Readable<boolean>;
  public readonly isRefreshing: Readable<boolean>;
  public readonly selectedState: Readable<StateValueType>;
  public readonly selectedVersion: Readable<VersionValueType>;
  public readonly error: Readable<string | null>;
  public readonly hasMemories: Readable<boolean>;
  public readonly memoryCount: Readable<number>;
  public readonly memoriesByState: Readable<Record<MemoryState, MemoryInfoWithState[]>>;
  public readonly pendingMemoriesCount: Readable<number>;

  constructor(config: MemoryModelConfig = {}) {
    // Determine default values using the same logic as MemoryBlock.svelte
    const defaultState = config.defaultState ?? "pending";
    const defaultVersion =
      config.defaultVersion ??
      (config.chatModel ? MemoryUtils.getMemoryVersionFromFlags(config.chatModel) : undefined) ??
      "default";

    this._config = {
      defaultState,
      defaultVersion,
      chatModel: config.chatModel,
    };

    // Initialize state
    this._selectedState.set(this._config.defaultState);
    this._selectedVersion.set(this._config.defaultVersion);

    // Create derived stores
    this.memories = derived(this._memories, ($memories) => $memories);
    this.isLoading = derived(this._isLoading, ($isLoading) => $isLoading);
    this.isRefreshing = derived(this._isRefreshing, ($isRefreshing) => $isRefreshing);
    this.selectedState = derived(this._selectedState, ($selectedState) => $selectedState);
    this.selectedVersion = derived(this._selectedVersion, ($selectedVersion) => $selectedVersion);
    this.error = derived(this._error, ($error) => $error);
    this.hasMemories = derived(this._memories, ($memories) => $memories.length > 0);
    this.memoryCount = derived(this._memories, ($memories) => $memories.length);

    // Group memories by state
    this.memoriesByState = derived(this._memories, ($memories) => {
      const grouped: Record<MemoryState, MemoryInfoWithState[]> = {} as Record<
        MemoryState,
        MemoryInfoWithState[]
      >;

      // Initialize all states with empty arrays
      MEMORY_STATES.forEach((state) => {
        grouped[state] = [];
      });

      // Group memories by state
      $memories.forEach((memory) => {
        if (grouped[memory.state]) {
          grouped[memory.state].push(memory);
        }
      });

      return grouped;
    });

    // Count pending memories specifically
    this.pendingMemoriesCount = derived(this.memoriesByState, ($memoriesByState) => {
      return $memoriesByState.pending?.length ?? 0;
    });

    // Load initial memories
    this.loadMemories();
  }

  /**
   * Load memories based on current filters
   */
  public async loadMemories(): Promise<void> {
    this._isLoading.set(true);
    this._error.set(null);

    try {
      const state = this._getSelectedState();
      const version = this._getSelectedVersion();

      if (state === undefined) {
        this._memories.set([]);
        return;
      }

      const memoriesData = await this._memoryClient.getMemoriesByState(state, version);
      this._memories.set(memoriesData);
    } catch (error) {
      console.error("Failed to load memories:", error);
      this._error.set(error instanceof Error ? error.message : "Failed to load memories");
    } finally {
      this._isLoading.set(false);
    }
  }

  /**
   * Refresh memories (same as load but with different loading state)
   */
  public async refreshMemories(): Promise<void> {
    this._isRefreshing.set(true);
    this._error.set(null);

    try {
      const state = this._getSelectedState();
      const version = this._getSelectedVersion();

      if (state === undefined) {
        this._memories.set([]);
        return;
      }

      const memoriesData = await this._memoryClient.getMemoriesByState(state, version);
      this._memories.set(memoriesData);
    } catch (error) {
      console.error("Failed to refresh memories:", error);
      this._error.set(error instanceof Error ? error.message : "Failed to refresh memories");
    } finally {
      this._isRefreshing.set(false);
    }
  }

  /**
   * Update memory state
   */
  public async updateMemoryState(
    memoryId: string,
    newState: MemoryState,
    editedContent?: string,
  ): Promise<void> {
    try {
      await this._memoryClient.updateMemoryState(memoryId, newState, editedContent);
      // Refresh memories after successful update
      await this.refreshMemories();
    } catch (error) {
      console.error("Failed to update memory state:", error);
      this._error.set(error instanceof Error ? error.message : "Failed to update memory state");
      throw error;
    }
  }

  /**
   * Set state filter and reload memories
   */
  public async setStateFilter(state: StateValueType): Promise<void> {
    this._selectedState.set(state);
    await this.loadMemories();
  }

  /**
   * Set version filter and reload memories
   */
  public async setVersionFilter(version: VersionValueType): Promise<void> {
    this._selectedVersion.set(version);
    await this.loadMemories();
  }

  /**
   * Clean up resources
   */
  public destroy(): void {
    if (this._refreshTimer) {
      clearInterval(this._refreshTimer);
      this._refreshTimer = null;
    }

    if (this._messageHandler) {
      window.removeEventListener("message", this._messageHandler);
      this._messageHandler = null;
    }
  }

  private _getSelectedState(): StateValueType {
    let state: StateValueType;
    this._selectedState.subscribe((value) => (state = value))();
    return state;
  }

  private _getSelectedVersion(): VersionValueType {
    let version: VersionValueType;
    this._selectedVersion.subscribe((value) => (version = value))();
    return version;
  }
}

/**
 * Create a memory model instance with optional configuration
 */
export function createMemoryModel(config?: MemoryModelConfig): MemoryModel {
  return new MemoryModel(config);
}

// Singleton instance for global use
let globalMemoryModel: MemoryModel | undefined;

/**
 * Get the global memory model instance
 */
export function getGlobalMemoryModel(): MemoryModel {
  if (!globalMemoryModel) {
    try {
      const chatModel = getChatModel();
      globalMemoryModel = createMemoryModel({ chatModel });
    } catch (e) {
      // If chatModel is not available in context, create without it
      console.warn("ChatModel not available for global memory model, using defaults");
      globalMemoryModel = createMemoryModel();
    }
  }
  return globalMemoryModel;
}
