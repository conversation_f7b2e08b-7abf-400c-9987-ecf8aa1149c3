/* eslint-disable @typescript-eslint/naming-convention */
import { describe, it, expect, vi, beforeEach } from "vitest";
import { MemoryModel, createMemoryModel } from "./memory-model";
import { MemoryUtils } from "./memory-utils";
import type { ChatModel } from "./chat-model";

// Mock the memory client
vi.mock("../services/memory-client", () => ({
  getMemoryClient: () => ({
    getMemoriesByState: vi.fn().mockResolvedValue([]),
    updateMemoryState: vi.fn().mockResolvedValue(undefined),
  }),
}));

// Mock MemoryUtils
vi.mock("./memory-utils", () => ({
  MemoryUtils: {
    getMemoryVersionFromFlags: vi.fn(),
  },
}));

describe("MemoryModel", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe("constructor", () => {
    it("should use default values when no config is provided", () => {
      const model = new MemoryModel();

      // Check that the model initializes with default values
      let selectedState: any;
      let selectedVersion: any;

      model.selectedState.subscribe((value) => (selectedState = value))();
      model.selectedVersion.subscribe((value) => (selectedVersion = value))();

      expect(selectedState).toBe("pending");
      expect(selectedVersion).toBe("default");
    });

    it("should use provided default values from config", () => {
      const model = new MemoryModel({
        defaultState: "user_accepted",
        defaultVersion: "custom-version",
      });

      let selectedState: any;
      let selectedVersion: any;

      model.selectedState.subscribe((value) => (selectedState = value))();
      model.selectedVersion.subscribe((value) => (selectedVersion = value))();

      expect(selectedState).toBe("user_accepted");
      expect(selectedVersion).toBe("custom-version");
    });

    it("should use chatModel to determine default version when provided", () => {
      const mockChatModel = {
        flags: {
          memoriesParams: {
            memory_retrieval_version: "chat-model-version",
          },
        },
      } as any as ChatModel;

      vi.mocked(MemoryUtils.getMemoryVersionFromFlags).mockReturnValue("chat-model-version");

      const model = new MemoryModel({
        chatModel: mockChatModel,
      });

      let selectedState: any;
      let selectedVersion: any;

      model.selectedState.subscribe((value) => (selectedState = value))();
      model.selectedVersion.subscribe((value) => (selectedVersion = value))();

      expect(selectedState).toBe("pending"); // default state
      expect(selectedVersion).toBe("chat-model-version"); // from chat model
      expect(MemoryUtils.getMemoryVersionFromFlags).toHaveBeenCalledWith(mockChatModel);
    });

    it("should fallback to default version when chatModel returns undefined", () => {
      const mockChatModel = {
        flags: {},
      } as any as ChatModel;

      vi.mocked(MemoryUtils.getMemoryVersionFromFlags).mockReturnValue(undefined);

      const model = new MemoryModel({
        chatModel: mockChatModel,
      });

      let selectedVersion: any;
      model.selectedVersion.subscribe((value) => (selectedVersion = value))();

      expect(selectedVersion).toBe("default"); // fallback to default
      expect(MemoryUtils.getMemoryVersionFromFlags).toHaveBeenCalledWith(mockChatModel);
    });

    it("should prioritize explicit config over chatModel", () => {
      const mockChatModel = {
        flags: {
          memoriesParams: {
            memory_retrieval_version: "chat-model-version",
          },
        },
      } as any as ChatModel;

      vi.mocked(MemoryUtils.getMemoryVersionFromFlags).mockReturnValue("chat-model-version");

      const model = new MemoryModel({
        chatModel: mockChatModel,
        defaultVersion: "explicit-version", // This should take precedence
      });

      let selectedVersion: any;
      model.selectedVersion.subscribe((value) => (selectedVersion = value))();

      expect(selectedVersion).toBe("explicit-version"); // explicit config wins
      // Should not call MemoryUtils since explicit version is provided
      expect(MemoryUtils.getMemoryVersionFromFlags).not.toHaveBeenCalled();
    });
  });

  describe("createMemoryModel factory", () => {
    it("should create a memory model with provided config", () => {
      const config = {
        defaultState: "user_accepted" as const,
        defaultVersion: "test-version",
      };

      const model = createMemoryModel(config);

      let selectedState: any;
      let selectedVersion: any;

      model.selectedState.subscribe((value) => (selectedState = value))();
      model.selectedVersion.subscribe((value) => (selectedVersion = value))();

      expect(selectedState).toBe("user_accepted");
      expect(selectedVersion).toBe("test-version");
    });
  });
});
