import {
    extractScope<PERSON><PERSON><PERSON>ontent,
    parseMemoriesFromContent,
} from "@augment-internal/sidecar-libs/src/agent/memory/memory-parser";
import { getPluginFileStore } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import type { IPluginFileStore } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import {
    MemoryEntry,
    MemoryInfoWithState,
    MemoryState,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/memory-messages";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";

import { APIServer } from "../augment-api";
import { getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { BlobUploader, BlobUploadRequest } from "../workspace/open-file-manager-v2/blob-uploader";

const STATE_FILE = "memory-database-state.json";

/**
 * If set to true, the MemoryDatabase will initialize with existing memories
 * even when no state file is found, instead of starting fresh.
 */
const INITIALIZE_WITH_EXISTING_MEMORIES = true;

/**
 * Extended interface for internal memory management with additional metadata
 */
export interface MemoryInfo extends MemoryEntry {
    timestamp: number;
    blobName?: string;
    state: MemoryState;
    scope?: string;
    requestId?: string;
}

/**
 * Interface representing a memory with its associated blob
 */
export interface MemoryWithBlob {
    memory: MemoryInfo;
    blobName: string;
}

/**
 * State interface for the MemoryDatabase
 */
export interface MemoryDatabaseState {
    memories: MemoryInfo[];
    lastCheckpointId?: string;
    lastProcessedTimestamp: number;
}

/**
 * MemoryDatabase manages the indexing and storage of agent memories.
 * It listens for memory creation events, stores them locally using IPluginFileStore,
 * and uploads them to the backend for indexing.
 *
 * TODO: This is all a temporary hack to get us started.  We will eventually move
 * it into the backend.
 *
 * TODO: This never deletes memories.
 */
export class MemoryDatabase extends DisposableService {
    private readonly _logger = getLogger("MemoryDatabase");
    private readonly _fileStore: IPluginFileStore;
    private _state: MemoryDatabaseState;
    private readonly _blobUploader: BlobUploader;

    // File names for persistent storage

    constructor(
        private readonly _blobNameCalculator: BlobNameCalculator,
        private readonly _apiServer: APIServer,
        private readonly _getAgentMemories: () => Promise<string | undefined>
    ) {
        super();
        this._fileStore = getPluginFileStore();
        this._blobUploader = new BlobUploader(this._blobNameCalculator, this._apiServer);

        // Initialize state
        this._state = {
            memories: [],
            lastProcessedTimestamp: 0,
        };

        void this._initialize();
    }

    /**
     * Initialize the MemoryDatabase by loading existing state
     */
    private async _initialize(): Promise<void> {
        try {
            await this._loadState();
            this._logger.debug("MemoryDatabase initialized");
        } catch (error) {
            this._logger.error("Failed to initialize MemoryDatabase", error);
        }
    }

    /**
     * Load state from persistent storage
     */
    private async _loadState(): Promise<void> {
        try {
            const stateData = await this._fileStore.loadAsset(STATE_FILE);
            if (stateData && stateData.length > 0) {
                const stateString = new TextDecoder().decode(stateData);
                this._state = JSON.parse(stateString) as MemoryDatabaseState;
                this._logger.debug(
                    `Loaded state with ${this._state.memories.length} memories and checkpoint ${this._state.lastCheckpointId}`
                );
            } else if (INITIALIZE_WITH_EXISTING_MEMORIES) {
                // Get current memories
                const currentMemories = await this._getAgentMemories();
                if (!currentMemories) {
                    this._logger.debug("No memories found");
                    return;
                }
                const memoryCount = await this._parseExistingMemories(currentMemories);
                this._logger.debug(
                    `No existing state found, initializing with ${memoryCount} existing memories`
                );
            } else {
                this._logger.debug("No existing state found, starting fresh");
            }
        } catch (error) {
            this._logger.error("Failed to load state", error);
        }
    }

    /**
     * Parse memories from the memories file content using the memory parser utility
     */
    private async _parseExistingMemories(content: string): Promise<number> {
        const memories = parseMemoriesFromContent(content, "DEFAULT");

        for (const memoryEntry of memories) {
            await this.processMemoryEntry(memoryEntry, "imported");
        }

        return memories.length;
    }

    /**
     * Save state to persistent storage
     */
    private async _saveState(): Promise<void> {
        try {
            const stateData = JSON.stringify(this._state, null, 2);
            const stateBytes = new TextEncoder().encode(stateData);
            await this._fileStore.saveAsset(STATE_FILE, stateBytes);
        } catch (error) {
            this._logger.error("Failed to save state", error);
        }
    }

    /**
     * Process a single memory entry from the listener system
     * This is the new method for handling schema-compliant memory entries
     */
    public async processMemoryEntry(
        memoryEntry: MemoryEntry,
        state?: MemoryState,
        requestId?: string
    ): Promise<void> {
        try {
            this._logger.debug(
                `Processing memory id: ${memoryEntry.id}\ncontent: ${memoryEntry.content}\nversion: ${memoryEntry.version}`
            );

            // Extract scope information from content if present
            const scope = extractScopeFromContent(memoryEntry.content);

            // Convert MemoryEntry to MemoryInfo with additional metadata
            const memoryInfo: MemoryInfo = {
                ...memoryEntry,
                id: this._blobNameCalculator.calculateNoThrow(
                    `memory/${memoryEntry.version}/${memoryEntry.id}`,
                    memoryEntry.content
                ),
                timestamp: Date.now(),
                state: state ?? "pending",
                scope,
                requestId,
            };

            // Check if we've already processed this memory (by content and version)
            const existingMemory = this._state.memories.find(
                (m) => m.content === memoryInfo.content && m.version === memoryInfo.version
            );

            if (existingMemory) {
                this._logger.debug(`Memory entry already processed: ${memoryEntry.id}`);
                return;
            }

            // Upload the memory as a blob
            this._uploadMemoryAsBlob(memoryInfo);

            // Add to state
            this._state.memories.push(memoryInfo);
            this._state.lastProcessedTimestamp = memoryInfo.timestamp;

            // Create a new checkpoint
            await this._createCheckpoint();

            // Save state
            await this._saveState();

            this._logger.debug(
                `Successfully processed memory entry: ${memoryEntry.id}\ncontent: ${memoryEntry.content}\nversion: ${memoryEntry.version}`
            );
        } catch (error) {
            this._logger.error(`Failed to process memory entry ${memoryEntry.id}`, error);
            throw error;
        }
    }

    /**
     * Upload a memory as a blob to the backend
     */
    private _uploadMemoryAsBlob(memory: MemoryInfo) {
        try {
            // Create blob content
            const blobContent = JSON.stringify(
                {
                    id: memory.id,
                    content: memory.content,
                    timestamp: memory.timestamp,
                    version: memory.version,
                    type: "memory",
                    requestId: memory.requestId,
                },
                null,
                2
            );

            // Upload as blob
            const uploadRequest: BlobUploadRequest = {
                path: `memory_${memory.id}`,
                readContent: async () => Promise.resolve(blobContent),
            };
            const blobName = this._blobUploader.enqueueUpload(uploadRequest, blobContent);

            // Update memory with blob name
            memory.blobName = blobName;

            this._logger.debug(`Uploaded memory as blob: ${blobName}`);
        } catch (error) {
            this._logger.error(`Failed to upload memory ${memory.id} as blob`, error);
        }
    }

    /**
     * Create a checkpoint with current memory blobs
     */
    private async _createCheckpoint(): Promise<void> {
        try {
            const memoryBlobs = this._state.memories
                .filter((memory) => memory.blobName)
                .map((memory) => memory.blobName!);

            if (memoryBlobs.length === 0) {
                return;
            }

            // Create a checkpoint using the existing checkpoint mechanism
            const result = await this._apiServer.checkpointBlobs({
                checkpointId: undefined,
                addedBlobs: memoryBlobs,
                deletedBlobs: [],
            });

            // Store the new checkpoint ID for future use
            this._state.lastCheckpointId = result.newCheckpointId;
            this._logger.debug(`Created memory checkpoint: ${this._state.lastCheckpointId}`);
        } catch (error) {
            this._logger.error("Failed to create checkpoint", error);
        }
    }

    /**
     * Get the current checkpoint ID for memory retrieval
     */
    public getCheckpointId(): string | undefined {
        return this._state.lastCheckpointId;
    }

    /**
     * Get memories filtered by version
     */
    public getMemoriesByVersion(version: string): MemoryInfo[] {
        return this._state.memories.filter((memory) => memory.version === version);
    }

    /**
     * Get memories filtered by state and optionally by version
     */
    public getMemoriesByState(state: MemoryState, version?: string): MemoryInfoWithState[] {
        return this._state.memories
            .filter((memory) => {
                const stateMatches = memory.state === state;
                const versionMatches = version === undefined || memory.version === version;
                return stateMatches && versionMatches;
            })
            .map(
                (memory): MemoryInfoWithState => ({
                    id: memory.id,
                    content: memory.content,
                    version: memory.version,
                    timestamp: memory.timestamp,
                    blobName: memory.blobName,
                    state: memory.state,
                    scope: memory.scope,
                    requestId: memory.requestId,
                })
            );
    }

    /**
     * Update a memory's state
     */
    public async updateMemoryState(
        memoryId: string,
        newState: MemoryState,
        editedContent?: string
    ): Promise<void> {
        const memoryIndex = this._state.memories.findIndex((m) => m.id === memoryId);

        if (memoryIndex === -1) {
            throw new Error(`Memory with id ${memoryId} not found`);
        }

        const memory = this._state.memories[memoryIndex];

        // Update the state
        memory.state = newState;

        // If content was edited, update it
        if (editedContent !== undefined) {
            memory.content = editedContent;

            // Extract scope from edited content
            memory.scope = extractScopeFromContent(editedContent);

            // Recalculate blob name with new content
            const newBlobName = this._blobNameCalculator.calculateNoThrow(
                `memory/${memory.version}/${memory.id}`,
                editedContent
            );

            // Re-upload the edited memory
            this._uploadMemoryAsBlob({
                ...memory,
                blobName: newBlobName,
            });
        }

        // Update timestamp
        memory.timestamp = Date.now();

        // Save state
        await this._saveState();

        this._logger.debug(
            `Updated memory ${memoryId} state to ${newState} with content: ${memory.content}`
        );
    }

    /**
     * Get a checkpoint ID for memories of a specific version
     */
    public async getCheckpointIdForVersion(version: string): Promise<string | undefined> {
        const versionMemories = this.getMemoriesByVersion(version);

        if (versionMemories.length === 0) {
            this._logger.debug(`No memories found for version: ${version}`);
            return undefined;
        }

        const memoryBlobs = versionMemories
            .filter((memory) => memory.blobName)
            .filter((memory) => memory.state !== "user_rejected")
            .map((memory) => memory.blobName!);

        if (memoryBlobs.length === 0) {
            this._logger.debug(`No blob names found for memories in version: ${version}`);
            return undefined;
        }

        try {
            // Create a checkpoint using only the blobs for this version
            const result = await this._apiServer.checkpointBlobs({
                checkpointId: undefined,
                addedBlobs: memoryBlobs,
                deletedBlobs: [],
            });

            this._logger.debug(
                `Created version-specific checkpoint for version ${version}: ${result.newCheckpointId}`
            );
            return result.newCheckpointId;
        } catch (error) {
            this._logger.error(`Failed to create checkpoint for version ${version}`, error);
            return undefined;
        }
    }

    /**
     * Get the absolute path to the memory database state file
     */
    public async getStateFilePath(): Promise<string | undefined> {
        return await this._fileStore.getAssetPath(STATE_FILE);
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        super.dispose();
    }
}
