package com.augmentcode.intellij.syncing

import 
import com.augmentcode.intellij.mock.PathFilterServiceMocks
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.utils.IndexUtil.expectedBlobName
import com.google.protobuf.util.JsonFormat
import com.intellij.testFramework.LightVirtualFile
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi.BatchUploadRequest

@RunWith(JUnit4::class)
class AugmentBlobUploaderTest : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()

    augmentHelpers().forcePluginState(PluginState.ENABLED)

    // Set up mock path filter that accepts everything
    PathFilterServiceMocks.mockAlwaysAccept(project, testRootDisposable)

    // Ensure the SyncFilter + PathFiler is reset
    augmentHelpers().unregisterServiceIfNeeded(AugmentBlobUploaderManager::class.java)
  }

  @Test
  fun testUploadBlob() =
    runTest {
      val gson = GsonUtil.createApiGson()
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/find-missing" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val findRequest = gson.fromJson(requestBody, FindMissingRequest::class.java)
              respond(
                content =
                  """
                  {
                    "unknown_memory_names": ${gson.toJson(findRequest.memObjectNames)},
                    "nonindexed_blob_names": []
                  }
                  """.trimIndent(),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest =
                BatchUploadRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()
              respond(
                content =
                  """
                  {
                    "blob_names": ${gson.toJson(uploadRequest.blobsList.map { expectedBlobName(it.path, it.content) })}
                  }
                  """.trimIndent(),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unhandled ${request.url.encodedPath}")
          }
        }
      // Set up mock HTTP client
      augmentHelpers().registerMockEngine(mockEngine)

      val blobUploader = AugmentBlobUploaderManager.getInstance(project)
      blobUploader.startUploadLoop()

      val blob1 =
        UploadRequest(
          rootPath = "/src",
          relPath = "foo.txt",
          expectedBlobName = expectedBlobName("/src/foo.txt", "foo"),
          fileReference = LightVirtualFile("foo.txt", "foo"),
        )
      val blob2 =
        UploadRequest(
          rootPath = "/src",
          relPath = "bar.txt",
          expectedBlobName = expectedBlobName("/src/bar.txt", "bar"),
          fileReference = LightVirtualFile("bar.txt", "bar"),
        )
      val blobError =
        UploadRequest(
          rootPath = "/src",
          relPath = "inject-error.txt",
          expectedBlobName = expectedBlobName("/src/inject-error.txt", "error"),
          fileReference = LightVirtualFile("inject-error.txt", "error"),
        )

      // Upload a valid blob
      blobUploader.enqueue(blob1)

      // Upload an invalid blob
      blobUploader.enqueue(blobError)

      // Upload second valid blog
      blobUploader.enqueue(blob2)

      // Wait for requests with timeout
      waitForAssertion({
        assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/find-missing" })
        assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" })
      }, timeoutMs = 5000)
    }

  @Test
  fun testUploadBlobGitIgnore() =
    runTest {
      // Set up mock engine that will fail the test if it receives any requests
      val mockEngine =
        MockEngine { request ->
          if (request.url.encodedPath == "/get-models") {
            HttpUtil.respondGetModels(this)
          } else {
            fail("Unexpected request to ${request.url.encodedPath}")
            respond(
              content = "Unexpected request",
              status = HttpStatusCode.InternalServerError,
            )
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val blobUploader = AugmentBlobUploaderManager.getInstance(project)
      blobUploader.startUploadLoop()

      val blob1 =
        UploadRequest(
          rootPath = "/src",
          relPath = "foo.txt",
          expectedBlobName = expectedBlobName("/src/foo.txt", "foo"),
          fileReference = LightVirtualFile("foo.txt", "foo"),
        )
      val blob2 =
        UploadRequest(
          rootPath = "/src",
          relPath = "bar.txt",
          expectedBlobName = expectedBlobName("/src/bar.txt", "bar"),
          fileReference = LightVirtualFile("bar.txt", "bar"),
        )

      blobUploader.enqueue(blob1)
      blobUploader.enqueue(blob2)

      // Wait a bit to ensure no requests are made
      delay(1000)

      // Verify no requests were made
      assertEquals(0, mockEngine.requestHistory.size)
    }

  @Test
  fun testUploadBlobContentSizeLimit() =
    runTest {
      // Scenario: Two blobs are enqueued. The first blob is under the size limit and should be uploaded.
      // The second blob exceeds MAX_BATCH_CONTENT_SIZE_BYTES and should trigger a new batch.

      // Create a normal-sized blob and an oversized blob
      val normalBlob =
        UploadRequest(
          rootPath = "/src",
          relPath = "normal.txt",
          expectedBlobName = expectedBlobName("/src/normal.txt", "normal.txt"),
          fileReference = LightVirtualFile("normal.txt", "normal content"),
        )

      val largeContent =
        "x".repeat(
          AugmentRemoteSyncingManagerImpl.MAX_BATCH_CONTENT_SIZE_BYTES + 1 - normalBlob.fileReference.length.toInt(),
        )
      val largeBlob =
        UploadRequest(
          rootPath = "/src",
          relPath = "large.txt",
          expectedBlobName = expectedBlobName("/src/large.txt", "large.txt"),
          fileReference = LightVirtualFile("large.txt", largeContent),
        )

      val gson = GsonUtil.createApiGson()
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/find-missing" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val findRequest = gson.fromJson(requestBody, FindMissingRequest::class.java)

              // Each find-missing request should contain exactly one blob
              assertEquals(1, findRequest.memObjectNames.size)

              respond(
                content = """{"unknown_memory_names":["${findRequest.memObjectNames.first()}"],"nonindexed_blob_names":[]}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest =
                BatchUploadRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()

              // Each batch-upload should contain exactly one blob
              assertEquals(1, uploadRequest.blobsList.size)

              respond(
                content = """{"blob_names":["${uploadRequest.blobsList[0].let { expectedBlobName(it.path, it.content) }}"]}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val blobUploader = AugmentBlobUploaderManager.getInstance(project)
      blobUploader.startUploadLoop()

      blobUploader.enqueue(normalBlob)
      blobUploader.enqueue(largeBlob)

      // Verify we made two separate find-missing and batch-upload requests
      waitForAssertion({
        assertEquals(2, mockEngine.requestHistory.count { it.url.encodedPath == "/find-missing" })
        assertEquals(2, mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" })
      }, timeoutMs = 5000)

      // Verify the order of requests
      val paths =
        mockEngine.requestHistory.mapNotNull { request ->
          when (request.url.encodedPath) {
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest =
                BatchUploadRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()
              uploadRequest.blobsList[0].path
            }

            else -> null
          }
        }

      assertEquals(listOf("normal.txt", "large.txt"), paths)
    }

  @Test
  fun testUploadBlobBatchSizeLimit() =
    runTest {
      // Scenario: We enqueue MAX_FIND_MISSING_BATCH_SIZE + 1 small blobs.
      // First batch should contain exactly MAX_FIND_MISSING_BATCH_SIZE blobs,
      // and second batch should contain the remaining blob.

      // Create MAX_FIND_MISSING_BATCH_SIZE + 1 small blobs
      val blobs =
        (1..AugmentRemoteSyncingManagerImpl.MAX_FIND_MISSING_BATCH_SIZE + 1).map { index ->
          UploadRequest(
            rootPath = "/src",
            relPath = "blob$index.txt",
            expectedBlobName = expectedBlobName("/src/blob$index.txt", "blob$index.txt"),
            fileReference = LightVirtualFile("blob$index.txt", "small content for blob $index"),
          )
        }

      val gson = GsonUtil.createApiGson()
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/find-missing" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val findRequest = gson.fromJson(requestBody, FindMissingRequest::class.java)

              // First request should have MAX_FIND_MISSING_BATCH_SIZE blobs
              // Second request should have 1 blob
              assertTrue(
                findRequest.memObjectNames.size == AugmentRemoteSyncingManagerImpl.MAX_FIND_MISSING_BATCH_SIZE ||
                  findRequest.memObjectNames.size == 1,
              )

              respond(
                content = """{"unknown_memory_names":${gson.toJson(findRequest.memObjectNames)},"nonindexed_blob_names":[]}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest =
                BatchUploadRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()

              // First request should have MAX_FIND_MISSING_BATCH_SIZE blobs
              // Second request should have 1 blob
              assertTrue(
                uploadRequest.blobsList.size == AugmentRemoteSyncingManagerImpl.MAX_FIND_MISSING_BATCH_SIZE ||
                  uploadRequest.blobsList.size == 1,
              )

              respond(
                content = """{"blob_names":${gson.toJson(uploadRequest.blobsList.map { expectedBlobName(it.path, it.content) })}}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val blobUploader = AugmentBlobUploaderManager.getInstance(project)
      blobUploader.startUploadLoop()

      blobs.forEach { blobUploader.enqueue(it) }

      // Verify we made two separate find-missing and batch-upload requests
      waitForAssertion({
        assertEquals(2, mockEngine.requestHistory.count { it.url.encodedPath == "/find-missing" })
        assertEquals(2, mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" })
      }, timeoutMs = 5000)

      // Verify the batch sizes
      val batchSizes =
        mockEngine.requestHistory.mapNotNull { request ->
          when (request.url.encodedPath) {
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest =
                BatchUploadRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()
              uploadRequest.blobsList.size
            }

            else -> null
          }
        }

      assertEquals(
        listOf(
          AugmentRemoteSyncingManagerImpl.MAX_FIND_MISSING_BATCH_SIZE,
          1,
        ),
        batchSizes,
      )

      // Verify all blobs were processed in order
      val processedPaths =
        mockEngine.requestHistory.mapNotNull { request ->
          when (request.url.encodedPath) {
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest =
                BatchUploadRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()
              uploadRequest.blobsList.map { it.path }
            }

            else -> null
          }
        }.flatten()

      assertEquals(
        blobs.map { it.relPath },
        processedPaths,
      )
    }

  @OptIn(ExperimentalCoroutinesApi::class)
  @Test
  fun testUploadBlobApiUnavailable() =
    runTest {
      // Scenario: API is unavailable (no auth token). Blobs should remain in queue
      // and no requests should be made until API becomes available.

      // Clear API token to make API unavailable
      AugmentSettings.instance.apiToken = null
      AugmentSettings.instance.completionURL = null

      // Create test blobs
      val blob1 =
        UploadRequest(
          rootPath = "/src",
          relPath = "test1.txt",
          expectedBlobName = expectedBlobName("/src/test1.txt", "test1.txt"),
          fileReference = LightVirtualFile("test1.txt", "test content 1"),
        )
      val blob2 =
        UploadRequest(
          rootPath = "/src",
          relPath = "test2.txt",
          expectedBlobName = expectedBlobName("/src/test2.txt", "test2.txt"),
          fileReference = LightVirtualFile("test2.txt", "test content 2"),
        )

      // Set up mock engine that will fail the test if it receives any requests
      val mockEngine =
        MockEngine { request ->
          fail("Unexpected request to ${request.url.encodedPath} while API unavailable")
          respond(
            content = "Unexpected request",
            status = HttpStatusCode.InternalServerError,
          )
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val blobUploader = AugmentBlobUploaderManager.getInstance(project)
      blobUploader.startUploadLoop()

      blobUploader.enqueue(blob1)
      blobUploader.enqueue(blob2)

      // Wait a bit to ensure no requests are made while API is unavailable
      delay(1000)
      assertEquals(0, mockEngine.requestHistory.size)

      // Replace mock engine with one that handles requests normally
      val gson = GsonUtil.createApiGson()
      val workingMockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/find-missing" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val findRequest = gson.fromJson(requestBody, FindMissingRequest::class.java)
              respond(
                content = """{"unknown_memory_names":${gson.toJson(findRequest.memObjectNames)},"nonindexed_blob_names":[]}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest =
                BatchUploadRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()
              respond(
                content = """{"blob_names":${gson.toJson(uploadRequest.blobsList.map { expectedBlobName(it.path, it.content) })}}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(workingMockEngine)

      // Make API available
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"

      // Verify requests were made once API became available
      waitForAssertion({
        assertEquals(1, workingMockEngine.requestHistory.count { it.url.encodedPath == "/find-missing" })
        assertEquals(1, workingMockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" })
      })

      // Verify all blobs were processed
      val processedPaths =
        workingMockEngine.requestHistory.mapNotNull { request ->
          when (request.url.encodedPath) {
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest =
                BatchUploadRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()
              uploadRequest.blobsList.map { it.path }
            }

            else -> null
          }
        }.flatten()

      assertEquals(
        listOf("test1.txt", "test2.txt"),
        processedPaths,
      )
    }
}
