package com.augmentcode.intellij.workspacemanagement.coordination.steps

import 
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.index.QualifiedPathName
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobAddedEvent
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobChangeEvent
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobUpdatedEvent
import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vfs.VirtualFile
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.test.advanceTimeBy
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Path
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(JUnit4::class)
class WaitForIndexingStepTest : AugmentBasePlatformTestCase() {
  private val gson = GsonUtil.createApiGson()
  private val findMissingRequests = mutableListOf<FindMissingRequest>()
  private lateinit var blobNamesChannel: RoughlySizedChannel<CoordinationFileDetailsWithBlob>
  private lateinit var changesChannel: RoughlySizedChannel<BlobChangeEvent>
  private lateinit var uploadChannel: RoughlySizedChannel<FileToUpload>
  private lateinit var manager: WaitForIndexingStep

  private val maxWaitTimeInTest = 500.milliseconds
  private val backendHealthBackoffTimeInTest = 5.seconds
  private val slowIndexingBackoffTimeInTest = 5.seconds
  private val batchSizeInTest = 2

  override fun setUp() {
    super.setUp()
    augmentHelpers().forcePluginState(PluginState.ENABLED)
    findMissingRequests.clear()

    // Mock AugmentRoot
    mockkObject(AugmentRoot)
    every { AugmentRoot.findFile(any<String>()) } answers {
      val file: VirtualFile = mockk(relaxed = true)
      every { file.path } returns arg<String>(0)
      file
    }
    every { AugmentRoot.findQualifiedPathName(any(), any()) } answers {
      QualifiedPathName("", arg<VirtualFile>(1).path)
    }

    blobNamesChannel = RoughlySizedChannel<CoordinationFileDetailsWithBlob>(Channel(Channel.Factory.UNLIMITED))
    changesChannel = RoughlySizedChannel<BlobChangeEvent>(Channel(Channel.Factory.UNLIMITED))
    uploadChannel = RoughlySizedChannel<FileToUpload>(Channel(Channel.Factory.UNLIMITED))
    manager =
      WaitForIndexingStep(
        project,
        augmentHelpers().createCoroutineScope(Dispatchers.IO),
        blobNamesChannel,
        changesChannel,
        uploadChannel,
        batchSize = batchSizeInTest,
        maxWaitTime = maxWaitTimeInTest,
        slowIndexingBackoffTime = slowIndexingBackoffTimeInTest,
        backendHealthBackoffTime = backendHealthBackoffTimeInTest,
      )
  }

  override fun tearDown() {
    Disposer.dispose(manager)
    blobNamesChannel.close()
    changesChannel.close()
    uploadChannel.close()
    super.tearDown()
  }

  private fun setupThrowingEngine(code: Int) {
    val mockEngine =
      MockEngine.Companion { request ->
        when (request.url.encodedPath) {
          "/find-missing" -> {
            val requestBody = request.body.toByteArray().decodeToString()
            val findRequest = gson.fromJson(requestBody, FindMissingRequest::class.java)
            findMissingRequests.add(findRequest)
            respond("test error", HttpStatusCode.fromValue(code))
          }
          "/get-models" -> HttpUtil.respondGetModels(this)
          "/report-error" -> HttpUtil.respondOK(this)
          else -> error("Unhandled ${request.url.encodedPath}")
        }
      }
    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)
  }

  private fun setupMockEngine(
    unknownBlobs: Set<String> = emptySet(),
    nonIndexedBlobs: Set<String> = emptySet(),
  ) {
    val mockEngine =
      MockEngine.Companion { request ->
        when (request.url.encodedPath) {
          "/find-missing" -> {
            val requestBody = request.body.toByteArray().decodeToString()
            val findRequest = gson.fromJson(requestBody, FindMissingRequest::class.java)
            findMissingRequests.add(findRequest)
            respond(
              content = """{"unknown_memory_names":${gson.toJson(unknownBlobs)},"nonindexed_blob_names":${gson.toJson(nonIndexedBlobs)}}""",
              headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
          }

          "/get-models" -> HttpUtil.respondGetModels(this)
          else -> error("Unhandled ${request.url.encodedPath}")
        }
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)
  }

  @Test
  fun testNothingIndexed() =
    kotlinx.coroutines.test.runTest {
      setupMockEngine(nonIndexedBlobs = setOf("blob1", "blob2"))

      // Send blob events
      val event1 =
        CoordinationFileDetailsWithBlob(
          CoordinationFileDetails(
            virtualFile = mockk(relaxed = true) { every { toNioPath() } returns Path.of("foo.txt") },
            rootPath = "",
            relPath = "foo.txt",
          ),
          "blob1",
          System.currentTimeMillis(),
        )
      val event2 =
        CoordinationFileDetailsWithBlob(
          CoordinationFileDetails(
            virtualFile = mockk(relaxed = true) { every { toNioPath() } returns Path.of("bar.txt") },
            rootPath = "",
            relPath = "bar.txt",
          ),
          "blob2",
          System.currentTimeMillis(),
        )

      blobNamesChannel.send(event1)
      blobNamesChannel.send(event2)

      manager.startProcessing()

      advanceTimeBy(maxWaitTimeInTest)

      waitForAssertion({
        assertEquals(1, findMissingRequests.size)
      }, timeoutMs = 5000)

      val firstRequest = findMissingRequests.first()
      assertEquals(setOf("blob1", "blob2"), firstRequest.memObjectNames.toSet())

      // Expect the blogs to be re-queued
      waitForAssertion({
        assertEquals(2, blobNamesChannel.approximateSize)
      })

      advanceTimeBy(slowIndexingBackoffTimeInTest - 1.seconds)
      assertEquals(1, findMissingRequests.size)

      // Surpass backoff time
      advanceTimeBy(2.seconds)

      // All blobs should be re-queued since they are not indexed
      waitForAssertion({
        assert(findMissingRequests.size > 1)
      }, timeoutMs = 5000)
      val last = findMissingRequests.last()
      assertEquals(setOf("blob1", "blob2"), last.memObjectNames.toSet())
    }

  @Test
  fun testIndexedBlobs() =
    kotlinx.coroutines.test.runTest {
      // Treat blob1 as indexed
      setupMockEngine(nonIndexedBlobs = setOf("blob2"))

      val event1 =
        CoordinationFileDetailsWithBlob(
          CoordinationFileDetails(
            virtualFile = mockk(relaxed = true) { every { toNioPath() } returns Path.of("foo.txt") },
            rootPath = "",
            relPath = "foo.txt",
          ),
          "blob1",
          System.currentTimeMillis(),
        )
      val event2 =
        CoordinationFileDetailsWithBlob(
          CoordinationFileDetails(
            virtualFile = mockk(relaxed = true) { every { toNioPath() } returns Path.of("bar.txt") },
            rootPath = "",
            relPath = "bar.txt",
          ),
          "blob2",
          System.currentTimeMillis(),
        )

      // Ensure BlobNameService has no existing blob for this path (BlobAddedEvent case)
      val blobNameService = BlobNameService.getInstance(project)
      val path1 = java.nio.file.Paths.get(event1.fileDetails.rootPath, event1.fileDetails.relPath)
      assertNull(blobNameService.getByPath(path1.toString()))

      blobNamesChannel.send(event1)
      blobNamesChannel.send(event2)

      manager.startProcessing()

      advanceTimeBy(maxWaitTimeInTest)

      waitForAssertion({
        assertEquals(1, findMissingRequests.size)
      }, timeoutMs = 5000)

      val firstRequest = findMissingRequests.first()
      assertEquals(setOf("blob1", "blob2"), firstRequest.memObjectNames.toSet())

      var changesResult: BlobChangeEvent? = null
      waitForAssertion({
        changesResult = changesChannel.tryReceive().getOrNull()
        assertNotNull(changesResult)
      }, timeoutMs = 5000)

      // Verify we got a BlobAddedEvent since no blob existed previously
      assertTrue(changesResult is BlobAddedEvent)
      assertEquals("blob1", (changesResult as BlobAddedEvent).newBlobName)

      waitForAssertion({
        assert(findMissingRequests.size > 1)
      })
      val last = findMissingRequests.last()
      assertEquals(setOf("blob2"), last.memObjectNames.toSet())
    }

  @Test
  fun testUnknownBlobs() =
    kotlinx.coroutines.test.runTest {
      // Treat blob1 as indexed
      setupMockEngine(unknownBlobs = setOf("blob2"))

      val event1 =
        CoordinationFileDetailsWithBlob(
          CoordinationFileDetails(
            virtualFile = mockk(relaxed = true) { every { toNioPath() } returns Path.of("foo.txt") },
            rootPath = "",
            relPath = "foo.txt",
          ),
          "blob1",
          System.currentTimeMillis(),
        )
      val event2 =
        CoordinationFileDetailsWithBlob(
          CoordinationFileDetails(
            virtualFile = mockk(relaxed = true) { every { toNioPath() } returns Path.of("bar.txt") },
            rootPath = "",
            relPath = "bar.txt",
          ),
          "blob2",
          System.currentTimeMillis(),
        )

      blobNamesChannel.send(event1)
      blobNamesChannel.send(event2)

      manager.startProcessing()

      waitForAssertion({
        assertEquals(1, findMissingRequests.size)
      }, timeoutMs = 5000)

      val firstRequest = findMissingRequests.first()
      assertEquals(setOf("blob1", "blob2"), firstRequest.memObjectNames.toSet())

      var changesResult: BlobChangeEvent? = null
      waitForAssertion({
        changesResult = changesChannel.tryReceive().getOrNull()
        assertNotNull(changesResult)
      }, timeoutMs = 5000)
      assertEquals("blob1", (changesResult as BlobAddedEvent).remoteBlobName)

      waitForAssertion({
        assertEquals(1, uploadChannel.approximateSize)
      })
      var uploadResult: FileToUpload? = null
      waitForAssertion({
        uploadResult = uploadChannel.tryReceive().getOrNull()
        assertNotNull(uploadResult)
      }, timeoutMs = 5000)
      assertEquals("blob2", uploadResult!!.expectedBlobName)
    }

  @Test
  fun testBatchSizeLimit() =
    kotlinx.coroutines.test.runTest {
      setupMockEngine()

      // Ensure the batch size is 2, if it changes, this test needs updating
      assertEquals(2, batchSizeInTest)

      // Send more events than batch size
      for (i in 1..batchSizeInTest * 2) {
        val event =
          CoordinationFileDetailsWithBlob(
            CoordinationFileDetails(
              virtualFile = mockk(relaxed = true) { every { toNioPath() } returns Path.of("file$i.txt") },
              rootPath = "",
              relPath = "file$i.txt",
            ),
            "blob$i",
            System.currentTimeMillis(),
          )
        blobNamesChannel.send(event)
      }

      manager.startProcessing()

      waitForAssertion({
        assert(findMissingRequests.size > 1)
      }, timeoutMs = 5000)

      val result = findMissingRequests.first().memObjectNames
      // Should only process up to batch size
      assertEquals(setOf("blob1", "blob2"), result.toSet())
    }

  @Test
  fun testErrorBackoff() =
    kotlinx.coroutines.test.runTest {
      setupThrowingEngine(429)

      // Send blob events
      val event1 =
        CoordinationFileDetailsWithBlob(
          CoordinationFileDetails(
            virtualFile = mockk(relaxed = true) { every { toNioPath() } returns Path.of("foo.txt") },
            rootPath = "",
            relPath = "foo.txt",
          ),
          "blob1",
          System.currentTimeMillis(),
        )
      val event2 =
        CoordinationFileDetailsWithBlob(
          CoordinationFileDetails(
            virtualFile = mockk(relaxed = true) { every { toNioPath() } returns Path.of("bar.txt") },
            rootPath = "",
            relPath = "bar.txt",
          ),
          "blob2",
          System.currentTimeMillis(),
        )

      blobNamesChannel.send(event1)
      blobNamesChannel.send(event2)

      manager.startProcessing()

      // Force a batch to be processed
      advanceTimeBy(maxWaitTimeInTest)

      waitForAssertion({
        assertEquals(1, findMissingRequests.size)
      }, timeoutMs = 5000)

      // All blobs should be returned as not indexed since remoteUploadState is empty
      assertEquals(2, blobNamesChannel.approximateSize)

      // Wait for backoff time
      advanceTimeBy(backendHealthBackoffTimeInTest - 1.seconds)

      // Ensure a new request hasn't been made
      assertEquals(1, findMissingRequests.size)

      // Ensure we exceed the backoff time
      advanceTimeBy(2.seconds)

      // Ensure a new request has been made
      waitForAssertion({
        assertEquals(2, findMissingRequests.size)
      }, timeoutMs = 5000)
    }

  @Test
  fun testIndexedBlobsWithBlobUpdatedEvent() =
    kotlinx.coroutines.test.runTest {
      // Treat blob1 as indexed
      setupMockEngine(nonIndexedBlobs = setOf("blob2"))

      val event1 =
        CoordinationFileDetailsWithBlob(
          CoordinationFileDetails(
            virtualFile = mockk(relaxed = true) { every { toNioPath() } returns Path.of("foo.txt") },
            rootPath = "",
            relPath = "foo.txt",
          ),
          "blob1",
          System.currentTimeMillis(),
        )
      val event2 =
        CoordinationFileDetailsWithBlob(
          CoordinationFileDetails(
            virtualFile = mockk(relaxed = true) { every { toNioPath() } returns Path.of("bar.txt") },
            rootPath = "",
            relPath = "bar.txt",
          ),
          "blob2",
          System.currentTimeMillis(),
        )

      // Pre-populate BlobNameService with an existing blob for the first file
      val blobNameService = BlobNameService.getInstance(project)
      val path1 = java.nio.file.Paths.get(event1.fileDetails.rootPath, event1.fileDetails.relPath)
      val oldBlobName = "old-blob-name"
      blobNameService.put(oldBlobName, path1.toString())

      blobNamesChannel.send(event1)
      blobNamesChannel.send(event2)

      manager.startProcessing()

      advanceTimeBy(maxWaitTimeInTest)

      waitForAssertion({
        assertEquals(1, findMissingRequests.size)
      }, timeoutMs = 5000)

      val firstRequest = findMissingRequests.first()
      assertEquals(setOf("blob1", "blob2"), firstRequest.memObjectNames.toSet())

      var changesResult: BlobChangeEvent? = null
      waitForAssertion({
        changesResult = changesChannel.tryReceive().getOrNull()
        assertNotNull(changesResult)
      }, timeoutMs = 5000)

      // Verify we got a BlobUpdatedEvent since the blob already existed
      assertTrue(changesResult is BlobUpdatedEvent)
      val blobUpdatedEvent = changesResult as BlobUpdatedEvent
      assertEquals("blob1", blobUpdatedEvent.newBlobName)

      waitForAssertion({
        assert(findMissingRequests.size > 1)
      })
      val last = findMissingRequests.last()
      assertEquals(setOf("blob2"), last.memObjectNames.toSet())
    }
}
