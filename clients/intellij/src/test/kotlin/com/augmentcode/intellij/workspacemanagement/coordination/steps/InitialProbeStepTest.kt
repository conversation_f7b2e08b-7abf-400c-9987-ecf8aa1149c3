package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.index.QualifiedPathName
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.utils.IndexUtil
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobAddedEvent
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobChangeEvent
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobUpdatedEvent
import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.augmentcode.intellij.workspacemanagement.coordination.mtimecache.MTimeCache
import com.augmentcode.intellij.workspacemanagement.coordination.mtimecache.MTimeCacheEntry
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.google.protobuf.util.JsonFormat
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.LightVirtualFile
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi.FindMissingRequest
import public_api.PublicApi.FindMissingResponse

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(JUnit4::class)
class InitialProbeStepTest : AugmentBasePlatformTestCase() {
  private lateinit var filteredFilesChannel: RoughlySizedChannel<CoordinationFileDetails>
  private lateinit var uploadChannel: RoughlySizedChannel<FileToUpload>
  private lateinit var waitForIndexChannel: RoughlySizedChannel<CoordinationFileDetailsWithBlob>
  private lateinit var checkpointChannel: RoughlySizedChannel<BlobChangeEvent>
  private lateinit var testInitialProbeStep: InitialProbeStep
  private val gson = GsonUtil.createApiGson()

  override fun setUp() {
    super.setUp()

    // Create fresh channels for each test
    filteredFilesChannel = RoughlySizedChannel(Channel(Channel.UNLIMITED))
    uploadChannel = RoughlySizedChannel(Channel(Channel.UNLIMITED))
    waitForIndexChannel = RoughlySizedChannel(Channel(Channel.UNLIMITED))
    checkpointChannel = RoughlySizedChannel(Channel(Channel.UNLIMITED))

    // Set up plugin state
    augmentHelpers().forcePluginState(PluginState.ENABLED)

    // Mock IndexUtil methods
    mockkObject(IndexUtil)
    every { IndexUtil.expectedBlobName(any(), any()) } answers {
      "blob-${firstArg<String>()}-${secondArg<String>()}"
    }
    assertEquals(
      "blob-test.txt-test content",
      IndexUtil.expectedBlobName("test.txt", "test content"),
    )

    // Mock AugmentRoot
    mockkObject(AugmentRoot)
    every { AugmentRoot.findFile(any<String>()) } answers {
      val file: VirtualFile = mockk(relaxed = true)
      every { file.path } returns arg<String>(0)
      file
    }
    every { AugmentRoot.findQualifiedPathName(any(), any()) } answers {
      QualifiedPathName("", arg<VirtualFile>(1).path)
    }
  }

  override fun tearDown() {
    filteredFilesChannel.close()
    uploadChannel.close()
    waitForIndexChannel.close()
    checkpointChannel.close()

    unmockkObject(IndexUtil)
    super.tearDown()
  }

  @Test
  fun testRoutesUnknownBlobsToUploadChannel() =
    runTest {
      try {
        // Setup mock HTTP engine
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                println("mockengine: find-missing called")
                val findMissingResponse =
                  FindMissingResponse.newBuilder().apply {
                    addAllUnknownMemoryNames(listOf("blob-test.txt-test content"))
                    addAllNonindexedBlobNames(emptyList())
                  }.build()

                val content = JsonFormat.printer().print(findMissingResponse)
                println("mockengine: find-missing response: $content")
                respond(
                  content = content,
                  status = HttpStatusCode.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Create test file details
        val fileDetails = createCoordinationFileDetails("test.txt", "test content")

        // Mock IndexUtil methods
        every { IndexUtil.normalizedText(any()) } returns "test content"

        // Send file to input channel
        filteredFilesChannel.send(fileDetails)

        // Wait for processing to complete
        advanceUntilIdle()
        assertTrue(filteredFilesChannel.isEmpty)

        // Verify file was routed to upload channel
        advanceUntilIdle()
        val uploadedFile = uploadChannel.receive()
        assertNotNull(uploadedFile)
        assertEquals("test.txt", uploadedFile.fileDetails.relPath)
        assertEquals("blob-test.txt-test content", uploadedFile.expectedBlobName)

        // Verify other channels are empty
        assertTrue(waitForIndexChannel.isEmpty)
        assertTrue(checkpointChannel.isEmpty)

        // Verify find-missing was called
        assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/find-missing" })
      } finally {
        // Dispose the step to stop the background job and allow runTest to complete
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  @Test
  fun testRoutesNonIndexedBlobsToWaitForIndexChannel() =
    runTest {
      try {
        // Setup mock HTTP engine
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                val findMissingResponse =
                  FindMissingResponse.newBuilder().apply {
                    addAllUnknownMemoryNames(emptyList())
                    addAllNonindexedBlobNames(listOf("blob-test.txt-test content"))
                  }.build()

                respond(
                  content = JsonFormat.printer().print(findMissingResponse),
                  status = HttpStatusCode.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Create test file details
        val fileDetails = createCoordinationFileDetails("test.txt", "test content")

        // Mock IndexUtil methods
        every { IndexUtil.normalizedText(any()) } returns "test content"

        // Send file to input channel
        filteredFilesChannel.send(fileDetails)

        // Wait for processing to complete
        advanceUntilIdle()
        assertTrue(filteredFilesChannel.isEmpty)

        // Verify file was routed to wait for index channel
        advanceUntilIdle()
        val blobState = waitForIndexChannel.receive()
        assertEquals("test.txt", blobState.fileDetails.relPath)
        assertEquals("blob-test.txt-test content", blobState.remoteBlobName)

        // Verify other channels are empty
        assertTrue(uploadChannel.isEmpty)
        assertTrue(checkpointChannel.isEmpty)
      } finally {
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  @Test
  fun testRoutesKnownIndexedBlobsToCheckpointChannelWithBlobAddedEvent() =
    runTest {
      try {
        // Setup mock HTTP engine
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                val findMissingResponse =
                  FindMissingResponse().apply {
                    unknownMemoryNames = emptySet()
                    nonindexedBlobNames = emptySet()
                  }

                respond(
                  content = gson.toJson(findMissingResponse),
                  status = HttpStatusCode.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Create test file details
        val fileDetails = createCoordinationFileDetails("test.txt", "test content")

        // Mock IndexUtil methods
        every { IndexUtil.normalizedText(any()) } returns "test content"

        // Ensure BlobNameService has no existing blob for this path
        val blobNameService = BlobNameService.getInstance(project)
        val path = java.nio.file.Paths.get(fileDetails.rootPath, fileDetails.relPath)
        assertNull(blobNameService.getByPath(path.toString()))

        // Send file to input channel
        filteredFilesChannel.send(fileDetails)

        // Wait for processing to complete
        advanceUntilIdle()
        assertTrue(filteredFilesChannel.isEmpty)

        // Verify file was routed to checkpoint channel with BlobAddedEvent
        val blobChangeEvent = checkpointChannel.receive()
        assertTrue(blobChangeEvent is BlobAddedEvent)
        val blobAddedEvent = blobChangeEvent as BlobAddedEvent
        assertEquals("blob-test.txt-test content", blobAddedEvent.newBlobName)

        // Verify other channels are empty
        assertTrue(uploadChannel.isEmpty)
        assertTrue(waitForIndexChannel.isEmpty)
      } finally {
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  @Test
  fun testRoutesKnownIndexedBlobsToCheckpointChannelWithBlobUpdatedEvent() =
    runTest {
      try {
        // Setup mock HTTP engine
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                val findMissingResponse =
                  FindMissingResponse().apply {
                    unknownMemoryNames = emptySet()
                    nonindexedBlobNames = emptySet()
                  }

                respond(
                  content = gson.toJson(findMissingResponse),
                  status = HttpStatusCode.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Create test file details
        val fileDetails = createCoordinationFileDetails("test.txt", "test content")

        // Mock IndexUtil methods
        every { IndexUtil.normalizedText(any()) } returns "test content"

        // Pre-populate BlobNameService with an existing blob for this path
        val blobNameService = BlobNameService.getInstance(project)
        val path = java.nio.file.Paths.get(fileDetails.rootPath, fileDetails.relPath)
        val oldBlobName = "old-blob-name"
        blobNameService.put(oldBlobName, path.toString())

        // Send file to input channel
        filteredFilesChannel.send(fileDetails)

        // Wait for processing to complete
        advanceUntilIdle()
        assertTrue(filteredFilesChannel.isEmpty)

        // Verify file was routed to checkpoint channel with BlobUpdatedEvent
        val blobChangeEvent = checkpointChannel.receive()
        assertTrue(blobChangeEvent is BlobUpdatedEvent)
        val blobUpdatedEvent = blobChangeEvent as BlobUpdatedEvent
        assertEquals("blob-test.txt-test content", blobUpdatedEvent.newBlobName)

        // Verify other channels are empty
        assertTrue(uploadChannel.isEmpty)
        assertTrue(waitForIndexChannel.isEmpty)
      } finally {
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  @Test
  fun testDropsFilesWithNullBlobNames() =
    runTest {
      try {
        // Setup mock HTTP engine
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                val requestBody = request.body.toByteArray().decodeToString()
                val findRequest = gson.fromJson(requestBody, FindMissingRequest::class.java)

                // Should only receive one blob name (the second file)
                assertEquals(1, findRequest.memObjectNames.size)

                val findMissingResponse =
                  FindMissingResponse().apply {
                    unknownMemoryNames = emptySet()
                    nonindexedBlobNames = emptySet()
                  }

                respond(
                  content = gson.toJson(findMissingResponse),
                  status = HttpStatusCode.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Create test file details
        val fileDetails1 = createCoordinationFileDetails("test1.txt", "test content")
        val fileDetails2 = createCoordinationFileDetails("test2.txt", "test content")

        // Mock IndexUtil methods - return null for first file
        every { IndexUtil.normalizedText(fileDetails1.virtualFile) } returns null
        every { IndexUtil.normalizedText(fileDetails2.virtualFile) } returns "test content"

        // Send files to input channel
        filteredFilesChannel.send(fileDetails1)
        filteredFilesChannel.send(fileDetails2)

        // Wait for processing to complete
        advanceUntilIdle()
        assertTrue(filteredFilesChannel.isEmpty)

        // Verify only the second file was processed (routed to checkpoint channel)
        val blobChangeEvent = checkpointChannel.receive()
        assertTrue(blobChangeEvent is BlobAddedEvent)

        // Verify other channels are empty
        assertTrue(uploadChannel.isEmpty)
        assertTrue(waitForIndexChannel.isEmpty)
        assertTrue(checkpointChannel.isEmpty) // No more items

        // Verify find-missing was called with only one blob name
        assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/find-missing" })
      } finally {
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  @Test
  fun testHandlesApiFailure() =
    runTest {
      try {
        // Setup mock HTTP engine to fail
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                respond(
                  content = "Server error",
                  status = HttpStatusCode.InternalServerError,
                )
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Create test file details
        val fileDetails = createCoordinationFileDetails("test.txt", "test content")

        // Mock IndexUtil methods
        every { IndexUtil.normalizedText(any()) } returns "test content"

        // Send file to input channel
        filteredFilesChannel.send(fileDetails)

        // Wait for processing to complete
        advanceUntilIdle()

        // Verify file was requeued
        val requeuedFile = filteredFilesChannel.receive()
        assertEquals("test.txt", requeuedFile.relPath)

        // Verify no files were routed to other channels
        assertTrue(uploadChannel.isEmpty)
        assertTrue(waitForIndexChannel.isEmpty)
        assertTrue(checkpointChannel.isEmpty)
      } finally {
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  @Test
  fun testRoutesBatchWithAllThreeBlobTypes() =
    runTest {
      try {
        // Setup mock HTTP engine
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                val findMissingResponse =
                  FindMissingResponse.newBuilder().apply {
                    addAllUnknownMemoryNames(listOf("blob-unknown.txt-unknown content"))
                    addAllNonindexedBlobNames(listOf("blob-nonindexed.txt-nonindexed content"))
                    // known.txt will be neither unknown nor nonindexed (known and indexed)
                  }.build()

                respond(
                  content = JsonFormat.printer().print(findMissingResponse),
                  status = HttpStatusCode.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Create test file details for all three cases
        val unknownFile = createCoordinationFileDetails("unknown.txt", "unknown content")
        val nonindexedFile = createCoordinationFileDetails("nonindexed.txt", "nonindexed content")
        val knownFile = createCoordinationFileDetails("known.txt", "known content")

        // Mock IndexUtil methods
        every { IndexUtil.normalizedText(unknownFile.virtualFile) } returns "unknown content"
        every { IndexUtil.normalizedText(nonindexedFile.virtualFile) } returns "nonindexed content"
        every { IndexUtil.normalizedText(knownFile.virtualFile) } returns "known content"

        // Send all files to input channel
        filteredFilesChannel.send(unknownFile)
        filteredFilesChannel.send(nonindexedFile)
        filteredFilesChannel.send(knownFile)

        // Wait for processing to complete
        advanceUntilIdle()
        assertTrue(filteredFilesChannel.isEmpty)

        // Verify unknown blob was routed to upload channel
        val uploadedFile = uploadChannel.receive()
        assertEquals("unknown.txt", uploadedFile.fileDetails.relPath)
        assertEquals("blob-unknown.txt-unknown content", uploadedFile.expectedBlobName)

        // Verify non-indexed blob was routed to wait for index channel
        val blobState = waitForIndexChannel.receive()
        assertEquals("nonindexed.txt", blobState.fileDetails.relPath)
        assertEquals("blob-nonindexed.txt-nonindexed content", blobState.remoteBlobName)

        // Verify known and indexed blob was routed to checkpoint channel
        val blobChangeEvent = checkpointChannel.receive()
        assertTrue(blobChangeEvent is BlobAddedEvent)
        val blobAddedEvent = blobChangeEvent as BlobAddedEvent
        assertEquals("blob-known.txt-known content", blobAddedEvent.newBlobName)

        // Verify all channels are now empty (no more items)
        assertTrue(uploadChannel.isEmpty)
        assertTrue(waitForIndexChannel.isEmpty)
        assertTrue(checkpointChannel.isEmpty)

        // Verify find-missing was called exactly once
        assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/find-missing" })
      } finally {
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  @Test
  fun testHandlesBatchProcessing() =
    runTest {
      try {
        // Setup mock HTTP engine
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                val findMissingResponse =
                  FindMissingResponse.newBuilder().apply {
                    addAllUnknownMemoryNames(listOf("blob-test1.txt-content1", "blob-test3.txt-content3"))
                    addAllNonindexedBlobNames(listOf("blob-test2.txt-content2"))
                  }.build()

                respond(
                  content = JsonFormat.printer().print(findMissingResponse),
                  status = HttpStatusCode.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Create multiple test file details
        val fileDetails1 = createCoordinationFileDetails("test1.txt", "content1")
        val fileDetails2 = createCoordinationFileDetails("test2.txt", "content2")
        val fileDetails3 = createCoordinationFileDetails("test3.txt", "content3")

        // Mock IndexUtil methods
        every { IndexUtil.normalizedText(fileDetails1.virtualFile) } returns "content1"
        every { IndexUtil.normalizedText(fileDetails2.virtualFile) } returns "content2"
        every { IndexUtil.normalizedText(fileDetails3.virtualFile) } returns "content3"

        // Send files to input channel
        filteredFilesChannel.send(fileDetails1)
        filteredFilesChannel.send(fileDetails2)
        filteredFilesChannel.send(fileDetails3)

        // Wait for processing to complete
        advanceUntilIdle()
        assertTrue(filteredFilesChannel.isEmpty)

        // Verify files were routed correctly
        // blob1 and blob3 should go to upload channel (unknown)
        val uploadedFiles = mutableListOf<FileToUpload>()
        repeat(2) {
          val uploadedFile = uploadChannel.receive()
          uploadedFiles.add(uploadedFile)
        }
        assertTrue(uploadedFiles.any { it.fileDetails.relPath == "test1.txt" && it.expectedBlobName == "blob-test1.txt-content1" })
        assertTrue(uploadedFiles.any { it.fileDetails.relPath == "test3.txt" && it.expectedBlobName == "blob-test3.txt-content3" })

        // blob2 should go to wait for index channel (non-indexed)
        val blobState = waitForIndexChannel.receive()
        assertEquals("test2.txt", blobState.fileDetails.relPath)
        assertEquals("blob-test2.txt-content2", blobState.remoteBlobName)

        // Verify channels are now empty
        assertTrue(uploadChannel.isEmpty)
        assertTrue(waitForIndexChannel.isEmpty)
        assertTrue(checkpointChannel.isEmpty)
      } finally {
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  @Test
  fun testHandlesEmptyBatch() =
    runTest {
      try {
        // Setup mock HTTP engine that should never be called
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                fail("find-missing should not be called for empty batch")
                respond(content = "", status = HttpStatusCode.OK)
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Mock IndexUtil methods to return null (drops all files)
        every { IndexUtil.normalizedText(any()) } returns null

        // Send files that will all be dropped
        repeat(3) { i ->
          val fileDetails = createCoordinationFileDetails("test$i.txt", "content$i")
          filteredFilesChannel.send(fileDetails)
        }

        // Wait for processing to complete
        advanceUntilIdle()
        assertTrue(filteredFilesChannel.isEmpty)

        // Verify find-missing was never called (empty batch)
        assertEquals(0, mockEngine.requestHistory.count { it.url.encodedPath == "/find-missing" })

        // Verify all channels are empty
        assertTrue(uploadChannel.isEmpty)
        assertTrue(waitForIndexChannel.isEmpty)
        assertTrue(checkpointChannel.isEmpty)
      } finally {
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  @Test
  fun testMTimeCache_notCached() =
    runTest {
      try {
        // Setup mock HTTP engine
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                val findMissingResponse =
                  FindMissingResponse.newBuilder().apply {
                    addAllUnknownMemoryNames(listOf("blob-test.txt-test content"))
                    addAllNonindexedBlobNames(emptyList())
                  }.build()

                respond(
                  content = JsonFormat.printer().print(findMissingResponse),
                  status = HttpStatusCode.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Create test file details
        val fileDetails = createCoordinationFileDetails("test.txt", "test content")

        // Mock IndexUtil methods
        every { IndexUtil.normalizedText(any()) } returns "test content"

        // Confirm mtime cache is empty
        assertNull(MTimeCache.getInstance(project).get(fileDetails.virtualFile.path))

        // Send file to input channel
        filteredFilesChannel.send(fileDetails)

        // Wait for processing to complete
        advanceUntilIdle()
        assertTrue(filteredFilesChannel.isEmpty)

        // Verify file was routed to upload channel
        advanceUntilIdle()
        val uploadedFile = uploadChannel.receive()
        assertNotNull(uploadedFile)
        assertEquals("test.txt", uploadedFile.fileDetails.relPath)
        assertEquals("blob-test.txt-test content", uploadedFile.expectedBlobName)

        // Verify other channels are empty
        assertTrue(waitForIndexChannel.isEmpty)
        assertTrue(checkpointChannel.isEmpty)

        // Verify mtime cache was updated
        val cacheEntry = MTimeCache.getInstance(project).get(fileDetails.virtualFile.path)
        assertNotNull(cacheEntry)
        assertEquals(fileDetails.virtualFile.modificationStamp, cacheEntry!!.mtime)
        assertEquals("blob-test.txt-test content", cacheEntry.blobName)
      } finally {
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  @Test
  fun testMTimeCache_useCache() =
    runTest {
      try {
        // Setup mock HTTP engine
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                val findMissingResponse =
                  FindMissingResponse.newBuilder().apply {
                    addAllUnknownMemoryNames(listOf("mtime-cache-blob-name"))
                    addAllNonindexedBlobNames(emptyList())
                  }.build()

                respond(
                  content = JsonFormat.printer().print(findMissingResponse),
                  status = HttpStatusCode.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Create test file details
        val fileDetails = createCoordinationFileDetails("test.txt", "test content")

        // Mock IndexUtil methods
        every { IndexUtil.normalizedText(any()) } throws RuntimeException("Should not be called")

        // Confirm mtime cache is empty
        MTimeCache.getInstance(project).put(
          fileDetails.virtualFile.path,
          MTimeCacheEntry(
            fileDetails.virtualFile.modificationStamp,
            fileDetails.virtualFile.length,
            "mtime-cache-blob-name",
          ),
        )

        // Send file to input channel
        filteredFilesChannel.send(fileDetails)

        // Wait for processing to complete
        advanceUntilIdle()
        assertTrue(filteredFilesChannel.isEmpty)

        // Verify file was routed to upload channel
        advanceUntilIdle()
        val uploadedFile = uploadChannel.receive()
        assertNotNull(uploadedFile)
        assertEquals("test.txt", uploadedFile.fileDetails.relPath)
        assertEquals("mtime-cache-blob-name", uploadedFile.expectedBlobName)

        // Verify other channels are empty
        assertTrue(waitForIndexChannel.isEmpty)
        assertTrue(checkpointChannel.isEmpty)
      } finally {
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  @Test
  fun testMTimeCache_cacheOutOfDate() =
    runTest {
      try {
        // Setup mock HTTP engine
        val mockEngine =
          MockEngine { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/find-missing" -> {
                val findMissingResponse =
                  FindMissingResponse.newBuilder().apply {
                    addAllUnknownMemoryNames(listOf("blob-test.txt-test content"))
                    addAllNonindexedBlobNames(emptyList())
                  }.build()

                respond(
                  content = JsonFormat.printer().print(findMissingResponse),
                  status = HttpStatusCode.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create InitialProbeStep with test coroutine scope
        testInitialProbeStep =
          InitialProbeStep(
            project = project,
            scope = this,
            filteredFilesChannel = filteredFilesChannel,
            uploadChannel = uploadChannel,
            waitForIndexChannel = waitForIndexChannel,
            checkpointChannel = checkpointChannel,
          )
        testInitialProbeStep.startProcessing()

        // Create test file details
        val fileDetails = createCoordinationFileDetails("test.txt", "test content")

        // Mock IndexUtil methods
        every { IndexUtil.normalizedText(any()) } returns "test content"

        // Confirm mtime cache is empty
        MTimeCache.getInstance(project).put(
          fileDetails.virtualFile.path,
          MTimeCacheEntry(
            fileDetails.virtualFile.modificationStamp - 1,
            fileDetails.virtualFile.length,
            "old-blob-name",
          ),
        )

        // Send file to input channel
        filteredFilesChannel.send(fileDetails)

        // Wait for processing to complete
        advanceUntilIdle()
        assertTrue(filteredFilesChannel.isEmpty)

        // Verify file was routed to upload channel
        advanceUntilIdle()
        val uploadedFile = uploadChannel.receive()
        assertNotNull(uploadedFile)
        assertEquals("test.txt", uploadedFile.fileDetails.relPath)
        assertEquals("blob-test.txt-test content", uploadedFile.expectedBlobName)

        // Verify other channels are empty
        assertTrue(waitForIndexChannel.isEmpty)
        assertTrue(checkpointChannel.isEmpty)

        // Verify mtime cache was updated
        val cacheEntry = MTimeCache.getInstance(project).get(fileDetails.virtualFile.path)
        assertNotNull(cacheEntry)
        assertEquals(fileDetails.virtualFile.modificationStamp, cacheEntry!!.mtime)
        assertEquals("blob-test.txt-test content", cacheEntry.blobName)
      } finally {
        if (::testInitialProbeStep.isInitialized) {
          Disposer.dispose(testInitialProbeStep)
        }
      }
    }

  private fun createCoordinationFileDetails(
    fileName: String,
    content: String,
  ): CoordinationFileDetails {
    val virtualFile: VirtualFile = LightVirtualFile(fileName, content)
    return CoordinationFileDetails(
      virtualFile = virtualFile,
      rootPath = "/src",
      relPath = fileName,
    )
  }
}
