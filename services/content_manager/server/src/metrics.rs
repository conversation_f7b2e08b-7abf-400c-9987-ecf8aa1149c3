use lazy_static::lazy_static;
use prometheus::{
    opts, register_counter, register_counter_vec, register_histogram, register_histogram_vec,
    register_int_counter_vec, register_int_gauge_vec, Counter, CounterVec, Histogram, HistogramVec,
    IntCounterVec, IntGaugeVec,
};

lazy_static! {
    pub static ref CACHE_ENTRY_COUNT: IntGaugeVec = register_int_gauge_vec!(
        "au_content_manager_cache_entry_count",
        "Number of entries in the cache",
        &["cache_type"]
    )
    .expect("metric can be created");
    pub static ref CACHE_BYTES_COUNT: IntGaugeVec = register_int_gauge_vec!(
        "au_content_manager_cache_bytes_count",
        "Number of bytes in the cache",
        &["cache_type"]
    )
    .expect("metric can be created");

    /// Histogram of request latencies to content manager endpoints. This can also be used to
    /// calculate throughput because Prometheus histograms keep a count.
    pub static ref RESPONSE_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_rpc_latency_histogram",
        "Histogram of RPC latencies",
        &["service", "endpoint", "status_code", "request_source", "tenant_name"]
    )
    .expect("metric can be created");

    /// The number of requests initiated
    pub static ref STARTED_REQUESTS_COUNTER: IntCounterVec = register_int_counter_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_rpc_started_requests_total",
        "Total number of requests initiated",
        &["service", "endpoint"],
    )
    .expect("metric can be created");

    /// The number of requests completed
    pub static ref HANDLED_REQUESTS_COUNTER: IntCounterVec = register_int_counter_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_rpc_handled_requests_total",
        "Total number of requests completed",
        &["service", "endpoint", "status_code"],
    )
    .expect("metric can be created");

    /// The number of currently active requests
    pub static ref ACTIVE_REQUESTS_COLLECTOR: IntGaugeVec = register_int_gauge_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_rpc_active_requests_gauge",
        "The number of currently active requests",
        &["service", "endpoint", "tenant_name"],
    )
    .expect("metric can be created");

    /// The number of blobs uploaded per request
    pub static ref NUM_BLOBS_UPLOADED_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_content_manager_num_blobs_uploaded",
        "The number of blobs uploaded in a request",
        &["tenant_name"],
        vec![0.0, 1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0, 128.0, 256.0, 512.0, 1024.0, 2048.0, 4096.0, 8192.0],
    )
    .expect("metric can be created");

    /// Histogram of the total batch upload size
    pub static ref BATCH_UPLOAD_SIZE_BYTES_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_content_manager_batch_upload_size_bytes",
        "The total size of of a batch upload in bytes",
        &["tenant_name"],
        vec![500.0, 1000.0, 2000.0, 4000.0, 8000.0, 16000.0, 32000.0, 64000.0, 128000.0, 256000.0, 512000.0, 1024000.0, 2048000.0, 4096000.0, 8192000.0],
    )
    .expect("metric can be created");

    /// Histogram of blob sizes uploaded
    pub static ref UPLOAD_BLOB_SIZE_BYTES_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_content_manager_upload_blob_size_bytes",
        "The size of individual blobs uploaded in bytes",
        &["raw", "tenant_name"],
        vec![500.0, 1000.0, 2000.0, 4000.0, 8000.0, 16000.0, 32000.0, 64000.0, 128000.0, 256000.0, 512000.0, 1024000.0, 2048000.0, 4096000.0, 8192000.0],
    )
    .expect("metric can be created");

    pub static ref UPLOAD_BLOB_LIMITED_COUNTER: CounterVec = register_counter_vec!(
        opts!(
        "au_content_manager_upload_blob_rate_limit_counter",
            "The number of times a blob upload was limited"),
        &["status"]
    )
    .expect("metric can be created");

    pub static ref INFO_CACHE_COUNTER: CounterVec = register_counter_vec!(
        opts!(
        "au_content_manager_info_cache_counter",
            "Metrics about the blob info cache hits and misses"),
        &["status"]
    )
    .expect("metric can be created");

    pub static ref CONTENT_CACHE_COUNTER: CounterVec = register_counter_vec!(
        opts!(
        "au_content_manager_content_cache_counter",
            "Metrics about the blob content cache hits and misses"),
        &["status"]
    )
    .expect("metric can be created");

    pub static ref BATCH_CONTENT_CALLS: Histogram = register_histogram!(
        "au_content_manager_object_store_batch_content_calls",
        "Metrics the calls of batch_content in the object store"
    )
    .expect("metric can be created");

    pub static ref BATCH_CONTENT_BLOB_KEYS_COUNT: Histogram = register_histogram!(
        "au_content_manager_object_store_batch_content_blob_keys",
        "Metrics about the number of blob keys in batch_content calls in the object store",
        vec![0.0, 1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0, 128.0, 256.0, 512.0, 1024.0, 2048.0, 4096.0, 8192.0],
    )
    .expect("metric can be created");

    pub static ref BATCH_CONTENT_ROWS_COUNT: Histogram = register_histogram!(
        "au_content_manager_object_store_batch_content_rows",
        "Metrics about the number of rows in batch_content calls in the object store",
        vec![0.0, 1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0, 128.0, 256.0, 512.0, 1024.0, 2048.0, 4096.0, 8192.0],
    )
    .expect("metric can be created");

    pub static ref FIND_MISSING_BLOB_COUNT: Histogram = register_histogram!(
        "au_content_manager_find_missing_blob_count",
        "Metrics about the number of blobs in find_missing calls",
        vec![0.0, 1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0, 128.0, 256.0, 512.0, 1024.0, 2048.0, 4096.0, 8192.0],
    )
    .expect("metric can be created");

    pub static ref FIND_MISSING_UNKNOWN_BLOB_COUNT: Histogram = register_histogram!(
        "au_content_manager_find_missing_unknown_blob_count",
        "Metrics about the number of unkown blobs in find_missing calls",
        vec![0.0, 1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0, 128.0, 256.0, 512.0, 1024.0, 2048.0, 4096.0, 8192.0],
    )
    .expect("metric can be created");

    pub static ref UPLOAD_CONSISTENCY_ERROR_COUNT: Counter = register_counter!(
        "au_content_manager_upload_consistency_error_count",
        "The number of times a consistency error was detected when uploading a blob"
    )
    .expect("metric can be created");

    pub static ref BLOB_LIFETIME_EXTENSION_COUNTER: CounterVec = register_counter_vec!(
        opts!(
            "au_content_manager_blob_lifetime_extension_total",
            "Total number of blob lifetime extension attempts by result"
        ),
        &["result"]
    )
    .expect("metric can be created");
}
