use std::fmt::Display;

use chrono::DateTime;
use chrono::Utc;

use itertools::Itertools;
use prost_wkt_types::Timestamp;
use secrecy::{ExposeSecret, SecretString};
use BlobContentKey::Raw;
use BlobContentKey::Transformed;

use crate::proto::content_manager::BlobMetadata;

// simple class to capture a blob name
#[derive(Debug, PartialEq, Eq, Clone, Hash, PartialOrd, Ord)]
pub struct BlobName(String);

impl BlobName {
    pub fn new(blob_name: &str) -> Self {
        Self(blob_name.to_string())
    }

    pub fn inner(&self) -> &str {
        &self.0
    }
}

impl Display for BlobName {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl From<&str> for BlobName {
    fn from(value: &str) -> Self {
        BlobName(value.to_string())
    }
}

impl From<String> for BlobName {
    fn from(value: String) -> Self {
        BlobName(value)
    }
}

#[derive(Clone, PartialEq, Eq, Debug, Hash, PartialOrd, Ord)]
pub enum BlobContentKey {
    Raw(BlobName),
    Transformed(BlobName, String, String),
}

impl BlobContentKey {
    pub fn blob_name(&self) -> &BlobName {
        match self {
            Raw(blob_name) => blob_name,
            Transformed(blob_name, _, _) => blob_name,
        }
    }

    pub fn is_raw(&self) -> bool {
        match self {
            Raw(_) => true,
            Transformed(_, _, _) => false,
        }
    }
}

// allows to extract blob name, transformation key, and sub key strings from a blob content key.
//
// If the object is a Raw BlobContentKey, transformation_key and sub_key are set to the empty string
impl From<BlobContentKey> for (BlobName, String, String) {
    fn from(value: BlobContentKey) -> Self {
        match value {
            Raw(blob_name) => (blob_name, "".to_string(), "".to_string()),
            Transformed(blob_name, transformation_key, sub_key) => {
                (blob_name, transformation_key, sub_key)
            }
        }
    }
}

// allows to extract blob name, transformation key, and sub key strings from a blob content key.
//
// If the object is a Raw BlobContentKey, transformation_key and sub_key are set to the empty string
impl<'a> From<&'a BlobContentKey> for (&'a BlobName, &'a str, &'a str) {
    fn from(value: &'a BlobContentKey) -> Self {
        match value {
            Raw(blob_name) => (blob_name, "", ""),
            Transformed(blob_name, transformation_key, sub_key) => {
                (blob_name, transformation_key, sub_key)
            }
        }
    }
}

// allow to create a BlobContentKey from blob name, transformation key, and sub key strings.
//
// If the transformation key is empty, the a Raw BlobContentKey is returned, otherwise
// a Transformed BlobContentKey is returned
impl From<(&String, &String, &String)> for BlobContentKey {
    fn from(value: (&String, &String, &String)) -> Self {
        if value.1.is_empty() {
            Raw(BlobName::new(value.0))
        } else {
            Transformed(
                BlobName::new(value.0),
                value.1.to_string(),
                value.2.to_string(),
            )
        }
    }
}

impl From<(String, String, String)> for BlobContentKey {
    fn from(value: (String, String, String)) -> Self {
        if value.1.is_empty() {
            Raw(BlobName::new(&value.0))
        } else {
            Transformed(BlobName::new(&value.0), value.1, value.2)
        }
    }
}

impl From<(&BlobName, &str, &str)> for BlobContentKey {
    fn from(value: (&BlobName, &str, &str)) -> Self {
        if value.1.is_empty() {
            Raw(value.0.clone())
        } else {
            Transformed(value.0.clone(), value.1.to_string(), value.2.to_string())
        }
    }
}

impl From<BlobContentKey> for crate::proto::content_manager::BlobContentKey {
    fn from(value: BlobContentKey) -> Self {
        let (blob_name, transformation_key, sub_key) = value.into();
        Self {
            blob_name: blob_name.to_string(),
            transformation_key,
            sub_key,
        }
    }
}

// The CheckpointId represents the type of the checkpoint id used in the Blobs
// workspace format. It's an alias for BlobName, since it shares the same
// representation, but it may differ in the future.
pub type CheckpointId = BlobName;

#[derive(Clone, PartialEq, Eq, Debug, PartialOrd, Ord)]
pub struct InformedTransformation {
    pub transformation_key: String,

    // time the transformation key message was created.
    pub time: chrono::DateTime<Utc>,
}

// information about the blob or transformed blob
#[derive(Clone, Debug)]
pub struct BlobInfo {
    // sha-256 digest of the complete content
    pub digest: String,

    // size of the blob content in bytes
    pub size: usize,

    // key/value metadata of the blob
    pub metadata: Vec<(String, SecretString)>,

    // all the transformation keys that were informed about the blob.
    //
    // All known transformation keys should be informed when a raw blob is uploaded.
    // Transformation keys created after the blob was uploaded, will be informed
    // when the blob is used.
    pub informed_transformation_keys: Vec<InformedTransformation>,

    // all the transformation keys that have uploaded information about the blob.
    pub uploaded_transformation_keys: Vec<InformedTransformation>,

    // time the blob information was uploaded
    pub time: chrono::DateTime<Utc>,
}

impl BlobInfo {
    /// returns true if the transformation key was uploaded
    pub fn is_transformation_key_uploaded(&self, transformation_key: &str) -> bool {
        self.uploaded_transformation_keys
            .iter()
            .any(|e| e.transformation_key == transformation_key)
    }

    pub fn size_bytes(&self) -> usize {
        // 128 is an estimate for the size of the struct itself. Metadata is usually empty but each Vec and Str costs us 24 bytes of overhead.
        // 64 is an estimate of InformedTransformation size: 24 for String overhead plus the DateTime and the string contents proper.
        128 + 64
            * (self.informed_transformation_keys.len() + self.uploaded_transformation_keys.len())
    }
}

impl From<crate::proto::content_manager_store::InformedTransformation> for InformedTransformation {
    fn from(value: crate::proto::content_manager_store::InformedTransformation) -> Self {
        InformedTransformation {
            transformation_key: value.transformation_key,
            time: match value.time {
                Some(t) => chrono::DateTime::from_timestamp(t.seconds, t.nanos.try_into().unwrap())
                    .unwrap(),
                None => DateTime::<Utc>::default(),
            },
        }
    }
}

#[allow(clippy::all)]
impl Into<crate::proto::content_manager_store::InformedTransformation> for InformedTransformation {
    fn into(self) -> crate::proto::content_manager_store::InformedTransformation {
        crate::proto::content_manager_store::InformedTransformation {
            transformation_key: self.transformation_key,
            time: Some(Timestamp {
                seconds: self.time.timestamp(),
                nanos: 0,
            }),
        }
    }
}

impl From<crate::proto::content_manager_store::BlobInfo> for BlobInfo {
    fn from(value: crate::proto::content_manager_store::BlobInfo) -> Self {
        BlobInfo {
            digest: value.digest,
            size: value.size as usize,
            metadata: value
                .metadata
                .iter()
                .map(|e| (e.key.to_string(), SecretString::new(e.value.to_string())))
                .collect(),
            informed_transformation_keys: value
                .informed_transformation_keys
                .into_iter()
                .map(|e| e.into())
                .collect(),
            uploaded_transformation_keys: value
                .uploaded_transformation_keys
                .into_iter()
                .map(|e| e.into())
                .collect(),
            time: match value.time {
                Some(t) => chrono::DateTime::from_timestamp(t.seconds, t.nanos.try_into().unwrap())
                    .unwrap(),
                None => DateTime::<Utc>::default(),
            },
        }
    }
}

impl TryInto<crate::proto::content_manager::GetBlobInfoResponse> for BlobInfo {
    type Error = tonic::Status;

    fn try_into(self) -> Result<crate::proto::content_manager::GetBlobInfoResponse, tonic::Status> {
        Ok(crate::proto::content_manager::GetBlobInfoResponse {
            content_hash: self.digest,
            size: u64::try_from(self.size)
                .map_err(|_| tonic::Status::internal("Failed to cast size"))?,
            metadata: self
                .metadata
                .into_iter()
                .map(|(k, v)| BlobMetadata {
                    key: k,
                    value: v.expose_secret().to_string(),
                })
                .collect(),
            informed_transformation_keys: self
                .informed_transformation_keys
                .into_iter()
                .map(|e| e.transformation_key)
                .collect(),
            uploaded_transformation_keys: self
                .uploaded_transformation_keys
                .into_iter()
                .map(|e| e.transformation_key)
                .collect(),
            time: Some(Timestamp {
                seconds: self.time.timestamp(),
                nanos: 0,
            }),
        })
    }
}

#[allow(clippy::all)]
impl Into<crate::proto::content_manager_store::BlobInfo> for (BlobContentKey, BlobInfo) {
    fn into(self) -> crate::proto::content_manager_store::BlobInfo {
        let (blob_name, transformation_key, sub_key) = self.0.into();
        crate::proto::content_manager_store::BlobInfo {
            digest: self.1.digest,
            size: self.1.size as u32,
            metadata: self
                .1
                .metadata
                .iter()
                .map(|e| crate::proto::content_manager::BlobMetadata {
                    key: e.0.to_string(),
                    value: e.1.expose_secret().to_string(),
                })
                .collect(),
            informed_transformation_keys: self
                .1
                .informed_transformation_keys
                .into_iter()
                .map(|e| e.into())
                .collect_vec(),
            uploaded_transformation_keys: self
                .1
                .uploaded_transformation_keys
                .into_iter()
                .map(|e| e.into())
                .collect_vec(),
            blob_name: blob_name.to_string(),
            transformation_key,
            sub_key,
            time: Some(Timestamp {
                seconds: self.1.time.timestamp(),
                nanos: 0,
            }),
        }
    }
}

#[derive(Clone, PartialEq, Eq, Debug)]
pub enum ObjectId {
    BlobName(BlobName),
    #[allow(dead_code)]
    CheckpointId(CheckpointId),
}

impl Display for ObjectId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ObjectId::BlobName(blob_name) => write!(f, "Blob({})", blob_name),
            ObjectId::CheckpointId(checkpoint_id) => write!(f, "Checkpoint({})", checkpoint_id),
        }
    }
}

/// information about an object and the transformation keys that were informed about it
#[derive(Clone, PartialEq, Eq, Debug)]
pub struct ObjectTransformationKeyInfo {
    /// name of the object
    pub object_id: ObjectId,

    /// all the transformation keys that were informed about the object.
    pub transformation_keys: Vec<String>,
}
