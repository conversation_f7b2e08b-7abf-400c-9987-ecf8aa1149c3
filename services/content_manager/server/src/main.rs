use std::{net::SocketAddr, sync::Arc, time::Duration};

use crate::gcp_queue_manager::GcpQueueManagerImpl;
use crate::queue_manager::QueueManager;
use crate::rate_limiter::RateLimiter;
use clap::Parser;
use config::CliArguments;
use grpc_auth::{GrpcAuth, GrpcAuthMiddlewareLayer};
use grpc_metrics::MetricsMiddlewareLayer;
use grpc_tls_config::{get_client_tls_creds, get_server_tls_creds};
use object_store::ObjectStore;
use rate_limiter::{NoopRateLimiter, TokenBucketRateLimiter};
use request_insight_publisher::{RequestInsightPublisherConfig, RequestInsightPublisherImpl};
use struct_logging::setup_struct_logging;
use tenant_watcher_client::TenantWatcherClientImpl;
use token_exchange_client::{TokenExchangeClient, TokenExchangeClientImpl, TokenGrpc<PERSON><PERSON>};
use tokio::select;
use tokio::signal::unix::{signal, SignalKind};
use tonic::transport::Server;
use tonic_reflection::server::ServerReflectionServer;

use crate::metrics::{
    ACTIVE_REQUESTS_COLLECTOR, HANDLED_REQUESTS_COUNTER, RESPONSE_LATENCY_COLLECTOR,
    STARTED_REQUESTS_COUNTER,
};
use crate::{
    config::Config, content_manager_service::ContentManagerImpl,
    transformation_keys::TransformationKeyWatcher,
};

mod config;
mod content_manager_service;
mod gcp_object_store;
mod gcp_queue_manager;
mod metrics;
mod object_store;
mod queue_manager;
mod rate_limiter;
mod transformation_keys;
mod util;

pub mod proto {
    pub mod base {
        pub use blob_names_rs_proto::base::blob_names;
    }

    pub(crate) const FILE_DESCRIPTOR_SET: &[u8] =
        tonic::include_file_descriptor_set!("content_manager_descriptor");

    pub use content_manager_rs_proto::content_manager;

    pub mod content_manager_store {
        tonic::include_proto!("content_manager_store");
    }
}

#[cfg(feature = "use_jemalloc")]
#[global_allocator]
static ALLOC: tikv_jemallocator::Jemalloc = tikv_jemallocator::Jemalloc;

#[cfg(feature = "use_jemalloc_profiling")]
#[allow(non_upper_case_globals)]
#[export_name = "malloc_conf"]
pub static malloc_conf: &[u8] = b"prof:true,prof_active:true,lg_prof_sample:19\0";

async fn run_with_object_store<
    OS: ObjectStore + Send + Sync + 'static,
    QM: QueueManager + Send + Sync + 'static,
>(
    config: Config,
    content_manager: ContentManagerImpl<OS, QM>,
    queue_manager: Arc<QM>,
    token_exchange_client: Arc<dyn TokenExchangeClient + Send + Sync + 'static>,
) -> Result<(), Box<dyn std::error::Error>> {
    let (_health_reporter, health_service) = tonic_health::server::health_reporter();

    metrics_server::setup_default_metrics();
    // register and init runtime metrics. needs unstable tokio flag to work
    prometheus::default_registry()
        .register(Box::new(
            tokio_metrics_collector::default_runtime_collector(),
        ))
        .unwrap();

    let metrics_server = metrics_server::setup_metrics_http_server(
        &config.metrics_server_bind_address,
        config.metrics_server_port,
    )?;

    let debug_server = debug_server::setup_debug_http_server(
        &config.debug_server_bind_address,
        config.debug_server_port,
        cfg!(feature = "use_jemalloc_profiling"),
    )?;

    let client = kube::Client::try_default()
        .await
        .expect("Failed to start kube client");
    let watcher = TransformationKeyWatcher::new(client, &config.namespace, queue_manager);

    let server_tls_config =
        get_server_tls_creds(&config.server_mtls_config).expect("Failed to create TLS config");

    let addr: SocketAddr = config.bind_address.parse()?;
    let server = match server_tls_config {
        None => Server::builder(),
        Some(server_tls_config) => Server::builder()
            .tls_config(server_tls_config)
            .expect("Failed to create rpc server"),
    };

    let listener = tokio::net::TcpListener::bind(addr)
        .await
        .expect("Failed to bind");
    tracing::info!(
        "Listening on {:?}",
        listener.local_addr().expect("Failed to get local address")
    );

    // Set up central server
    let central_server_tls_config = get_server_tls_creds(&config.central_server_mtls_config)
        .expect("Failed to create central TLS config");

    let central_addr: SocketAddr = config.central_bind_address.parse()?;
    let central_server = match central_server_tls_config {
        None => Server::builder(),
        Some(central_server_tls_config) => Server::builder()
            .tls_config(central_server_tls_config)
            .expect("Failed to create central rpc server"),
    };

    let central_listener = tokio::net::TcpListener::bind(central_addr)
        .await
        .expect("Failed to bind central server");
    tracing::info!(
        "Central server listening on {:?}",
        central_listener
            .local_addr()
            .expect("Failed to get central local address")
    );

    let grpc_auth: Arc<dyn GrpcAuth + Send + Sync> = Arc::new(TokenGrpcAuth::new(
        token_exchange_client,
        vec![], // the scopes are handled by the handler functions
    ));

    let reflection_service: ServerReflectionServer<_> =
        tonic_reflection::server::Builder::configure()
            .register_encoded_file_descriptor_set(proto::FILE_DESCRIPTOR_SET)
            .build_v1()?;

    let mut sigterm_notifier = signal(SignalKind::terminate()).expect("handle SIGTERM");
    let mut central_sigterm_notifier = signal(SignalKind::terminate()).expect("handle SIGTERM");

    // When we clone this and run it in the normal and central ports, it should
    // only clone the grpc service and not the ContentManagerImpl. We need two
    // ports because that seems like the easiest way to support namespace and
    // central certs in tonic.
    let inner_server = content_manager.new_server();

    // We want this to share the same inner monitor hashmap, so create it once
    // and clone it later. Since the inner hashmap is wrapped in an Arc, it is
    // shared between clones.
    let metrics_middleware_layer = MetricsMiddlewareLayer::new(
        &RESPONSE_LATENCY_COLLECTOR,
        &ACTIVE_REQUESTS_COLLECTOR,
        &STARTED_REQUESTS_COUNTER,
        &HANDLED_REQUESTS_COUNTER,
    );

    let central_server = central_server
        .timeout(Duration::from_secs(300))
        // should be >= max_concurrent_streams to prevent deadlock, see hyperium/hyper#3559
        .concurrency_limit_per_connection(config.grpc_max_concurrent_streams.try_into().unwrap())
        .max_concurrent_streams(Some(config.grpc_max_concurrent_streams))
        .http2_keepalive_timeout(Some(Duration::from_secs(15)))
        .http2_keepalive_interval(Some(Duration::from_secs(15)))
        .tcp_keepalive(Some(Duration::from_secs(15)))
        .trace_fn(tracing_tonic::server::trace_fn)
        .layer(
            tower::ServiceBuilder::new()
                .layer(metrics_middleware_layer.clone())
                .layer(GrpcAuthMiddlewareLayer::new(grpc_auth.clone()))
                .into_inner(),
        )
        .add_service(inner_server.clone())
        .add_service(health_service.clone())
        .add_service(reflection_service.clone())
        .serve_with_incoming_shutdown(
            tokio_stream::wrappers::TcpListenerStream::new(central_listener),
            async move {
                central_sigterm_notifier.recv().await;
            },
        );

    let server = server
        .timeout(Duration::from_secs(300))
        // should be >= max_concurrent_streams to prevent deadlock, see hyperium/hyper#3559
        .concurrency_limit_per_connection(config.grpc_max_concurrent_streams.try_into().unwrap())
        .max_concurrent_streams(Some(config.grpc_max_concurrent_streams))
        .http2_keepalive_timeout(Some(Duration::from_secs(15)))
        .http2_keepalive_interval(Some(Duration::from_secs(15)))
        .tcp_keepalive(Some(Duration::from_secs(15)))
        .trace_fn(tracing_tonic::server::trace_fn)
        .layer(
            tower::ServiceBuilder::new()
                .layer(metrics_middleware_layer)
                .layer(GrpcAuthMiddlewareLayer::new(grpc_auth))
                .into_inner(),
        )
        .add_service(inner_server)
        .add_service(health_service)
        .add_service(reflection_service)
        .serve_with_incoming_shutdown(
            tokio_stream::wrappers::TcpListenerStream::new(listener),
            async move {
                sigterm_notifier.recv().await;
            },
        );

    if config.health_logger_frequency_s > 0.0 {
        // We are seeing some weird stalls and don't know exactly what is going on.
        // This is a bit of a hail mary to see if the task executor or logging is
        // getting stuck perhaps.
        tokio::spawn(async move {
            loop {
                tracing::info!(
                    "Health logging every {}s... ok",
                    config.health_logger_frequency_s
                );
                tokio::time::sleep(Duration::from_secs_f64(config.health_logger_frequency_s)).await;
            }
        });
    } else {
        tracing::info!("Health logging disabled");
    }

    select! {
        server = futures::future::join4(server, central_server, metrics_server, debug_server) => {
            panic!("servers done: {server:?}");
        },
        watcher = watcher.run() => {
            panic!("watcher failed: {watcher:?}");
        },
    };
}

async fn run(args: CliArguments) -> Result<(), Box<dyn std::error::Error>> {
    let config = Config::read(&args.config_file).expect("Failed to read config file");
    tracing::info!("{:?}", config);

    // Remove underscore on first use
    let _registry = feature_flags::new_registry();

    // Remove underscore on first use
    let feature_flags = feature_flags::setup(
        "content_manager",
        "0.0.0",
        config.feature_flags_sdk_key_path.as_ref(),
        config.dynamic_feature_flags_endpoint.as_deref(),
    )
    .await;

    let rate_limiter: Box<dyn RateLimiter + Send + Sync> =
        match &config.upload_notification_rate_limit {
            Some(config) => Box::new(TokenBucketRateLimiter::new(config.cache_size)),
            None => Box::new(NoopRateLimiter {}),
        };

    let queue_manager =
        Arc::new(GcpQueueManagerImpl::new(&config, rate_limiter, feature_flags.clone()).await);

    let audit_logger = Arc::new(audit::stdout_audit_logger());

    let request_insight_publisher_config =
        RequestInsightPublisherConfig::read(&args.request_insight_publisher_config_file)
            .expect("Failed to read publisher config file");
    let request_insight_publisher =
        Arc::new(RequestInsightPublisherImpl::new(request_insight_publisher_config).await);

    let object_store =
        Arc::new(gcp_object_store::GcpObjectStoreImpl::new(&config, feature_flags.clone()).await);

    // Create tenant watcher client
    let client_creds =
        get_client_tls_creds(&config.client_mtls_config).expect("Failed to create TLS config");
    let tenant_watcher_client = Arc::new(TenantWatcherClientImpl::new(
        &config.tenant_watcher_config.endpoint,
        client_creds,
        std::time::Duration::from_secs_f32(config.tenant_watcher_config.request_timeout_s),
    ));

    let central_client_tls_config = get_client_tls_creds(&config.central_client_mtls_config)
        .expect("Failed to create TLS config");

    let token_exchange_client: Arc<TokenExchangeClientImpl> =
        Arc::new(TokenExchangeClientImpl::new(
            &config.auth_config.token_exchange_endpoint,
            config.namespace.clone(),
            central_client_tls_config,
            Duration::from_secs_f32(config.auth_config.token_exchange_request_timeout_s),
        ));

    let content_manager = ContentManagerImpl::new(
        config.clone(),
        queue_manager.clone(),
        object_store,
        feature_flags,
        audit_logger,
        request_insight_publisher,
        tenant_watcher_client,
        token_exchange_client.clone(),
    );
    run_with_object_store(
        config,
        content_manager,
        queue_manager,
        token_exchange_client,
    )
    .await
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    setup_struct_logging().expect("Failed to setup logging");

    let args = CliArguments::parse();
    tracing::info!("{:?}", args);

    run(args).await
}
