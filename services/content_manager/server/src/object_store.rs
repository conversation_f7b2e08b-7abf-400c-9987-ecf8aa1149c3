use crate::proto::base::blob_names as blob_names_proto;
use crate::proto::content_manager::BatchDeleteBlobsResponse;
use crate::util::{BlobContentKey, BlobInfo, BlobName, CheckpointId, ObjectTransformationKeyInfo};
use async_trait::async_trait;
use blob_names::SortedBlobNameBytesVec;
use bytes::Bytes;
use chrono::{DateTime, Utc};
use prost_wkt_types::Timestamp;
use request_context::{RequestContext, TenantId};
use secrecy::{SecretString, SecretVec};

#[derive(Clone, PartialEq, Eq, Debug, PartialOrd, Ord)]
pub struct BlobContentsKey {
    pub blob_name: String,
    pub transformation_key: String,
    pub sub_key: String,
}

pub struct UploadContent {
    pub blob_key: BlobContentKey,
    pub metadata: Vec<(String, SecretString)>,
    pub content: SecretVec<u8>,
}

pub struct AnnIndexAssetKey {
    pub index_id: String,
    pub transformation_key: String,
    pub sub_key: String,
}

pub struct UploadAnnIndexAsset {
    pub key: AnnIndexAssetKey,
    pub data: Vec<u8>,
}

pub struct AnnIndexMapping {
    pub index_id: String,
    pub added_blobs: Vec<blob_names::BlobName>,
    pub removed_blobs: Vec<blob_names::BlobName>,
    pub timestamp: DateTime<Utc>,
}

pub struct AnnIndexMappingValue {
    pub index_id: String,
    pub added_blobs: Vec<blob_names::BlobName>,
    pub removed_blobs: Vec<blob_names::BlobName>,
}

pub struct AnnIndexMappingKey {
    pub transformation_key: String,
    pub checkpoint_id: CheckpointId,
}

pub struct AnnIndexBlobInfoValue {
    pub blob_name: blob_names::BlobName,
    pub chunk_count: u32,
}

pub struct UploadAnnIndexBlobInfosPayload {
    pub transformation_key: String,
    pub index_id: String,
    pub blob_infos: Vec<AnnIndexBlobInfoValue>,
}

pub enum TryNextResult {
    UnknownBlob(BlobContentKey),
    FinalContent(BlobContentKey, BlobInfo, Bytes),
}

#[async_trait]
pub trait BatchGetContentStream {
    async fn try_next(
        &mut self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
    ) -> Result<Option<TryNextResult>, tonic::Status>;
}

#[async_trait]
pub trait AnnIndexAssetStream {
    async fn try_next(
        &mut self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
    ) -> Result<Option<Vec<u8>>, tonic::Status>;
}

// trait representing the object store
#[async_trait]
pub trait ObjectStore {
    type BGCS<'a>: BatchGetContentStream + Send
    where
        Self: 'a;

    type AIAS: AnnIndexAssetStream + Send;

    /// upload a collection of new blobs.
    ///
    /// Returns a vector with an entry per blob in the input vector.
    /// Each entry contains of the blob and and a Option indicating if the blob existed.
    /// If the option is not set, this is a newly uploaded blob. If it is set, it is the blob info of
    /// the existing blob
    async fn batch_upload_blob(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blobs: Vec<UploadContent>,
        user_id: Option<&SecretString>,
    ) -> Result<Vec<(BlobContentKey, Option<BlobInfo>)>, tonic::Status>;

    /// returns a vector with an entry per blob key in the input vector.
    /// Each entry contains of the blob key and the blob info if the blob exists
    ///
    /// Note(dirk): This function should never return NotFound instead if should
    /// use the Option to indicate if the blob exists.
    async fn get_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_keys: &[BlobContentKey],
    ) -> Result<Vec<(BlobContentKey, Option<BlobInfo>)>, tonic::Status>;

    async fn upload_ann_index_asset(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        asset: UploadAnnIndexAsset,
    ) -> Result<(), tonic::Status>;

    async fn get_ann_index_asset(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        key: &AnnIndexAssetKey,
    ) -> Result<Self::AIAS, tonic::Status>;

    /// deletes a given blob and all its transformed content persistently
    async fn delete_blob(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_name: &BlobName,
        user_id: &SecretString,
        timestamp: Option<DateTime<Utc>>,
    ) -> Result<(), tonic::Status>;

    /// Deletes multiple blobs and all their transformed content persistently in a single batch operation.
    /// If ignore_index is true, will not delete the user index entries.
    async fn batch_delete_blobs(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        entries: &[(BlobName, SecretString, Option<DateTime<Utc>>)],
        ignore_index: bool,
    ) -> Result<BatchDeleteBlobsResponse, tonic::Status>;

    /// records which transformation keys were informed about the newly uploaded blob.
    ///
    /// Note (dirk): two calls on the same blobs of this function and record_uploaded_keys might race and overwrite each other
    /// If that (hopefully rare) cases happens, the next usage of the blob would generate
    /// an new notification. If that case is indeed rare, that is not a problem.
    ///
    async fn record_informed_keys(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        entries: Vec<ObjectTransformationKeyInfo>,
    ) -> Result<(), tonic::Status>;

    /// adds transformation keys to the list of uploaded transformation keys for a blob.
    ///
    /// Note (dirk): two calls on the same blobs of this function and record_informed_keys might race and overwrite each other.
    /// If that (hopefully rare) cases happens, the next usage of the blob would generate
    /// an new notification. If that case is indeed rare, that is not a problem.
    ///
    async fn record_uploaded_keys(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        entries: Vec<ObjectTransformationKeyInfo>,
    ) -> Result<(), tonic::Status>;

    // returns the content of a given blob
    //
    // if the transformation key is empty, the raw content is returned
    async fn get_blob_content(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_key: &BlobContentKey,
    ) -> Result<(BlobInfo, Bytes), tonic::Status>;

    // returns the contents of a batch of blobs or transformed blobs
    //
    // if the transformation key is empty, the raw content is returned
    async fn batched_get_blob_content<'a>(
        &'a self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_key: Vec<BlobContentKey>,
    ) -> Result<Self::BGCS<'a>, tonic::Status>;

    async fn list_blob_content_keys(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_name: &BlobName,
    ) -> Result<Vec<BlobContentsKey>, tonic::Status>;

    async fn get_blobs_from_checkpoint(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        checkpoint_id: &CheckpointId,
    ) -> Result<SortedBlobNameBytesVec, tonic::Status>;

    async fn checkpoint_blobs(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blobs: blob_names_proto::Blobs,
    ) -> Result<BlobName, tonic::Status>;

    async fn get_blobs_from_user(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        user_id: &SecretString,
        limit: Option<i64>,
        min_timestamp: Option<Timestamp>,
        max_timestamp: Option<Timestamp>,
    ) -> Result<Vec<(String, Option<Timestamp>)>, tonic::Status>;

    async fn set_ann_index_mapping(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        key: &AnnIndexMappingKey,
        value: AnnIndexMappingValue,
    ) -> Result<(), tonic::Status>;

    async fn get_ann_index_mappings(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        key: &AnnIndexMappingKey,
    ) -> Result<Vec<AnnIndexMapping>, tonic::Status>;

    // returns the blob infos of the blobs tracked by the specified ANN index.
    async fn get_ann_index_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        transformation_key: &str,
        index_id: &str,
    ) -> Result<Vec<AnnIndexBlobInfoValue>, tonic::Status>;

    // Uploads the blob infos tracked by the specified index.
    async fn upload_ann_index_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        payload: UploadAnnIndexBlobInfosPayload,
    ) -> Result<(), tonic::Status>;

    async fn extend_blobs_lifetime(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_names: &[BlobName],
        checkpoint_id: &CheckpointId,
    ) -> Result<(), tonic::Status>;

    async fn delete_stale_blobs(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        start_prefix: &str,
        end_prefix: &str,
    ) -> Result<(), tonic::Status>;
}
