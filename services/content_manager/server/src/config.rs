use clap::Parser;
use grpc_tls_config::TlsConfig;
use serde::{Deserialize, Serialize};
use std::{fs::File, path::Path};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct NotificationRetryPolicy {
    // minimal delay before re-publishing by pub/sub of a blob notification after the the ack deadline was exceeded
    pub min_backoff_seconds: i64,

    // maximal delay before re-publishing by pub/sub of a blob notification after the the ack deadline was exceeded
    // An exponential backoff between notification_min_retry_backoff_seconds and notification_max_retry_backoff_seconds is used
    pub max_backoff_seconds: i64,
}

impl NotificationRetryPolicy {
    pub fn validate(&self) -> Result<(), tonic::Status> {
        if self.min_backoff_seconds > self.max_backoff_seconds {
            return Err(tonic::Status::invalid_argument(
                "min_backoff_seconds must be smaller than max_backoff_seconds",
            ));
        }
        Ok(())
    }
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct UploadNotificationRateLimitConfig {
    pub cache_size: u64,
}

impl UploadNotificationRateLimitConfig {
    pub fn validate(&self) -> Result<(), tonic::Status> {
        if self.cache_size == 0 {
            return Err(tonic::Status::invalid_argument("cache_size must be > 0"));
        }
        Ok(())
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct GcpConfig {
    pub project_id: String,

    pub topic_prefix: String,
    pub subscription_prefix: String,

    // the ack deadline for the result subscription (in seconds)
    // a blob notification to a transformation key that isn't acked via an upload_transformed_content call
    // is retried
    pub notification_ack_deadline: u32,

    pub notification_retry: Option<NotificationRetryPolicy>,

    // the maximal number of pub/sub notifications to send in a single batch
    pub notification_batch_size: usize,

    // batch size in batch get operations
    pub batch_get_batch_size: usize,

    // minimal time a subscripe call is kept alive
    pub min_subscribe_ttl_seconds: u32,

    // maximal time a subscripe call is kept alive
    pub max_subscribe_ttl_seconds: u32,

    // number of rows to process in a single batch
    pub gc_batch_size: usize,

    // number of rows to delete in a single batch
    pub gc_deletion_batch_size: usize,
}

impl GcpConfig {
    pub fn validate(&self) -> Result<(), tonic::Status> {
        if let Some(notification_retry) = self.notification_retry.as_ref() {
            notification_retry.validate()?;
        }
        if self.notification_ack_deadline > 600 {
            return Err(tonic::Status::invalid_argument(
                "notification_ack_deadline must be <= 600",
            ));
        }
        Ok(())
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AuthConfig {
    pub token_exchange_endpoint: String,
    pub token_exchange_request_timeout_s: f32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TenantWatcherConfig {
    pub endpoint: String,
    pub request_timeout_s: f32,
}

/// structure representing the configuration information in the configuration file, i.e.
/// the configmap of the content manager pod.
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Config {
    // the address to bind the rpc server to, e.g. 0.0.0.0:50051
    pub bind_address: String,
    // the address to bind the rpc server to, for central namespace callers
    pub central_bind_address: String,
    pub namespace: String,

    pub feature_flags_sdk_key_path: Option<std::path::PathBuf>,

    pub dynamic_feature_flags_endpoint: Option<String>,

    // Configure the HTTP server that returns Prometheus metrics.
    pub metrics_server_bind_address: String,
    pub metrics_server_port: u16,

    // Configure the HTTP server that returns debuging info
    pub debug_server_bind_address: String,
    pub debug_server_port: u16,

    pub blob_info_cache_size: u64,  // # bytes
    pub checkpoint_cache_size: u64, // # bytes
    pub content_cache_size: u64,    // # bytes

    // populated if server MTLS should be used.
    pub server_mtls_config: Option<TlsConfig>,

    // populated if central server MTLS should be used.
    pub central_server_mtls_config: Option<TlsConfig>,

    // the maximal size per raw blob (in bytes)
    pub upload_raw_content_limit: usize,

    // maximal size for a single upload of a transformed blob (in bytes)
    pub upload_content_limit: usize,

    // the maximal size for all content in a batch upload (in bytes)
    pub batch_upload_content_limit: usize,

    // the maximal number of blobs in a batch upload
    pub batch_upload_blob_limit: usize,

    // the maximal number of blobs in a batch delete
    pub batch_delete_blob_limit: usize,

    pub object_store_mode: String,

    pub gcp: GcpConfig,

    pub checkpoints_length_limit: usize,

    // the threshold in seconds before refreshing the timestamp of a blob
    pub blob_timestamp_refresh_threshold: u64,

    // the time to live for a blob in seconds
    pub blob_ttl_seconds: u64,

    pub cell_bytes_limit: usize,

    // if true, we also do a a catchup check if a blob was reuploaded
    pub catchup_on_reupload: bool,

    // populated if client MTLS should be used.
    pub client_mtls_config: Option<TlsConfig>,

    // populated if central client MTLS should be used.
    pub central_client_mtls_config: Option<TlsConfig>,

    pub auth_config: AuthConfig,

    pub tenant_watcher_config: TenantWatcherConfig,

    pub bigtable_proxy_endpoint: String,

    pub upload_notification_rate_limit: Option<UploadNotificationRateLimitConfig>,

    pub health_logger_frequency_s: f64,

    // maximum number of entries to return from the get user blobs rpc
    pub get_user_blobs_limit: i64,

    // Timeout duration in seconds for bigtable proxy client
    pub bigtable_proxy_timeout_secs: f32,

    // maximum number of concurrent streams per gRPC connection
    pub grpc_max_concurrent_streams: u32,

    // interval in seconds between GC jobs (set to 0 to disable)
    pub gc_interval_seconds: u32,
}

impl Config {
    pub fn validate(&self) -> Result<(), tonic::Status> {
        self.gcp.validate()?;
        if let Some(rate_limit) = self.upload_notification_rate_limit.as_ref() {
            rate_limit.validate()?;
        }
        Ok(())
    }

    /// read the configuration from a file
    pub fn read(path: &Path) -> Result<Config, tonic::Status> {
        let file = File::open(path).map_err(|e| tonic::Status::internal(e.to_string()))?;

        let config: Config =
            serde_json::from_reader(file).map_err(|e| tonic::Status::internal(e.to_string()))?;

        config.validate()?;
        Ok(config)
    }
}

/// Search for a pattern in a file and display the lines that contain it.
#[derive(Parser, Debug)]
pub struct CliArguments {
    /// path to the configuration file
    #[arg(long)]
    pub config_file: std::path::PathBuf,

    /// path to the request insight publisher configuration file
    #[arg(long)]
    pub request_insight_publisher_config_file: std::path::PathBuf,
}
