package tasks

import (
	"context"
	"fmt"

	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	analyticsclient "github.com/augmentcode/augment/services/request_insight/analytics/client"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	GET_USER_BLOBS_BATCH_LIMIT = 10000
	MAX_TOTAL_BLOBS_LIMIT      = 1000000
)

// getCentralRequestContextWithToken creates a request context with a service
// token for background tasks where tenant-specific permissions are not
// required.
func getCentralRequestContextWithToken(
	ctx context.Context,
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient,
	scopes []tokenscopesproto.Scope,
) (*requestcontext.RequestContext, error) {
	serviceToken, err := tokenExchangeClient.GetSignedTokenForService(ctx, "", scopes)
	if err != nil {
		return nil, fmt.Errorf("failed to get service token: %w", err)
	}

	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(),
		requestcontext.NewRandomRequestSessionId(),
		"shredder-background-task",
		serviceToken,
	)

	return requestCtx, nil
}

// getTenantRequestContextWithToken creates a request context with a service
// token for background tasks for specific tenants.
func getTenantRequestContextWithToken(
	ctx context.Context,
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient,
	tenantID string,
	tenantNamespace string,
	scopes []tokenscopesproto.Scope,
) (*requestcontext.RequestContext, error) {
	serviceToken, err := tokenExchangeClient.GetSignedTokenForServiceWithNamespace(ctx, tenantID, tenantNamespace, scopes)
	if err != nil {
		return nil, fmt.Errorf("failed to get service token: %w", err)
	}

	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(),
		requestcontext.NewRandomRequestSessionId(),
		"shredder-background-task",
		serviceToken,
	)

	return requestCtx, nil
}

func getTenantHistoryForUser(
	ctx context.Context,
	analyticsClient analyticsclient.RequestInsightAnalyticsClient,
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient,
	userID string,
) ([]string, error) {
	requestCtx, err := getCentralRequestContextWithToken(ctx, tokenExchangeClient, []tokenscopesproto.Scope{
		tokenscopesproto.Scope_REQUEST_R, // For analytics queries
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get request context")
		return nil, fmt.Errorf("failed to get request context: %w", err)
	}

	tenantIDs, err := analyticsClient.GetUserTenantHistory(ctx, userID, requestCtx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get user tenant history")
		return nil, fmt.Errorf("failed to get user tenant history: %w", err)
	}

	log.Ctx(ctx).Info().Strs("tenant_ids", tenantIDs).Msgf("Found %d tenants for user", len(tenantIDs))

	return tenantIDs, nil
}

// blobInfo represents a blob with its metadata for deletion operations.
type blobInfo struct {
	Name      string
	Timestamp *timestamppb.Timestamp
}

// getUserBlobsFromContentManager retrieves all blobs for a user from content manager with
// pagination and deduplication. Processing is asynchronous to help avoid OOMs from large blob
// lists.
func getUserBlobsFromContentManager(
	ctx context.Context,
	contentManagerClient contentmanagerclient.ContentManagerClient,
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient,
	userID string,
	userEmail string,
	tenantID string,
	tenantNamespace string,
) (<-chan blobInfo, <-chan error) {
	blobChan := make(chan blobInfo, GET_USER_BLOBS_BATCH_LIMIT*2)
	errChan := make(chan error, 1)

	// Fetch blobs from ContentManager in a goroutine, so that the caller can start processing them as
	// they come in.
	go func() {
		defer close(blobChan)
		defer close(errChan)

		requestCtx, err := getTenantRequestContextWithToken(ctx, tokenExchangeClient, tenantID, tenantNamespace, []tokenscopesproto.Scope{
			tokenscopesproto.Scope_CONTENT_ADMIN, // For content manager operations
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to get request context")
			errChan <- fmt.Errorf("failed to get request context: %w", err)
			return
		}

		seen := make(map[string]bool) // set of blob names we've seen so far

		// Do a sort of manual pagination using the timestamps. We start with
		// an unbounded range, then lower the max_timestamp on subsequent calls.
		var maxTimestamp *int64
		for {
			log.Ctx(ctx).Info().
				Str("user_id", userID).
				Str("tenant_id", tenantID).
				Interface("max_timestamp", maxTimestamp).
				Msg("Making GetUserBlobs request")

			limit := uint32(GET_USER_BLOBS_BATCH_LIMIT)
			var maxTimestampProto *timestamppb.Timestamp
			if maxTimestamp != nil {
				maxTimestampProto = &timestamppb.Timestamp{Seconds: *maxTimestamp}
			}

			response, err := contentManagerClient.GetUserBlobs(
				ctx,
				userEmail,
				tenantID,
				requestCtx,
				&limit,
				nil, // minTimestamp
				maxTimestampProto,
			)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to get user blobs")
				errChan <- fmt.Errorf("failed to get user blobs: %w", err)
				return
			}

			if len(response) == 0 {
				// No blobs
				break
			}

			// Keys are reversed timestamps. "first" in the response = latest upload time, "last" in the response = earliest upload time
			firstTimestamp := response[0].Time.Seconds
			lastTimestamp := response[len(response)-1].Time.Seconds
			log.Ctx(ctx).Debug().
				Int("blob_count", len(response)).
				Int64("first_timestamp", firstTimestamp).
				Int64("last_timestamp", lastTimestamp).
				Msg("Received blobs")

			if firstTimestamp == lastTimestamp && len(response) == GET_USER_BLOBS_BATCH_LIMIT {
				errChan <- fmt.Errorf("all blobs in response have the same timestamp, unable to search for next page")
				return
			}

			for _, blob := range response {
				key := fmt.Sprintf("%s-%d", blob.BlobName, blob.Time.Seconds)
				if !seen[key] {
					blobChan <- blobInfo{
						Name:      blob.BlobName,
						Timestamp: blob.Time,
					}
					seen[key] = true
				}
			}

			// less than limit implies we're on the last page
			if len(response) < GET_USER_BLOBS_BATCH_LIMIT {
				log.Ctx(ctx).Info().
					Int("received_blobs", len(response)).
					Int("limit", GET_USER_BLOBS_BATCH_LIMIT).
					Msg("Received fewer blobs than the limit, stopping iteration")
				break
			}

			maxTimestamp = &lastTimestamp
		}
	}()

	return blobChan, errChan
}
