package tasks

import (
	"context"
	"time"

	teammanagementclient "github.com/augmentcode/augment/services/auth/central/client"
	authpb "github.com/augmentcode/augment/services/auth/central/server/proto"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	contentmanagerproto "github.com/augmentcode/augment/services/content_manager/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	analyticsclient "github.com/augmentcode/augment/services/request_insight/analytics/client"
	analyticsproto "github.com/augmentcode/augment/services/request_insight/analytics/proto"
	requestinsightcentralclient "github.com/augmentcode/augment/services/request_insight/central/client"
	ricentralpb "github.com/augmentcode/augment/services/request_insight/central/proto"
	twclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Test constants
const (
	testUserID    = "user123"
	testUserEmail = "<EMAIL>"
	testToken     = "test-token"
	tenantToken   = "tenant-token"
)

// Tenant IDs and configurations
const (
	enterpriseTenantID   = "enterprise-tenant"
	professionalTenantID = "professional-tenant"
	communityTenantID    = "community-tenant"

	enterpriseNamespace   = "enterprise-namespace"
	professionalNamespace = "professional-namespace"
	communityNamespace    = "community-namespace"
)

type mockShardedClientFactory[T any] struct {
	ShardedClientFactory[T]
	mock.Mock
}

func (m *mockShardedClientFactory[T]) GetClientForTenant(ctx context.Context, tenantID string) (T, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(T), args.Error(1)
}

func (m *mockShardedClientFactory[T]) Close() {
	m.Called()
}

// Mock ContentManagerClient
type mockContentManagerClient struct {
	contentmanagerclient.ContentManagerClient
	mock.Mock
}

func (m *mockContentManagerClient) GetUserBlobs(
	ctx context.Context,
	userEmail string,
	tenantID string,
	requestContext *requestcontext.RequestContext,
	limit *uint32,
	minTimestamp *timestamppb.Timestamp,
	maxTimestamp *timestamppb.Timestamp,
) ([]*contentmanagerproto.UploadInfo, error) {
	args := m.Called(ctx, userEmail, tenantID, requestContext, limit, minTimestamp, maxTimestamp)
	return args.Get(0).([]*contentmanagerproto.UploadInfo), args.Error(1)
}

func (m *mockContentManagerClient) BatchDeleteBlobs(
	ctx context.Context,
	blobUserTuples []*contentmanagerproto.BatchDeleteBlobsRequest_BlobUserPair,
	requestContext *requestcontext.RequestContext,
	tenantID string,
) (bool, error) {
	args := m.Called(ctx, blobUserTuples, requestContext, tenantID)
	return args.Bool(0), args.Error(1)
}

func (m *mockContentManagerClient) Close() {
	m.Called()
}

// Mock AnalyticsClient
type mockAnalyticsClient struct {
	analyticsclient.RequestInsightAnalyticsClient
	mock.Mock
}

func (m *mockAnalyticsClient) GetUserTenantHistory(
	ctx context.Context,
	userID string,
	requestContext *requestcontext.RequestContext,
) ([]string, error) {
	args := m.Called(ctx, userID, requestContext)
	return args.Get(0).([]string), args.Error(1)
}

func (m *mockAnalyticsClient) ForgetUser(
	ctx context.Context,
	userID string,
	userEmail string,
	startTime *time.Time,
	endTime time.Time,
	requestContext *requestcontext.RequestContext,
) (*analyticsproto.ForgetUserResponse, error) {
	args := m.Called(ctx, userID, userEmail, startTime, endTime, requestContext)
	return args.Get(0).(*analyticsproto.ForgetUserResponse), args.Error(1)
}

func (m *mockAnalyticsClient) Close() {
	m.Called()
}

// Mock TeamManagementClient
type mockTeamManagementClient struct {
	teammanagementclient.TeamManagementClient
	mock.Mock
}

func (m *mockTeamManagementClient) CancelSubscription(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	userID, tenantID string,
	cancelImmediately bool,
) error {
	args := m.Called(ctx, requestContext, userID, tenantID, cancelImmediately)
	return args.Error(0)
}

func (m *mockTeamManagementClient) DeleteAccount(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	userID, tenantID string,
) (*authpb.DeleteAccountResponse, error) {
	args := m.Called(ctx, requestContext, userID, tenantID)
	return args.Get(0).(*authpb.DeleteAccountResponse), args.Error(1)
}

// Mock TenantCache
func newMockTenantCache(tenantIDs []string) twclient.TenantCacheSync {
	mockTenantWatcherClient := new(twclient.MockTenantWatcherClient)
	for _, tenantID := range tenantIDs {
		var tenant *tenantproto.Tenant
		switch tenantID {
		case enterpriseTenantID:
			tenant = createEnterpriseTenant()
		case professionalTenantID:
			tenant = createProfessionalTenant()
		case communityTenantID:
			tenant = createCommunityTenant()
		default:
			tenant = &tenantproto.Tenant{
				Id:             tenantID,
				ShardNamespace: "test-namespace",
				Tier:           tenantproto.TenantTier_ENTERPRISE,
			}
		}
		mockTenantWatcherClient.Tenants = append(mockTenantWatcherClient.Tenants, tenant)
	}
	return twclient.NewTenantCacheSync(mockTenantWatcherClient)
}

func createTenantByTier(tier tenantproto.TenantTier) *tenantproto.Tenant {
	var tenantID, tenantNamespace string
	switch tier {
	case tenantproto.TenantTier_ENTERPRISE:
		tenantID = enterpriseTenantID
		tenantNamespace = enterpriseNamespace
	case tenantproto.TenantTier_PROFESSIONAL:
		tenantID = professionalTenantID
		tenantNamespace = professionalNamespace
	case tenantproto.TenantTier_COMMUNITY:
		tenantID = communityTenantID
		tenantNamespace = communityNamespace
	default:
		tenantID = "unknown-tenant"
		tenantNamespace = "unknown-namespace"
	}

	return &tenantproto.Tenant{
		Id:             tenantID,
		ShardNamespace: tenantNamespace,
		Tier:           tier,
	}
}

func createEnterpriseTenant() *tenantproto.Tenant {
	return createTenantByTier(tenantproto.TenantTier_ENTERPRISE)
}

func createProfessionalTenant() *tenantproto.Tenant {
	return createTenantByTier(tenantproto.TenantTier_PROFESSIONAL)
}

func createCommunityTenant() *tenantproto.Tenant {
	return createTenantByTier(tenantproto.TenantTier_COMMUNITY)
}

// Mock RequestInsightCentralClient
type mockRequestInsightCentralClient struct {
	requestinsightcentralclient.RequestInsightCentralClient
	mock.Mock
}

func (m *mockRequestInsightCentralClient) DeleteEventsForUser(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	userID string,
	startTime *time.Time,
	endTime time.Time,
) (*ricentralpb.DeleteEventsForUserResponse, error) {
	args := m.Called(ctx, requestContext, userID, startTime, endTime)
	return args.Get(0).(*ricentralpb.DeleteEventsForUserResponse), args.Error(1)
}

func (m *mockRequestInsightCentralClient) DeleteGcsExportedBlobs(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	requests []*ricentralpb.DeleteGcsExportedBlobsRequest,
) ([]*ricentralpb.DeleteGcsExportedBlobsResponse, error) {
	args := m.Called(ctx, requestContext, requests)
	return args.Get(0).([]*ricentralpb.DeleteGcsExportedBlobsResponse), args.Error(1)
}

func (m *mockRequestInsightCentralClient) Close() {
	m.Called()
}
