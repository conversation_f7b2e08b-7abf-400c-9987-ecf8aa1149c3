package tasks

import (
	"context"
	"errors"
	"testing"

	analyticsproto "github.com/augmentcode/augment/services/request_insight/analytics/proto"
	pb "github.com/augmentcode/augment/services/shredder/admin_proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func createAnalyticsTestExecutor() (*AnalyticsForgetUserExecutor, *mockAnalyticsClient, *tokenexchangeclient.MockTokenExchangeClient) {
	mockAnalytics := &mockAnalyticsClient{}
	mockTokenExchange := tokenexchangeclient.NewMockTokenExchangeClient()

	executor := NewAnalyticsForgetUserExecutor(
		mockAnalytics,
		mockTokenExchange,
	)

	return executor, mockAnalytics, mockTokenExchange
}

func createAnalyticsTaskDetails() *pb.TaskDetails {
	return &pb.TaskDetails{
		Task: &pb.TaskDetails_AnalyticsForgetUser{
			AnalyticsForgetUser: &pb.AnalyticsForgetUserTask{},
		},
	}
}

func TestNewAnalyticsForgetUserExecutor(t *testing.T) {
	executor, _, _ := createAnalyticsTestExecutor()
	require.NotNil(t, executor)
}

func TestAnalyticsExecute_InvalidTaskDetails(t *testing.T) {
	executor, _, _ := createAnalyticsTestExecutor()

	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, nil)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")

	// Test with wrong task details type
	wrongDetails := &pb.TaskDetails{
		Task: &pb.TaskDetails_ContentManagerDeleteBlobs{
			ContentManagerDeleteBlobs: &pb.ContentManagerDeleteBlobsTask{},
		},
	}
	_, err = executor.Execute(context.Background(), testUserID, testUserEmail, wrongDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")
}

func TestAnalyticsExecute_TokenExchangeFailure(t *testing.T) {
	executor, _, mockTokenExchange := createAnalyticsTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return("", errors.New("token exchange failed"))

	taskDetails := createAnalyticsTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to get request context")

	mockTokenExchange.AssertExpectations(t)
}

func TestAnalyticsExecute_ForgetUserFailure(t *testing.T) {
	executor, mockAnalytics, mockTokenExchange := createAnalyticsTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testToken, nil)

	mockAnalytics.On("ForgetUser", mock.Anything, testUserID, testUserEmail, mock.Anything, mock.Anything, mock.Anything).
		Return((*analyticsproto.ForgetUserResponse)(nil), errors.New("analytics forget user failed"))

	taskDetails := createAnalyticsTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to forget user in analytics")

	mockTokenExchange.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
}

func TestAnalyticsExecute_Success(t *testing.T) {
	executor, mockAnalytics, mockTokenExchange := createAnalyticsTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testToken, nil)

	mockAnalytics.On("ForgetUser", mock.Anything, testUserID, testUserEmail, mock.Anything, mock.Anything, mock.Anything).
		Return(&analyticsproto.ForgetUserResponse{RowsUpdated: 5}, nil)

	taskDetails := createAnalyticsTaskDetails()
	statusDetail, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.NoError(t, err)
	require.Equal(t, "Updated 5 rows", statusDetail)

	mockTokenExchange.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
}
