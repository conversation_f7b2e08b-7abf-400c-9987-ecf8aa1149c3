package main

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"strings"

	"github.com/gorilla/mux"
	"github.com/rs/zerolog/log"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	"github.com/augmentcode/augment/base/logging/audit"
	authclient "github.com/augmentcode/augment/services/auth/central/client"
	authcentralproto "github.com/augmentcode/augment/services/auth/central/server/proto"
	connectproto "github.com/augmentcode/augment/services/integrations/marketing_api/connectproto"
	marketingapiproto "github.com/augmentcode/augment/services/integrations/marketing_api/proto"
	connectforwarder "github.com/augmentcode/augment/services/lib/grpc/connect_forward"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	useridresolver "github.com/augmentcode/augment/services/lib/user_id_resolver"
	notificationproto "github.com/augmentcode/augment/services/notification/proto"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
)

type Config struct {
	Port                        int                     `json:"port"`
	HttpsServerKey              string                  `json:"https_server_key"`
	HttpsServerCert             string                  `json:"https_server_cert"`
	ClientMtls                  *tlsconfig.ClientConfig `json:"client_mtls"`
	TenantWatcherGrpcUrl        string                  `json:"tenant_watcher_grpc_url"`
	TokenExchangeGrpcUrl        string                  `json:"token_exchange_grpc_url"`
	AuthCentralGrpcUrl          string                  `json:"auth_central_grpc_url"`
	NotificationGrpcUrlTemplate string                  `json:"notification_grpc_url_template"`

	FeatureFlagsSdkKeyPath      string `json:"feature_flags_sdk_key_path"`
	DynamicFeatureFlagsEndpoint string `json:"dynamic_feature_flags_endpoint"`

	SigningKeyPath        string `json:"signing_key_path"`
	EnableSigningKeyCheck bool   `json:"enable_signing_key_check"`
}

func loadConfig(filename string) (*Config, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	var config Config
	if err := json.NewDecoder(file).Decode(&config); err != nil {
		return nil, err
	}
	log.Info().Msgf("Loaded config: %v", config)
	return &config, nil
}

func checkPath(r *http.Request) error {
	if r.URL.Path == "/" {
		return nil
	}
	if r.URL.Path[0] != '/' {
		return fmt.Errorf("path must start with /")
	}
	if strings.Contains(r.URL.Path[1:], "..") {
		return fmt.Errorf("path must not contain '..'")
	}
	return nil
}

func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("ok"))
}

type SigningKeyVerifier struct {
	SigningKey []byte
}

// verify checks the signature and timestamp headers and verifies the signature.
// This is compatible with Customer.io's webhook signature verification algorithm.
func (v *SigningKeyVerifier) verify(w http.ResponseWriter, r *http.Request) error {
	if r.URL.Path == "/health" {
		return nil
	}

	// Get the signature and timestamp headers
	xCIOSignature := r.Header.Get("X-CIO-Signature")
	xCIOTimestamp := r.Header.Get("X-CIO-Timestamp")

	if xCIOSignature == "" {
		return fmt.Errorf("missing X-CIO-Signature header")
	}
	if xCIOTimestamp == "" {
		return fmt.Errorf("missing X-CIO-Timestamp header")
	}

	// Parse timestamp
	timestamp, err := strconv.Atoi(xCIOTimestamp)
	if err != nil {
		return fmt.Errorf("invalid X-CIO-Timestamp header: %v", err)
	}

	// Read the request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return fmt.Errorf("failed to read request body: %v", err)
	}
	// Reset the body so it can be read again by the handler
	r.Body = io.NopCloser(strings.NewReader(string(body)))

	// Verify the signature using Customer.io's algorithm
	isValid, err := v.checkSignature(string(v.SigningKey), xCIOSignature, timestamp, body)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}
	if !isValid {
		return fmt.Errorf("signature verification failed: signature mismatch")
	}

	return nil
}

// checkSignature implements Customer.io's webhook signature verification algorithm
func (v *SigningKeyVerifier) checkSignature(webhookSigningSecret, xCIOSignature string, xCIOTimestamp int, requestBody []byte) (bool, error) {
	signature, err := hex.DecodeString(xCIOSignature)
	if err != nil {
		return false, err
	}

	mac := hmac.New(sha256.New, []byte(webhookSigningSecret))

	if _, err := mac.Write([]byte("v0:" + strconv.Itoa(xCIOTimestamp) + ":")); err != nil {
		return false, err
	}
	if _, err := mac.Write(requestBody); err != nil {
		return false, err
	}

	computed := mac.Sum(nil)

	if !hmac.Equal(computed, signature) {
		log.Error().Msg("Marketing API webhook signature didn't match")
		return false, nil
	}

	return true, nil
}

func (v *SigningKeyVerifier) funcFactory() (func(http.Handler) http.Handler, error) {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if err := v.verify(w, r); err != nil {
				log.Error().Err(err).Msg("Failed to verify request")
				http.Error(w, "Unauthorized", http.StatusUnauthorized)
				return
			}
			next.ServeHTTP(w, r)
		})
	}, nil
}

func main() {
	logging.SetupServerLogging()

	config, err := loadConfig("/config/config.json")
	if err != nil {
		log.Fatal().Msgf("Could not load config: %v", err)
	}

	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	_, err = featureflags.NewFeatureFlagHandleFromFile(config.FeatureFlagsSdkKeyPath,
		config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	// Create auth central client
	authCentralConn, err := grpc.Dial(config.AuthCentralGrpcUrl, grpc.WithTransportCredentials(centralClientCreds))
	if err != nil {
		log.Fatal().Msgf("Failed to connect to auth central: %v", err)
	}

	// Create token exchange client
	namespace := os.Getenv("POD_NAMESPACE")
	tokenExchangeClient, err := tokenexchangeclient.New(config.TokenExchangeGrpcUrl, namespace,
		centralClientCreds,
	)
	if err != nil {
		log.Fatal().Msgf("Failed to create token exchange client: %v", err)
	}

	// Setup tenant watcher client.
	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherGrpcUrl, grpc.WithTransportCredentials(centralClientCreds))
	tenantCache := tenantwatcherclient.NewTenantCacheSync(tenantWatcherClient)
	defer tenantCache.Close()

	authClient, err := authclient.New(config.AuthCentralGrpcUrl, grpc.WithTransportCredentials(centralClientCreds))
	if err != nil {
		log.Fatal().Msgf("Failed to create auth client: %v", err)
	}

	userIDResolver := useridresolver.NewUserIDResolver(
		useridresolver.DefaultConfig(),
		authClient,
		tenantCache,
	)

	connFactory := connectforwarder.NewShardedGrpcConnFactory(
		map[string]string{"notification.NotificationService": config.NotificationGrpcUrlTemplate},
		centralClientCreds,
	)

	forwarderFactory := connectforwarder.NewForwarderFactory()
	notificationForwarder := connectforwarder.NewServiceForwarderFactory(
		tokenExchangeClient,
		connFactory, notificationproto.File_services_notification_notification_proto, "NotificationService", "marketing_api")
	teamManagementForwarder := connectforwarder.NewServiceForwarderFactory(
		tokenExchangeClient,
		connectforwarder.NewSingleGrpcConnFactory(authCentralConn),
		authcentralproto.File_services_auth_central_server_auth_proto, "TeamManagementService", "marketing_api")
	forwarderFactory.AddService(notificationForwarder)
	forwarderFactory.AddService(teamManagementForwarder)
	if err := forwarderFactory.AddMethodsFromFileDescriptor(marketingapiproto.File_services_integrations_marketing_api_marketing_api_proto); err != nil {
		log.Fatal().Msgf("Failed to add marketing api methods: %v", err)
	}
	auditLogger := audit.NewDefaultAuditLogger()
	marketingApiHandler, err := NewMarketingApiServiceHandler(userIDResolver, forwarderFactory, auditLogger, tokenExchangeClient)
	if err != nil {
		log.Fatal().Msgf("Failed to create marketing api service handler: %v", err)
	}

	r := mux.NewRouter()
	r.HandleFunc("/health", healthCheck).Methods("GET")

	// Add ConnectRPC marketing api service routes
	marketingApiPath, marketingApiConnectHandler := connectproto.NewMarketingApiServiceHandler(marketingApiHandler)
	r.PathPrefix(marketingApiPath).Handler(marketingApiConnectHandler)

	r.NotFoundHandler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		log.Warn().Msgf("Not found: %s", r.URL.Path)
		http.Error(w, "Not Found", http.StatusNotFound)
	})

	if config.EnableSigningKeyCheck {
		// read signing key
		signingKey, err := os.ReadFile(config.SigningKeyPath)
		if err != nil {
			log.Fatal().Msgf("Failed to read signing key: %v", err)
		}
		signingKeyVerifier := &SigningKeyVerifier{
			SigningKey: signingKey,
		}
		signingKeyVerifierFunc, err := signingKeyVerifier.funcFactory()
		if err != nil {
			log.Fatal().Msgf("Failed to create signing key verifier: %v", err)
		}
		r.Use(signingKeyVerifierFunc)
	}

	srv := &http.Server{
		Handler: r,
		Addr:    fmt.Sprintf(":%d", config.Port),
	}
	log.Info().Msgf("Listening on %d", config.Port)

	if err := srv.ListenAndServeTLS(config.HttpsServerCert, config.HttpsServerKey); err != nil {
		log.Fatal().Msgf("Failed to listen: %v", err)
	}
}
