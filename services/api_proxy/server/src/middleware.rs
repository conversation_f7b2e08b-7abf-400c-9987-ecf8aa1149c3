use std::collections::HashSet;
use std::future::{ready, Ready};
use std::rc::Rc;
use std::sync::Arc;
use std::time::{Duration, Instant};

use actix_web::{
    body::MessageBody,
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    error::Error,
    rt::time::timeout,
    HttpMessage,
};
use futures::future::LocalBoxFuture;
use moka::sync::Cache;
use tracing::Span;

use crate::api_auth::{ApiAuth, User};
use crate::handler_utils::{parse_user_agent, CLI_ACCESS_ENABLED};
use crate::metrics::{RESPONSE_CODE_COLLECTOR, RESPONSE_LATENCY_COLLECTOR};
use crate::public_api_proto::ApiVersion;
use bigtable_proxy_client::BigtableProxyClient;
use request_context::{
    RequestContext, RequestId, RequestSessionId, RequestSource, TenantInfo, EMPTY_TENANT_ID,
    REQUEST_ID_HEADER_NAME,
};

#[cfg(test)]
use auth_query_client::MockAuthQueryClient;

// flag checking if a request id should be created
pub const CREATE_REQUEST_ID_IF_MISSING_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_create_request_id_if_missing", true);

pub const CHECK_TENANT_DATA_ACCESS_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_check_tenant_data_access", false);

pub const CLIENT_VERSION_BLOCKED_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_client_version_blocked", false);

// Create a request ID if one doesn't exist in req's headers
//
// Postcondition: req.headers() and req.extensions() contain the request ID
pub fn setup_request_id(
    req: &mut ServiceRequest,
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
) -> tonic::Result<RequestId> {
    use actix_web::http::header::{HeaderName, HeaderValue};
    use std::str::FromStr;

    // Create a request ID if one does not exist
    let id: RequestId = match RequestId::from_headers(req.headers(), REQUEST_ID_HEADER_NAME) {
        Some(id) => Ok(id),
        None => {
            if !CREATE_REQUEST_ID_IF_MISSING_FLAG.get_from(feature_flags) {
                Err(tonic::Status::invalid_argument("Missing request id"))
            } else {
                let id = RequestId::create_random();

                let headers = req.headers_mut();

                headers.insert(
                    HeaderName::from_str(REQUEST_ID_HEADER_NAME).unwrap(),
                    HeaderValue::from_str(id.to_string().as_str()).unwrap(),
                );

                Ok(id)
            }
        }
    }?;
    req.extensions_mut().insert::<RequestId>(id);

    Ok(id)
}

// Teach tracing-actix-web how to extract our request ID as well as suppress the one it generates.
pub struct AugmentRootSpanBuilder {}

impl tracing_actix_web::RootSpanBuilder for AugmentRootSpanBuilder {
    fn on_request_start(request: &ServiceRequest) -> Span {
        let id: String = match request.extensions().get::<RequestId>() {
            Some(extracted_id) => extracted_id.to_string(),
            None => String::from("none"),
        };

        let session_id = RequestSessionId::from_headers(request.headers());

        let span = tracing_actix_web::root_span!(
            request,
            augment.client_session_id = tracing::field::display(session_id),
            // we have to predeclare all properties
            // see https://docs.rs/tracing-actix-web/latest/tracing_actix_web/#macros
            tenant_name = tracing::field::Empty,
            opaque_user_id = tracing::field::Empty,
        );

        span.record("request_id", id);

        span
    }

    fn on_request_end<B: MessageBody>(
        span: Span,
        outcome: &Result<ServiceResponse<B>, actix_web::Error>,
    ) {
        tracing_actix_web::DefaultRootSpanBuilder::on_request_end(span, outcome);
        tracing::debug!("request end");
    }
}

// Metrics middleware boilerplate: actix middleware wrap() expects a factory
// that implements new_transform which can produce a Service impl from another
// Service.
pub struct MetricsMiddlewareFactory;

impl MetricsMiddlewareFactory {
    pub fn new() -> Self {
        MetricsMiddlewareFactory {}
    }
}

impl Default for MetricsMiddlewareFactory {
    fn default() -> Self {
        Self::new()
    }
}

impl<S, B> Transform<S, ServiceRequest> for MetricsMiddlewareFactory
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = MetricsMiddleware<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(MetricsMiddleware { service }))
    }
}

pub struct MetricsMiddleware<S> {
    service: S,
}

impl<S, B> Service<ServiceRequest> for MetricsMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let method = req.method().to_string();
        let uri = req.uri().path().to_string();
        let pattern = req.match_pattern();
        let user_agent = req
            .headers()
            .get("user-agent")
            .map(|h| h.to_str().unwrap_or("not-utf8"))
            // Extension user_agent looks like `Augment.vscode-augment/0.159.1 (darwin; arm64; 23.4.0) vscode/1.90.2`
            // but all we really want is `Augment.vscode-augment/0.159.1`
            .and_then(|s| s.split_whitespace().next())
            .unwrap_or("")
            .to_string();
        // TODO: test and return possible 406 based on version
        let api_version = req
            .headers()
            .get("x-api-version")
            .and_then(|h| h.to_str().ok())
            .and_then(|h| h.parse::<i32>().ok())
            .unwrap_or(ApiVersion::Unspecified.into());

        tracing::info!("{} {} v{}", method, uri, api_version);

        let start = Instant::now();
        let fut = self.service.call(req);
        Box::pin(async move {
            let res = fut.await;
            let duration = start.elapsed();
            let code = match &res {
                Ok(res) => res.response().status().as_u16(),
                Err(e) => e.as_response_error().status_code().as_u16(),
            };

            let tenant_name = res
                .as_ref()
                .ok()
                .and_then(|resp| {
                    resp.request()
                        .extensions()
                        .get::<TenantInfo>()
                        .map(|t| t.tenant_name.clone())
                })
                .unwrap_or("unknown".to_string());

            let request_source = res
                .as_ref()
                .ok()
                .and_then(|resp| {
                    resp.request()
                        .extensions()
                        .get::<RequestContext>()
                        .map(|rc| rc.request_source().to_string())
                })
                .unwrap_or("unknown".to_string());

            if let Some(pattern) = pattern {
                RESPONSE_CODE_COLLECTOR
                    .with_label_values(&[
                        &method,
                        &pattern,
                        &code.to_string(),
                        &user_agent,
                        &tenant_name,
                        &request_source,
                    ])
                    .inc();
                RESPONSE_LATENCY_COLLECTOR
                    .with_label_values(&[
                        &method,
                        &pattern,
                        &code.to_string(),
                        &user_agent,
                        &tenant_name,
                        &request_source,
                    ])
                    .observe(duration.as_secs_f64());
            }
            res
        })
    }
}

// Middleware to verify auth, rejecting the request if auth fails, and also
// initialize the RequestContext for this request. Any middleware or handler
// after this can assume that the request has been authenticated and has a
// usable RequestContext in req.extensions(), as well as a User
pub struct AuthCheckMiddlewareFactory {
    api_auth: Arc<ApiAuth>,
    exclude_paths: HashSet<String>,
}

impl AuthCheckMiddlewareFactory {
    pub fn new(api_auth: Arc<ApiAuth>, exclude_paths: Vec<String>) -> Self {
        let exclude_paths = exclude_paths.into_iter().collect();
        Self {
            api_auth,
            exclude_paths,
        }
    }
}

impl<S: 'static, B> Transform<S, ServiceRequest> for AuthCheckMiddlewareFactory
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = AuthCheckMiddleware<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(AuthCheckMiddleware {
            service: Rc::new(service),
            api_auth: self.api_auth.clone(),
            exclude_paths: self.exclude_paths.clone(),
        }))
    }
}

pub struct AuthCheckMiddleware<S> {
    // This is necessary to avoid lifetime issues
    service: Rc<S>,

    api_auth: Arc<ApiAuth>,
    exclude_paths: HashSet<String>,
}

impl<S, B> Service<ServiceRequest> for AuthCheckMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        if self.exclude_paths.contains(req.path()) {
            return Box::pin(self.service.call(req));
        }

        let svc = self.service.clone();
        let api_auth = self.api_auth.clone();

        Box::pin(async move {
            let req = req;
            let request_id = *req.extensions().get::<RequestId>().unwrap();
            let mut request_session_id = RequestSessionId::from_headers(req.headers());
            if request_session_id.is_empty() {
                request_session_id = RequestSessionId::new(request_id.uuid());
            }
            let user_agent = req
                .headers()
                .get("user-agent")
                .map(|h| h.to_str().unwrap_or("not-utf8"));
            let request_source = match user_agent {
                // research/eval/harness/systems/remote_lib.py
                Some("Augment-EvalHarness/0 (Regression Testing)") => RequestSource::Evaluation,
                // services/api_proxy/health_check/health_check.py
                Some("AugmentHealthCheck/0") => RequestSource::HealthCheck,
                // Assume anything else entering the public API is coming from a real client
                // TODO: more validation and/or granularity here?
                _ => RequestSource::Client,
            };
            let request_context =
                RequestContext::new(request_id, request_session_id, request_source, None);

            // Authenticate this request
            let client_addr = req
                .connection_info()
                .realip_remote_addr()
                .unwrap_or("")
                .to_owned();
            let (user, tenant_info, jwt) = api_auth
                .check(
                    req.headers(),
                    req.path(),
                    req.request(),
                    &request_context,
                    client_addr,
                )
                .await
                .map_err(|e| {
                    tracing::warn!("Auth check error: {}", e);
                    e
                })?;

            // reset request context with jwt
            let request_context: RequestContext =
                RequestContext::new(request_id, request_session_id, request_source, Some(jwt));

            let feature_flags = api_auth.get_feature_flags(&user, &tenant_info, req.request())?;

            if CLIENT_VERSION_BLOCKED_FLAG.get_from(&feature_flags) {
                tracing::warn!("Client version blocked via feature flag");
                return Err(actix_web::error::ErrorUpgradeRequired(
                    "Your client version is no longer supported. Please update your client to the latest version.",
                ));
            }

            // Check CLI access before proceeding
            if let Some(user_agent) = user_agent {
                if let Some(client_info) = parse_user_agent(user_agent) {
                    if let Some(client_type) = client_info.0 {
                        if client_type == "cli" && !CLI_ACCESS_ENABLED.get_from(&feature_flags) {
                            tracing::warn!("CLI access is disabled via feature flag");
                            return Err(actix_web::error::ErrorForbidden(
                                "CLI access is currently disabled",
                            ));
                        }
                    }
                }
            }

            // Stash request context and other relevant metadata into the extensions
            req.extensions_mut().insert::<Instant>(Instant::now());
            req.extensions_mut().insert::<User>(user);
            req.extensions_mut()
                .insert::<TenantInfo>(tenant_info.clone());
            req.extensions_mut()
                .insert::<RequestContext>(request_context);

            // 5m timeout to help ensure our auth doesn't expire out from under the request
            match timeout(Duration::from_secs(300), svc.call(req)).await {
                Ok(res) => res,
                Err(_) => {
                    tracing::warn!("Request exceeded auth middleware timeout");
                    Err(actix_web::error::ErrorRequestTimeout(
                        "Request exceeded auth middleware timeout",
                    ))
                }
            }
        })
    }
}

pub struct TenantDataAccessMiddlewareFactory {
    exclude_paths: HashSet<String>,
    tenant_cache: Arc<dyn tenant_watcher_client::TenantCacheClient + Send + Sync>,
    bigtable_proxy_client: Arc<dyn BigtableProxyClient>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
    tenant_access_cache: Cache<String, bool>,
}

impl TenantDataAccessMiddlewareFactory {
    pub fn new(
        exclude_paths: Vec<String>,
        tenant_cache: Arc<dyn tenant_watcher_client::TenantCacheClient + Send + Sync>,
        bigtable_proxy_client: Arc<dyn BigtableProxyClient>,
        feature_flags: feature_flags::FeatureFlagsServiceHandle,
    ) -> Self {
        let exclude_paths = exclude_paths.into_iter().collect();
        // Create a cache with 5-minute TTL
        let tenant_access_cache = Cache::builder()
            .time_to_live(Duration::from_secs(300))
            .build();

        Self {
            exclude_paths,
            tenant_cache,
            bigtable_proxy_client,
            feature_flags,
            tenant_access_cache,
        }
    }
}

impl<S: 'static, B> Transform<S, ServiceRequest> for TenantDataAccessMiddlewareFactory
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = TenantDataAccessMiddleware<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(TenantDataAccessMiddleware {
            service: Rc::new(service),
            exclude_paths: self.exclude_paths.clone(),
            tenant_cache: self.tenant_cache.clone(),
            bigtable_proxy_client: self.bigtable_proxy_client.clone(),
            feature_flags: self.feature_flags.clone(),
            tenant_access_cache: self.tenant_access_cache.clone(),
        }))
    }
}

pub struct TenantDataAccessMiddleware<S> {
    service: Rc<S>,
    exclude_paths: HashSet<String>,
    tenant_cache: Arc<dyn tenant_watcher_client::TenantCacheClient + Send + Sync>,
    bigtable_proxy_client: Arc<dyn BigtableProxyClient>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
    tenant_access_cache: Cache<String, bool>,
}

impl<S, B> Service<ServiceRequest> for TenantDataAccessMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        // Skip check if path is excluded
        if self.exclude_paths.contains(req.path()) {
            return Box::pin(self.service.call(req));
        }

        let tenant_info: TenantInfo = match req.extensions().get::<TenantInfo>().cloned() {
            Some(info) => info,
            None => {
                tracing::error!("TenantInfo missing from request extensions");
                return Box::pin(async {
                    Err(actix_web::error::ErrorInternalServerError(
                        "Internal server error: missing tenant info",
                    ))
                });
            }
        };

        let request_context = match req.extensions().get::<RequestContext>().cloned() {
            Some(ctx) => ctx,
            None => {
                tracing::error!("RequestContext missing from request extensions");
                return Box::pin(async {
                    Err(actix_web::error::ErrorInternalServerError(
                        "Internal server error: missing request context",
                    ))
                });
            }
        };

        // Skip check if not enabled by tenant feature flag or if binding fails
        let should_check = match self
            .feature_flags
            .bind_attribute("tenant_name", &tenant_info.tenant_name)
        {
            Ok(bound_flags) => CHECK_TENANT_DATA_ACCESS_FLAG.get_from(&bound_flags),
            Err(e) => {
                tracing::warn!(
                    "Failed to bind feature flags to tenant: {:?}, skipping check",
                    e
                );
                false
            }
        };
        if !should_check {
            return Box::pin(self.service.call(req));
        }

        let svc = self.service.clone();
        let bigtable_proxy_client = self.bigtable_proxy_client.clone();
        let tenant_cache = self.tenant_cache.clone();
        let tenant_access_cache = self.tenant_access_cache.clone();

        Box::pin(async move {
            // Check tenant data access
            if let Some(tenant_id) = &tenant_info.tenant_id {
                // Skip check for empty tenant ID
                if tenant_id != &EMPTY_TENANT_ID {
                    // Only check data access if tenant has encryption enabled
                    let tenant_id_str = tenant_id.to_string();

                    // Get tenant from cache to check if encryption is enabled
                    if let Some(tenant) = tenant_cache.get_tenant(&tenant_id_str).await {
                        if !tenant.encryption_key_name.is_empty() {
                            // Check cache first
                            if let Some(true) = tenant_access_cache.get(&tenant_id_str) {
                                // Access is already verified
                                tracing::debug!(
                                    "Using cached tenant data access for {}",
                                    tenant_id_str
                                );
                            } else {
                                match bigtable_proxy_client
                                    .check_tenant_data_access(&request_context, tenant_id)
                                    .await
                                {
                                    Ok(_) => {
                                        // Cache successful access
                                        tenant_access_cache.insert(tenant_id_str, true);
                                    }
                                    Err(e) => {
                                        tracing::warn!(
                                            "Tenant data access denied for tenant {}: {}",
                                            tenant_id,
                                            e
                                        );
                                        return Err(actix_web::error::ErrorForbidden(
                                            "Tenant encryption key access denied. Please contact your administrator.",
                                        ));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Continue with the request
            svc.call(req).await
        })
    }
}

#[cfg(test)]
mod tests {
    use actix_http::header;
    use actix_web::{http, test, web, App, HttpResponse};
    use mockall::predicate::*;
    use std::collections::HashMap;

    use crate::metrics::RESPONSE_LATENCY_COLLECTOR;
    use crate::middleware::{
        setup_request_id, AuthCheckMiddlewareFactory, MetricsMiddlewareFactory,
    };

    use super::*;

    use bigtable_proxy_client::MockBigtableProxyClient;
    use tenant_watcher_client::MockTenantCacheClient;

    #[actix_web::test]
    async fn test_http_metrics() {
        let app = test::init_service(
            App::new()
                .wrap(MetricsMiddlewareFactory::new())
                .service(web::resource("/test").to(|| async {
                    tokio::time::sleep(std::time::Duration::from_millis(200)).await;
                    "OK"
                }))
                .service(web::resource("/health").to(|| async { "OK" })),
        )
        .await;

        // Test that generic GET requests are ignored.
        let req = test::TestRequest::get().uri("/invalid").to_request();
        let _ = app.call(req).await;
        let histogram = RESPONSE_LATENCY_COLLECTOR
            .get_metric_with_label_values(&["GET", "/invalid", "200", "", "unknown", "unknown"])
            .unwrap();
        assert_eq!(histogram.get_sample_count(), 0);

        // Test that GET /health requests aren't ignored.
        let req = test::TestRequest::get().uri("/health").to_request();
        let _ = app.call(req).await;
        let histogram = RESPONSE_LATENCY_COLLECTOR
            .get_metric_with_label_values(&["GET", "/health", "200", "", "unknown", "unknown"])
            .unwrap();
        assert_eq!(histogram.get_sample_count(), 1);

        // Test a POST request.
        let req = test::TestRequest::post().uri("/test").to_request();
        let _ = app.call(req).await;
        let histogram = RESPONSE_LATENCY_COLLECTOR
            .get_metric_with_label_values(&["POST", "/test", "200", "", "unknown", "unknown"])
            .unwrap();
        assert_eq!(histogram.get_sample_count(), 1);
        let latency_sec = histogram.get_sample_sum();
        assert!(latency_sec >= 0.2);
        // Should normally be very close to 0.2 but the sleep could cause scheduling delays
        assert!(latency_sec < 0.5);

        // Test with a specific request source
        let req = test::TestRequest::post().uri("/test").to_request();
        let request_context = RequestContext::new(
            RequestId::create_random(),
            RequestSessionId::new(uuid::Uuid::new_v4()),
            RequestSource::Client,
            None,
        );
        req.extensions_mut().insert(request_context);
        let _ = app.call(req).await;
        let histogram = RESPONSE_LATENCY_COLLECTOR
            .get_metric_with_label_values(&["POST", "/test", "200", "", "unknown", "client"])
            .unwrap();
        assert_eq!(histogram.get_sample_count(), 1);
        let latency_sec = histogram.get_sample_sum();
        assert!(latency_sec >= 0.2);
        assert!(latency_sec < 0.5);
    }

    #[actix_web::test]
    async fn test_auth_check() {
        let api_auth = Arc::new(ApiAuth::new_for_test(HashMap::from([(
            "token1".to_string(),
            "123".to_string(),
        )])));
        let fake_feature_flags = feature_flags::setup_local();
        let app = test::init_service(
            App::new()
                .wrap(AuthCheckMiddlewareFactory::new(
                    api_auth.clone(),
                    vec!["/health".to_string()],
                ))
                .wrap_fn(move |mut req, srv| {
                    setup_request_id(&mut req, &fake_feature_flags).unwrap();
                    srv.call(req)
                })
                .service(web::resource("/test").to(HttpResponse::Ok))
                .service(web::resource("/health").to(HttpResponse::Ok)),
        )
        .await;

        // no auth should fail
        let req = test::TestRequest::with_uri("/test").to_request();
        let resp = app.call(req).await;
        assert_eq!(
            resp.expect_err("expected auth failure")
                .as_response_error()
                .status_code(),
            http::StatusCode::UNAUTHORIZED
        );

        // no auth to /health should pass, though
        let req = test::TestRequest::with_uri("/health").to_request();
        let resp = app.call(req).await;
        assert_eq!(
            resp.expect("expected success").status(),
            http::StatusCode::OK
        );

        // bad auth should fail
        let req = test::TestRequest::with_uri("/test")
            .insert_header((header::AUTHORIZATION, "Bearer tokenUnknown"))
            .to_request();
        let resp = app.call(req).await;
        assert_eq!(
            resp.expect_err("expected auth failure")
                .as_response_error()
                .status_code(),
            http::StatusCode::UNAUTHORIZED
        );

        // good auth should pass
        let req = test::TestRequest::with_uri("/test")
            .insert_header((header::AUTHORIZATION, "Bearer token1"))
            .to_request();
        let resp = app.call(req).await;
        assert_eq!(
            resp.expect("expected success").status(),
            http::StatusCode::OK
        );
    }

    // Simple handler for testing
    async fn test_handler() -> HttpResponse {
        HttpResponse::Ok().body("success")
    }

    async fn run_tenant_data_access_test(
        feature_flag_value: bool,
        tenant_id: &str,
        tenant_name: &str,
        encryption_key: &str,
        expect_bigtable_call: bool,
        bigtable_error: Option<tonic::Status>,
        expected_status: http::StatusCode,
    ) {
        // Setup feature flags
        let feature_flags = feature_flags::setup_local();
        CHECK_TENANT_DATA_ACCESS_FLAG.set_local(&feature_flags, feature_flag_value);

        // Create a shared mock that can be accessed from the middleware
        let mut mock_bigtable = MockBigtableProxyClient::new();

        // Create a shared counter to track bigtable calls
        let call_counter = Arc::new(std::sync::atomic::AtomicUsize::new(0));

        // Configure the bigtable mock
        {
            let error = bigtable_error.clone();
            let counter = call_counter.clone();

            mock_bigtable
                .expect_check_tenant_data_access()
                .returning(move |_, _| {
                    // Increment the counter when called
                    counter.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
                    if let Some(err) = error.clone() {
                        Err(err)
                    } else {
                        Ok(())
                    }
                });
        }

        // Create a tenant cache mock
        let mut mock_tenant_cache = MockTenantCacheClient::new();

        // Setup tenant cache to return appropriate tenant info
        let tenant = tenant_watcher_client::tenant_watcher::Tenant {
            id: tenant_id.to_string(),
            name: tenant_name.to_string(),
            encryption_key_name: encryption_key.to_string(),
            ..Default::default()
        };

        // Configure the tenant cache mock
        mock_tenant_cache
            .expect_get_tenant()
            .with(eq(tenant_id.to_string()))
            .returning(move |_| Some(tenant.clone()));

        // Create a wrapper for the bigtable client
        let bigtable_client = Arc::new(mock_bigtable);
        let tenant_cache_client = Arc::new(mock_tenant_cache);

        // Create app with middleware
        let app = test::init_service(
            App::new()
                .app_data(web::Data::new(bigtable_client.clone()))
                .app_data(web::Data::new(tenant_cache_client.clone()))
                .app_data(web::Data::new(feature_flags.clone()))
                .wrap(TenantDataAccessMiddlewareFactory::new(
                    vec!["/health".to_string()],
                    tenant_cache_client.clone(),
                    bigtable_client.clone(),
                    feature_flags.clone(),
                ))
                .service(web::resource("/test").to(test_handler)),
        )
        .await;

        // Create request with tenant info
        let req = test::TestRequest::get().uri("/test").to_request();
        let tenant_info = TenantInfo {
            tenant_id: Some(tenant_id.to_string().into()),
            tenant_name: tenant_name.to_string(),
            shard_namespace: "test-shard".to_string(),
            cloud: "test-cloud".to_string(),
            scopes: vec![],
            user_id: None,
            opaque_user_id: None,
            user_email: None,
            service_name: None,
            genie_id: None,
        };
        req.extensions_mut().insert(tenant_info);
        // Add request context
        let request_context = RequestContext::new(
            RequestId::create_random(),
            RequestSessionId::new(uuid::Uuid::new_v4()),
            RequestSource::Client,
            None,
        );
        req.extensions_mut().insert(request_context);

        // Make request with a timeout to prevent hanging
        let resp_result =
            tokio::time::timeout(std::time::Duration::from_secs(5), app.call(req)).await;

        // Verify bigtable client was called (or not) as expected
        let call_count = call_counter
            .clone()
            .load(std::sync::atomic::Ordering::SeqCst);
        if expect_bigtable_call {
            assert!(
                call_count > 0,
                "Test case '{}': Expected bigtable check_tenant_data_access to be called, but it wasn't",
                tenant_name
            );
        } else {
            assert_eq!(
                call_count, 0,
                "Test case '{}': Expected bigtable check_tenant_data_access NOT to be called, but it was called {} times",
                tenant_name, call_count
            );
        }

        // Check if we expect an error response
        if expected_status == http::StatusCode::FORBIDDEN {
            // For forbidden status, we expect the response to be an error
            let err = resp_result.expect("Test timed out").expect_err(&format!(
                "Test case '{}' failed: expected error but got success",
                tenant_name
            ));

            // Verify the error message
            let err_msg = err.to_string();
            assert!(
                err_msg.contains("encryption key access denied")
                    || err_msg.contains("Tenant encryption key access denied"),
                "Error message should mention encryption key access: {}",
                err_msg
            );
        } else {
            // For successful status codes, we expect the call to succeed
            let resp = resp_result.expect("Test timed out").unwrap_or_else(|_| {
                panic!(
                    "Test case '{}' failed: expected success but got error",
                    tenant_name
                )
            });

            assert_eq!(
                resp.status(),
                expected_status,
                "Test case '{}' failed: expected status {} but got {}",
                tenant_name,
                expected_status,
                resp.status()
            );
        }
    }

    #[actix_web::test]
    async fn test_tenant_without_encryption() {
        run_tenant_data_access_test(
            true,
            "tenant-without-encryption",
            "tenant-without-encryption",
            "",
            false,
            None,
            http::StatusCode::OK,
        )
        .await;
    }

    #[actix_web::test]
    async fn test_tenant_with_encryption_accessible() {
        run_tenant_data_access_test(
            true,
            "tenant-with-encryption",
            "tenant-with-encryption",
            "projects/foo/locations/global/keyRings/bar/cryptoKeys/key_1",
            true,
            None,
            http::StatusCode::OK,
        )
        .await;
    }

    #[actix_web::test]
    async fn test_tenant_with_encryption_inaccessible() {
        run_tenant_data_access_test(
            true,
            "tenant-with-encryption-error",
            "tenant-with-encryption-error",
            "projects/foo/locations/global/keyRings/bar/cryptoKeys/key_2",
            true,
            Some(tonic::Status::permission_denied("permission denied")),
            http::StatusCode::FORBIDDEN,
        )
        .await;
    }

    #[actix_web::test]
    async fn test_tenant_with_feature_flag_disabled() {
        run_tenant_data_access_test(
            false,
            "tenant-with-encryption",
            "tenant-with-encryption",
            "projects/foo/locations/global/keyRings/bar/cryptoKeys/key_1",
            false,
            None,
            http::StatusCode::OK,
        )
        .await;
    }

    #[actix_web::test]
    async fn test_subscription_status_check() {
        use crate::api_auth::CHECK_SUBSCRIPTION_STATUS;
        use auth_query_client::auth_query::get_token_info_response;

        // Create tokens with different subscription statuses
        let active_token = "active_token".to_string();
        let inactive_token = "inactive_token".to_string();
        let enterprise_token = "enterprise_token".to_string();
        let trialing_token = "trialing_token".to_string();
        let unknown_token = "unknown_token".to_string();

        let mut tokens = HashMap::new();
        tokens.insert(active_token.clone(), "user1".to_string());
        tokens.insert(inactive_token.clone(), "user2".to_string());
        tokens.insert(enterprise_token.clone(), "user3".to_string());
        tokens.insert(trialing_token.clone(), "user4".to_string());
        tokens.insert(unknown_token.clone(), "user5".to_string());

        // Create mock auth client with different subscription types
        let auth_client = MockAuthQueryClient::new(tokens)
            .with_subscription(
                active_token.clone(),
                Some(get_token_info_response::Subscription::ActiveSubscription(
                    auth_query_client::auth_query::ActiveSubscription {
                        end_date: None,
                        usage_balance_depleted: false,
                    },
                )),
            )
            .with_subscription(
                inactive_token.clone(),
                Some(get_token_info_response::Subscription::InactiveSubscription(
                    auth_query_client::auth_query::InactiveSubscription {},
                )),
            )
            .with_subscription(
                enterprise_token.clone(),
                Some(get_token_info_response::Subscription::Enterprise(
                    auth_query_client::auth_query::EnterpriseSubscription {},
                )),
            )
            .with_subscription(
                trialing_token.clone(),
                Some(get_token_info_response::Subscription::Trial(
                    auth_query_client::auth_query::Trial { trial_end: None },
                )),
            )
            .with_subscription(unknown_token.clone(), None);

        // Create feature flags for the test
        let fake_feature_flags = feature_flags::setup_local();

        // Create ApiAuth using the new_with_mock_client method to use the mock client with subscription statuses
        let api_auth = Arc::new(ApiAuth::new_with_mock_client(
            auth_client,
            Some(fake_feature_flags.clone()),
        ));

        // Clone feature flags for use in the test
        let ff_clone = fake_feature_flags.clone();

        // Test with feature flag disabled
        {
            let app = test::init_service(
                App::new()
                    .wrap(AuthCheckMiddlewareFactory::new(
                        api_auth.clone(),
                        vec!["/health".to_string()],
                    ))
                    .wrap_fn(move |mut req, srv| {
                        setup_request_id(&mut req, &ff_clone).unwrap();
                        srv.call(req)
                    })
                    .service(web::resource("/test").to(HttpResponse::Ok)),
            )
            .await;

            // With feature flag disabled, inactive subscription should still pass
            let req = test::TestRequest::with_uri("/test")
                .insert_header((header::AUTHORIZATION, format!("Bearer {}", inactive_token)))
                .to_request();
            let resp = app.call(req).await;
            assert_eq!(
                resp.expect(
                    "expected success with inactive subscription when feature flag is disabled"
                )
                .status(),
                http::StatusCode::OK
            );
        }

        // Test with feature flag enabled
        {
            // Enable the subscription status check feature flag
            CHECK_SUBSCRIPTION_STATUS.set_local(&fake_feature_flags, true);

            // Verify the feature flag is enabled
            assert!(
                CHECK_SUBSCRIPTION_STATUS.get_from(&fake_feature_flags),
                "Feature flag should be enabled"
            );

            let app = test::init_service(
                App::new()
                    .wrap(AuthCheckMiddlewareFactory::new(
                        api_auth.clone(),
                        vec!["/health".to_string()],
                    ))
                    .wrap_fn(move |mut req, srv| {
                        setup_request_id(&mut req, &fake_feature_flags).unwrap();
                        srv.call(req)
                    })
                    .service(web::resource("/test").to(HttpResponse::Ok)),
            )
            .await;

            // Active subscription should pass
            let req = test::TestRequest::with_uri("/test")
                .insert_header((header::AUTHORIZATION, format!("Bearer {}", active_token)))
                .to_request();
            let resp = app.call(req).await;
            assert_eq!(
                resp.expect("expected success with active subscription")
                    .status(),
                http::StatusCode::OK
            );

            // Enterprise subscription should pass
            let req = test::TestRequest::with_uri("/test")
                .insert_header((
                    header::AUTHORIZATION,
                    format!("Bearer {}", enterprise_token),
                ))
                .to_request();
            let resp = app.call(req).await;
            assert_eq!(
                resp.expect("expected success with enterprise subscription")
                    .status(),
                http::StatusCode::OK
            );

            // Trialing subscription should pass
            let req = test::TestRequest::with_uri("/test")
                .insert_header((header::AUTHORIZATION, format!("Bearer {}", trialing_token)))
                .to_request();
            let resp = app.call(req).await;
            assert_eq!(
                resp.expect("expected success with trialing subscription")
                    .status(),
                http::StatusCode::OK
            );

            // Unknown subscription should pass
            let req = test::TestRequest::with_uri("/test")
                .insert_header((header::AUTHORIZATION, format!("Bearer {}", unknown_token)))
                .to_request();
            let resp = app.call(req).await;
            assert_eq!(
                resp.expect("expected success with unknown subscription")
                    .status(),
                http::StatusCode::OK
            );

            // Inactive subscription should fail with 402 Payment Required
            let req = test::TestRequest::with_uri("/test")
                .insert_header((header::AUTHORIZATION, format!("Bearer {}", inactive_token)))
                .to_request();
            let resp = app.call(req).await;
            assert_eq!(
                resp.expect_err("expected failure with inactive subscription")
                    .as_response_error()
                    .status_code(),
                http::StatusCode::PAYMENT_REQUIRED
            );
        }
    }
}
