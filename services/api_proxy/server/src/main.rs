mod agents_client;
mod api_auth;
mod chat_utils;
mod circuit_breaker;
mod config;
mod handler_utils;
mod handlers;
mod handlers_agents;
mod handlers_chat;
mod handlers_completion;
mod handlers_external_sources;
mod handlers_github;
mod handlers_next_edit;
mod handlers_notification;
mod handlers_remote_agent_actions;
mod handlers_remote_agents;
mod metrics;
mod middleware;
mod model_registry;
mod silent_request_validator;
mod timed_extractor;

mod generation_clients;
mod grpc_handler;
mod model_finder;
mod request_insight_util;

use crate::agents_client::AgentsClientImpl;
use api_auth::ApiAuth;
use auth_central_client::{AuthCentralClient, AuthCentralClientImpl};
use clap::Parser;
use handler_utils::register_handler_utils_flags;
use request_insight_publisher::RequestInsightPublisherConfig;
use rustls::ServerConfig;
use std::io;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::Duration;
use tonic::transport::{Certificate, ClientTlsConfig, Identity};
use tonic::transport::{Server as GrpcServer, ServerTlsConfig};
use tonic_reflection::server::ServerReflectionServer;

use std::fs::File;
use std::io::BufReader;

use actix_web::dev::Service;
use actix_web::{dev::Server, web, App, HttpServer};
use config::CliArguments;
use docset_client::DocSetClientImpl;
use model_finder::ModelFinder;
use model_registry::ModelRegistry;
use request_insight_util::RequestInsightPublisher;
use struct_logging::setup_struct_logging;
use tokio::select;
use tokio::signal::unix::{signal, SignalKind};

use crate::agents_client::register_client_flags as register_client_flags_agents;
use crate::api_auth::register_flags as register_api_auth_flags;
use crate::generation_clients::register_flags as register_client_flags;
use crate::generation_clients::ClientImplFactory;
use crate::grpc_handler::ModelFinderServiceImpl;
use crate::handler_utils::{handle_api_auth, Handler, ThrottleCache};
use crate::handlers::register_handler_flags;
use crate::handlers_agents::register_handler_flags as register_handler_flags_agents;
use crate::handlers_chat::register_handler_flags as register_handler_flags_chat;
use crate::handlers_completion::register_handler_flags as register_handler_flags_completion;
use crate::handlers_external_sources::ExternalSourceClients;
use crate::handlers_github::register_handler_flags as register_handler_flags_github_processor;
use crate::handlers_next_edit::register_handler_flags as register_handler_flags_next_edit;
use crate::handlers_notification::register_flags as register_handler_flags_notification;
use crate::handlers_remote_agents::register_handler_flags as register_handler_flags_remote_agents;
use crate::request_insight_util::RequestInsightPublisherImpl;
use bigtable_proxy_client::BigtableProxyClientImpl;
use content_manager_client::ContentManagerClientImpl;
use memstore_client::{MemstoreClient, MemstoreClientImpl};

use request_insight_publisher::request_insight;

use crate::config::Config;
use crate::timed_extractor::TimedExtractorConfig;

pub use grpc_stream_mux::stream_mux;

pub mod google {
    pub mod rpc {
        tonic::include_proto!("google.rpc");
    }
}

pub mod agents {
    tonic::include_proto!("agents");
}

pub mod completion {
    tonic::include_proto!("completion");
}

pub mod model_finder_proto {
    tonic::include_proto!("model_finder");
}

pub mod public_api_proto {
    tonic::include_proto!("public_api");

    pub(crate) const FILE_DESCRIPTOR_SET: &[u8] =
        tonic::include_file_descriptor_set!("api_proxy_descriptor");
}
pub mod edit {
    tonic::include_proto!("edit");
}

pub mod next_edit {
    tonic::include_proto!("next_edit");
}

pub mod chat {
    tonic::include_proto!("chat");
}

pub mod error_details_proto {
    tonic::include_proto!("error_details");
}

pub mod augment {
    #[allow(clippy::all)]
    pub mod model_instance_config {
        tonic::include_proto!("augment.model_instance_config");
    }
}

pub mod share_proto {
    tonic::include_proto!("share");
}

pub mod base {
    pub use blob_names_rs_proto::base::blob_names;

    pub mod diff_utils {
        tonic::include_proto!("base.diff_utils");
    }
}

#[cfg(feature = "use_jemalloc")]
#[global_allocator]
static ALLOC: tikv_jemallocator::Jemalloc = tikv_jemallocator::Jemalloc;

#[cfg(feature = "use_jemalloc_profiling")]
#[allow(non_upper_case_globals)]
#[export_name = "malloc_conf"]
pub static malloc_conf: &[u8] = b"prof:true,prof_active:true,lg_prof_sample:19\0";

/// setup the routes that are exposed to the public.
///
/// Any handler here should have authentication.
#[allow(clippy::too_many_arguments)]
fn setup_public_http_server<MR: ModelRegistry + Send + Sync + 'static>(
    config: Config,
    api_auth: Arc<ApiAuth>,
    model_registry: Arc<MR>,
    request_insight_publisher: Arc<dyn RequestInsightPublisher + Send + Sync>,
    content_manager_client: ContentManagerClientImpl,
    external_source_clients: ExternalSourceClients,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
    tenant_cache: Arc<dyn tenant_watcher_client::TenantCacheClient + Send + Sync>,
    bind_address: &str,
    port: u16,
    ssl_builder: ServerConfig,
    record_user_events: bool,
    share_client: Arc<dyn share_client::ShareClient + Send + Sync>,
    agents_client: Arc<dyn agents_client::AgentsClient>,
    auth_central_client: Arc<dyn AuthCentralClient>,
    remote_agents_client: Arc<dyn remote_agents_client::RemoteAgentsClient>,
    remote_agent_actions_client: Arc<dyn remote_agent_actions_client::RemoteAgentActionsClient>,
    github_processor_client: Arc<dyn github_processor_client::GithubProcessorClient>,
    bigtable_proxy_client: Arc<BigtableProxyClientImpl>,
    memstore_client: Arc<dyn MemstoreClient>,
    notification_client: Arc<notification_client::NotificationClientImpl>,
    throttle_cache: Arc<ThrottleCache>,
) -> io::Result<Server> {
    let chat_payload_timeout = config.enable_chat_stream_payload_timeout;

    let state = web::Data::new(Handler::new(
        config,
        api_auth,
        model_registry,
        request_insight_publisher,
        content_manager_client,
        external_source_clients,
        feature_flags.clone(),
        share_client,
        agents_client,
        auth_central_client,
        remote_agents_client,
        remote_agent_actions_client,
        github_processor_client,
        memstore_client,
        notification_client,
        throttle_cache,
    ));

    let s = HttpServer::new(move || {
        let ff = feature_flags.clone();
        let app =
            // The wrap() calls are run in reverse order, so the last wrap()
            // call is the first to be executed.
            // https://docs.rs/actix-web/3.3.2/actix_web/struct.App.html#method.wrap
            App::new()
                // Check tenant data access after auth check.
                .wrap(middleware::TenantDataAccessMiddlewareFactory::new(
                    vec!["/health".to_string(), "/token".to_string()],
                    tenant_cache.clone(),
                    bigtable_proxy_client.clone(),
                    ff.clone(),
                ))
                // Finally, do the auth check and set up RequestContext. Note
                // that /health and /token are excluded from this
                .wrap(middleware::AuthCheckMiddlewareFactory::new(
                    state.api_auth.clone(),
                    vec!["/health".to_string(), "/token".to_string()],
                ))
                // Record metrics on how long it takes to process the request
                .wrap(middleware::MetricsMiddlewareFactory::new())
                // Second, add a root span
                .wrap(tracing_actix_web::TracingLogger::<middleware::AugmentRootSpanBuilder>::new())
                // First, setup the request ID
                .wrap_fn(move |mut req, srv| {
                    let r = middleware::setup_request_id(&mut req, &ff);
                    let path = req.path().to_string();
                    let fut = srv.call(req);
                    async move {
                        // ignore /health
                        if path == "/health" {
                            return fut.await;
                        }
                        let r = r.map_err(|_| {
                            tracing::warn!("Missing request id, creating a random one");
                            actix_web::error::ErrorBadRequest("Invalid request")
                        });
                        match r {
                            Ok(_) => fut.await,
                            Err(e) => Err(e),
                        }
                    }

                })
                .app_data(state.clone())
                .service(web::resource("/agents/llm-generate").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::LlmGenerateRequest>),
                ))
                .service(web::resource("/agents/codebase-retrieval").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::CodebaseRetrievalRequest>),
                ))
                .service(web::resource("/agents/edit-file").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::EditFileRequest>),
                ))
                .service(web::resource("/agents/list-remote-tools").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ListRemoteToolsRequest>),
                ))
                .service(web::resource("/agents/run-remote-tool").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RunRemoteToolRequest>),
                ))
                .service(web::resource("/agents/revoke-tool-access").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RevokeToolAccessRequest>),
                ))
                .service(web::resource("/agents/test-tool-connection").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::TestToolConnectionRequest>),
                ))
                .service(web::resource("/agents/check-tool-safety").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::CheckToolSafetyRequest>),
                ))
                .service(web::resource("/memorize").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::MemorizeRequest>),
                ))
                .service(web::resource("/batch-upload").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::BatchUploadRequest>),
                ))
                .service(
                    web::resource("/checkpoint-blobs").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::CheckpointBlobsRequest>,
                    )),
                )
                .service(web::resource("/completion").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::CompletionRequest>),
                ))
                .service(web::resource("/chat").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ChatRequest>),
                ))
                .service(web::resource("/chat-stream")
                    .app_data(TimedExtractorConfig { timeout: Duration::from_secs(40) })
                    .route(
                        if chat_payload_timeout {
                            web::post().to(handlers_chat::chat_stream_api_auth_timeout::<MR, ContentManagerClientImpl>)
                        } else {
                            web::post().to(handlers_chat::chat_stream_api_auth::<MR, ContentManagerClientImpl>)
                        }
                ))
                .service(
                    web::resource("/chat-feedback").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ChatFeedback>,
                    )),
                )
                .service(web::resource("/generate-commit-message-stream").route(
                    web::post().to(handlers_chat::generate_commit_message_stream_api_auth::<MR, ContentManagerClientImpl>),
                ))
                .service(web::resource("/instruction-stream").route(
                    web::post().to(handlers::instruction_stream_api_auth::<MR, ContentManagerClientImpl>),
                ))
                .service(web::resource("/smart-paste-stream").route(
                    web::post().to(handlers::instruction_stream_api_auth::<MR, ContentManagerClientImpl>),
                ))
                .service(
                    web::resource("/edit").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::EditRequest>),
                    ),
                )
                .service(
                    web::resource("/next-edit-stream").route(
                        web::post().to(handlers_next_edit::next_edit_stream_api_auth::<MR, ContentManagerClientImpl>),
                    ),
                )
                .service(
                    web::resource("/client-completion-timelines").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ClientCompletionTimelineRequest>,
                    )),
                )
                .service(
                    web::resource("/next-edit-feedback").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::NextEditFeedback>,
                    )),
                )
                .service(
                    web::resource("/record-next-edit-session-event").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::NextEditSessionEventBatch>,
                    )),
                )
                .service(
                    web::resource("/record-onboarding-session-event").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::OnboardingSessionEventBatch>,
                    )),
                )
                .service(
                    web::resource("/resolve-next-edit").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::NextEditResolutionBatch>,
                    )),
                )
                .service(
                    web::resource("/resolve-completions").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ResolveCompletions>,
                    )),
                )
                .service(web::resource("/resolve-edit").route(web::post().to(
                    handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::EditResolution>,
                    )),
                )
                .service(
                    web::resource("/resolve-instruction").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::InstructionResolution>,
                    )),
                )
                .service(
                    web::resource("/resolve-smart-paste").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::SmartPasteResolution>,
                    )),
                )
                .service(
                    web::resource("/completion-feedback").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::CompletionFeedback>,
                    )),
                )
                .service(
                    web::resource("/find-missing").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::FindMissingRequest>,
                    )),
                )
                .service(
                    web::resource("/list-external-source-types").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ListExternalSourceTypesRequest>),
                    ),
                )
                .service(
                    web::resource("/search-external-sources").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::SearchExternalSourcesRequest>),
                    ),
                )
                .service(
                    web::resource("/get-implicit-external-sources").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::GetImplicitExternalSourcesRequest>),
                    ),
                )
                .service(web::resource("/get-models").route(
                    web::post().to(handlers::get_models_auth::<MR, ContentManagerClientImpl>),
                ))
                .service(web::resource("/report-error").route(
                    web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ReportErrorRequest>),
                ))
                .service(
                    web::resource("/client-metrics").route(
                        web::post()
                            .to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ClientMetricsRequest>),
                    ),
                )
                .service(
                    web::resource("/report-feature-vector").route(
                        web::post()
                            .to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ReportFeatureVectorRequest>),
                    ),
                )
                .service(web::resource("/health").route(web::get().to(handlers::health)))
                .service(web::resource("/token").route(
                    web::post().to(handlers::token::<MR, ContentManagerClientImpl>)
                ))
                .service(
                    web::resource("/save-chat").route(web::post().to(
                        handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::SaveChatRequest>,
                    ))
                )
                .service(
                    web::resource("/record-request-events").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RecordRequestEventsRequest>),
                    )
                )
                .service(
                    web::resource("/record-session-events").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RecordSessionEventsRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/create").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::CreateRemoteAgentRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/list").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ListRemoteAgentsRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/list-stream").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ListRemoteAgentsStreamRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/chat").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RemoteAgentChatRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/get-chat-history").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::GetRemoteAgentChatHistoryRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/agent-history-stream").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::GetRemoteAgentHistoryStreamRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/interrupt").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::InterruptRemoteAgentRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/delete").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::DeleteRemoteAgentRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/logs").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RemoteAgentWorkspaceLogsRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/add-ssh-key").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RemoteAgentAddSshKeyRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/resume").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RemoteAgentResumeRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/resume-hint").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RemoteAgentResumeHintRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/pause").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RemoteAgentPauseRequest>),
                    )
                )
                // GitHub setup script management endpoints
                .service(
                    web::resource("/remote-agents/list-github-setup-scripts").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ListGithubSetupScriptsRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/read-github-setup-script").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ReadGithubSetupScriptRequest>),
                    )
                )
                // Trigger management endpoints
                .service(
                    web::resource("/remote-agent-actions/triggers/create").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::CreateTriggerRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agent-actions/triggers/list").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ListTriggersRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agent-actions/triggers/update").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::UpdateTriggerRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agent-actions/triggers/delete").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::DeleteTriggerRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agent-actions/triggers/executions").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::GetTriggerExecutionsRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agent-actions/triggers/matching-entities").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::GetMatchingEntitiesRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agent-actions/triggers/execute-manually").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ExecuteTriggerManuallyRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agent-actions/execute-manual-agent").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ExecuteManualAgentRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agent-actions/get-entity-details").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::GetEntityDetailsRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agent-actions/list-pr-files").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ListPullRequestFilesRequest>),
                    )
                )

                .service(
                    web::resource("/remote-agent-actions/triggers/dismiss-entity").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::DismissEntityRequest>),
                    )
                )

                .service(
                    web::resource("/remote-agents/update").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::UpdateRemoteAgentRequest>),
                    )
                )
                .service(
                    web::resource("/remote-agents/generate-summary").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RemoteAgentGenerateSummaryRequest>),
                    )
                )
                .service(
                    web::resource("/agent-workspace/report-status").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::AgentWorkspaceReportStatusRequest>),
                    )
                )
                .service(
                    web::resource("/agent-workspace/report-chat-history").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::AgentWorkspaceReportChatHistoryRequest>),
                    )
                )
                .service(
                    web::resource("/agent-workspace/report-setup-logs").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::AgentWorkspaceReportSetupLogsRequest>),
                    )
                )
                .service(
                    web::resource("/agent-workspace/poll-update").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::AgentWorkspacePollUpdateRequest>),
                    )
                )
                .service(
                    web::resource("/agent-workspace/stream").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::AgentWorkspaceStreamRequest>),
                    )
                )
                .service(
                    web::resource("/github/list-repos").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ListGithubReposForAuthenticatedUserRequest>),
                    )
                )
                .service(
                    web::resource("/github/list-branches").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ListGithubRepoBranchesRequest>),
                    )
                )
                .service(
                    web::resource("/github/is-user-configured").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::IsUserGithubConfiguredRequest>),
                    )
                )
                .service(
                    web::resource("/github/create-pull-request").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::CreatePullRequestRequest>),
                    )
                )
                .service(
                    web::resource("/github/get-repo").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::GetGithubRepoRequest>),
                    )
                )
                .service(
                    web::resource("/subscription-info").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::GetSubscriptionInfoRequest>),
                    )
                )
                .service(
                    web::resource("/revoke-current-user-tokens").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::RevokeCurrentUserTokensRequest>),
                    )
                )
                .service(
                    web::resource("/notifications/read").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::ReadNotificationsRequest>),
                    )
                )
                .service(
                    web::resource("/notifications/mark-as-read").route(
                        web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::MarkNotificationAsReadRequest>),
                    )
                );

        if record_user_events {
            // This endpoint is for reording keystrokes from full-export clients. These are now
            // called "full export user events" on the backend, but we can't rename this endpoint.
            app.service(web::resource("/record-user-events").route(
                web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, request_insight::RecordFullExportUserEventsRequest>),
            ))
            .service(web::resource("/record-preference-sample").route(
                web::post().to(handle_api_auth::<MR, ContentManagerClientImpl, public_api_proto::PreferenceSample>),
            ))
        } else {
            app
        }
    })
    .bind_rustls_0_23((bind_address, port), ssl_builder)
    .expect("Failed to bind TLS port");

    s.addrs().iter().for_each(|addr| {
        tracing::info!("Listening on {}", addr);
    });

    Ok(s.run())
}

fn read_all(f: &str) -> Result<String, tonic::Status> {
    match std::fs::read_to_string(f) {
        Err(e) => {
            tracing::error!("Failed to read file {}: {}", f, e);
            Err(tonic::Status::internal(e.to_string()))
        }
        Ok(s) => Ok(s),
    }
}

fn get_client_config(config: &Config) -> Result<Option<ClientTlsConfig>, tonic::Status> {
    if config.client_mtls {
        let client_key_data = read_all(&config.client_key)?;
        let client_cert_data = read_all(&config.client_cert)?;
        let client_ca_data = read_all(&config.client_ca_cert)?;

        let client_identity = Identity::from_pem(client_cert_data, client_key_data);

        let tls = ClientTlsConfig::new()
            .ca_certificate(Certificate::from_pem(client_ca_data))
            .identity(client_identity);
        Ok(Some(tls))
    } else {
        Ok(None)
    }
}

fn get_central_client_config(config: &Config) -> Result<Option<ClientTlsConfig>, tonic::Status> {
    if config.central_client_mtls {
        let client_key_data = read_all(&config.central_client_key)?;
        let client_cert_data = read_all(&config.central_client_cert)?;
        let client_ca_data = read_all(&config.central_client_ca_cert)?;

        let client_identity = Identity::from_pem(client_cert_data, client_key_data);

        let tls = ClientTlsConfig::new()
            .ca_certificate(Certificate::from_pem(client_ca_data))
            .identity(client_identity);
        Ok(Some(tls))
    } else {
        Ok(None)
    }
}

fn get_grpc_server_tls_config(config: &Config) -> Result<Option<ServerTlsConfig>, tonic::Status> {
    if config.grpc_server_mtls {
        let server_key_data = read_all(&config.grpc_server_key)?;
        let server_cert_data = read_all(&config.grpc_server_cert)?;
        let server_ca_data = read_all(&config.grpc_server_ca_cert)?;

        let server_identity = Identity::from_pem(server_cert_data, server_key_data);

        let tls = ServerTlsConfig::new()
            .client_ca_root(Certificate::from_pem(server_ca_data))
            .identity(server_identity);
        Ok(Some(tls))
    } else {
        Ok(None)
    }
}

fn load_certs(filename: &str) -> Vec<rustls_pki_types::CertificateDer<'static>> {
    let certfile = File::open(filename).expect("cannot open certificate file");
    let mut reader = BufReader::new(certfile);
    let r: Result<Vec<rustls_pki_types::CertificateDer<'static>>, io::Error> =
        rustls_pemfile::certs(&mut reader).collect();
    r.unwrap()
}

fn load_private_key(filename: &str) -> rustls_pki_types::PrivateKeyDer<'static> {
    let keyfile = File::open(filename).expect("cannot open private key file");
    let mut reader = BufReader::new(keyfile);

    loop {
        match rustls_pemfile::read_one(&mut reader).expect("cannot parse private key .pem file") {
            Some(rustls_pemfile::Item::Pkcs1Key(key)) => {
                return rustls_pki_types::PrivateKeyDer::Pkcs1(key)
            }
            Some(rustls_pemfile::Item::Pkcs8Key(key)) => {
                return rustls_pki_types::PrivateKeyDer::Pkcs8(key)
            }
            Some(rustls_pemfile::Item::Sec1Key(key)) => {
                return rustls_pki_types::PrivateKeyDer::Sec1(key)
            }
            None => break,
            _ => {}
        }
    }

    panic!(
        "no keys found in {:?} (encrypted keys not supported)",
        filename
    );
}

fn get_ssl_builder(config: &Config) -> ServerConfig {
    // Load key files
    let certs = load_certs(&config.https_server_cert);
    let privkey = load_private_key(&config.https_server_key);

    rustls::ServerConfig::builder()
        .with_no_client_auth()
        .with_single_cert(certs, privkey)
        .expect("bad certificates/private key")
}

async fn run(args: CliArguments) -> Result<(), Box<dyn std::error::Error>> {
    let config = Config::read(&args.config_file).expect("Failed to read config file");
    tracing::info!("{:?}", config);

    let feature_flags = feature_flags::setup(
        "api_proxy",
        "0.0.0",
        args.launch_darkly_secrets_file.as_ref(),
        config.dynamic_feature_flags_endpoint.as_deref(),
    )
    .await;
    let registry = feature_flags::new_registry();

    register_handler_flags(&registry);
    register_handler_flags_chat(&registry);
    register_handler_flags_completion(&registry);
    register_handler_flags_next_edit(&registry);
    register_handler_flags_agents(&registry);
    register_handler_flags_remote_agents(&registry);
    register_handler_flags_github_processor(&registry);
    register_handler_flags_notification(&registry);
    register_api_auth_flags(&registry);
    register_client_flags(&registry);
    register_client_flags_agents(&registry);
    register_handler_utils_flags(&registry);

    // Populate defaults from JSON config, mostly for testing. LaunchDarkly will take precedence when available.
    if let Some(ref config) = config.dynamic_feature_flags {
        feature_flags::populate_from_hashmap(&feature_flags, config, &registry)?;
    }

    let tls_config = get_client_config(&config).expect("Failed to create TLS config");
    let central_tls_config =
        get_central_client_config(&config).expect("Failed to create TLS config");
    let request_insight_publisher_config =
        RequestInsightPublisherConfig::read(&args.request_insight_publisher_config_file)
            .expect("Failed to read publisher config file");

    let request_insight =
        Arc::new(RequestInsightPublisherImpl::new(request_insight_publisher_config).await);

    let content_manager =
        ContentManagerClientImpl::new(&config.content_manager_endpoint, tls_config.clone(), None);

    let external_source_clients = ExternalSourceClients {
        docset_client: Box::new(DocSetClientImpl::new(
            &config.docset_endpoint,
            tls_config.clone(),
            // TODO: make this a config
            Duration::from_secs_f32(10.0),
        )),
    };

    tracing::info!(
        "Creating TenantWatcher to endpoint={:?}.",
        config.tenant_watcher_endpoint
    );
    let tenant_watcher_client = Arc::new(tenant_watcher_client::TenantWatcherClientImpl::new(
        &config.tenant_watcher_endpoint,
        central_tls_config.clone(),
        Duration::from_secs(120),
    ));
    let tenant_cache: Arc<dyn tenant_watcher_client::TenantCacheClient + Send + Sync> = Arc::new(
        tenant_watcher_client::WatchTenantCache::new(tenant_watcher_client, "".to_string()),
    );

    let throttle_cache = Arc::new(ThrottleCache::new(
        config.throttle_cache_size,
        config.throttle_cache_ttl_seconds,
        feature_flags.clone(),
    ));

    let api_auth = Arc::new(api_auth::ApiAuth::new(
        config.namespace.clone(),
        config.cloud.clone(),
        config.auth_query.clone(),
        tls_config.clone(),
        feature_flags.clone(),
        tenant_cache.clone(),
        throttle_cache.clone(),
    ));
    let client_factory = Arc::new(ClientImplFactory::new(
        tls_config.clone(),
        feature_flags.clone(),
    ));
    let model_registry = Arc::new(model_registry::DynamicModelRegistry::new(client_factory));

    let share_client = Arc::new(share_client::ShareClientImpl::new(
        &config.share_endpoint,
        tls_config.clone(),
    ));

    let agents_client = Arc::new(AgentsClientImpl::new(
        &config.agents_endpoint,
        tls_config.clone(),
        feature_flags.clone(),
    ));

    let auth_central_client = Arc::new(AuthCentralClientImpl::new(
        &config.auth_central_grpc_url,
        central_tls_config.clone(),
    ));

    let remote_agents_client = Arc::new(remote_agents_client::RemoteAgentsClientImpl::new(
        &config.remote_agents_endpoint,
        tls_config.clone(),
    ));

    let remote_agent_actions_client = Arc::new(
        remote_agent_actions_client::RemoteAgentActionsClientImpl::new(
            &config.remote_agent_actions_endpoint,
            tls_config.clone(),
        ),
    );

    let github_processor_client =
        Arc::new(github_processor_client::GithubProcessorClientImpl::new(
            &config.github_processor_endpoint,
            tls_config.clone(),
        ));

    // Initialize bigtable proxy client
    let bigtable_proxy_client = Arc::new(BigtableProxyClientImpl::new(
        &config.bigtable_proxy_endpoint,
        tls_config.clone(),
        Duration::from_secs_f32(config.bigtable_proxy_timeout_secs),
    ));

    let memstore_client: Arc<dyn MemstoreClient> = Arc::new(MemstoreClientImpl::new(
        &config.memstore_endpoint,
        tls_config.clone(),
        Some(Duration::from_secs_f32(config.memstore_timeout_secs)),
    ));

    // Initialize notification client
    let notification_client = Arc::new(notification_client::NotificationClientImpl::new(
        &config.notification_endpoint,
        tls_config.clone(),
        Duration::from_secs_f32(config.notification_timeout_secs),
    ));

    let client = kube::Client::try_default()
        .await
        .expect("Failed to start kube client");
    let watcher = ModelFinder::new(client, &config.namespace, model_registry.clone());

    let ssl_builder = get_ssl_builder(&config);

    let public_server = setup_public_http_server(
        config.clone(),
        api_auth,
        model_registry.clone(),
        request_insight.clone(),
        content_manager.clone(),
        external_source_clients,
        feature_flags.clone(),
        tenant_cache.clone(),
        &config.bind_address,
        config.port,
        ssl_builder,
        config.record_user_events,
        share_client,
        agents_client,
        auth_central_client,
        remote_agents_client,
        remote_agent_actions_client,
        github_processor_client,
        bigtable_proxy_client,
        memstore_client.clone(),
        notification_client,
        throttle_cache,
    )?;

    metrics_server::setup_default_metrics();

    let metrics_server = metrics_server::setup_metrics_http_server(
        &config.metrics_server_bind_address,
        config.metrics_server_port,
    )?;

    let debug_server = debug_server::setup_debug_http_server(
        &config.debug_server_bind_address,
        config.debug_server_port,
        cfg!(feature = "use_jemalloc_profiling"),
    )?;

    let grpc_addr: SocketAddr = config.grpc_bind_address.parse()?;

    let grpc_handler = ModelFinderServiceImpl::new(model_registry.clone());
    let grpc_handler = grpc_handler.new_server();

    let (_health_reporter, health_service) = tonic_health::server::health_reporter();

    let grpc_server_tls_config =
        get_grpc_server_tls_config(&config).expect("Failed to create TLS config");
    let grpc_server = match grpc_server_tls_config {
        None => GrpcServer::builder(),
        Some(server_tls_config) => GrpcServer::builder()
            .tls_config(server_tls_config)
            .expect("Failed to create grpc server"),
    };

    let listener = tokio::net::TcpListener::bind(&grpc_addr).await?;
    tracing::info!(
        "gRPC server listening on {:?}",
        listener.local_addr().expect("Failed to get local address")
    );

    let mut sigterm_notifier = signal(SignalKind::terminate()).expect("handle SIGTERM");

    let reflection_service: ServerReflectionServer<_> =
        tonic_reflection::server::Builder::configure()
            .register_encoded_file_descriptor_set(public_api_proto::FILE_DESCRIPTOR_SET)
            .build_v1()?;
    let grpc_server = grpc_server
        .timeout(Duration::from_secs(300))
        .trace_fn(tracing_tonic::server::trace_fn)
        .add_service(grpc_handler)
        .add_service(health_service)
        .add_service(reflection_service)
        .serve_with_incoming_shutdown(
            tokio_stream::wrappers::TcpListenerStream::new(listener),
            async move {
                sigterm_notifier.recv().await;
            },
        );

    let servers = futures::future::join4(public_server, metrics_server, grpc_server, debug_server);

    select! {
        watcher = watcher.run() => {
            panic!("watcher failed: {watcher:?}");
        },
        servers = servers => {
            panic!("servers done: {servers:?}");
        }
        tenant_cache = tenant_cache.run() => {
            panic!("tenant_cache failed: {tenant_cache:?}");
        }
    };
}

#[actix_web::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    setup_struct_logging().expect("Failed to setup logging");

    let args = CliArguments::parse();
    tracing::info!("{:?}", args);

    run(args).await
}
