use std::convert::TryFrom;
use std::time::Duration;
use std::vec;

use actix_web::{HttpRequest, HttpResponse};
use blob_names::BlobName;
use model_registry::ModelRegistry;
use request_context::RequestSessionId;
use request_insight_publisher::request_insight;
use tracing_actix_web::RootSpan;

use crate::augment::model_instance_config::model_instance_config::ModelConfig::Inference;
use crate::augment::model_instance_config::ModelType;
use crate::base::diff_utils::GranularEditEvent;
use crate::completion::{
    self, CompletionPosition, CompletionRequest, GitDiffFileInfo, RecencyInfo, TabSwitchEvent,
    ViewedContentEvent,
};
use crate::generation_clients::Client;
use crate::handler_utils::{
    convert_blobs_and_names, convert_replacement_text, gate_on_circuit_breaker, get_model,
    request_context_from_req, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,
};
use crate::handlers::MODEL_FLAG;
use crate::model_registry;
use crate::public_api_proto;
use crate::request_insight_util::extract_request_metadata;
use content_manager_client::ContentManagerClient;

pub const CB_COMPLETION: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_completion", false);

pub const COMPLETION_SLOW_PAYLOAD_THRESHOLD_MS: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_slow_payload_threshold_ms_completion", 1000);

// Token bucket rate limiting for completions
pub const COMPLETION_THROTTLE_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_throttle_completion", false);

pub const COMPLETION_THROTTLE_FILL_RATE_PER_SEC_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("api_proxy_throttle_completion_fill_rate", 5.0);

pub const COMPLETION_THROTTLE_CAPACITY_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_throttle_completion_capacity", 500);

pub fn register_handler_flags(registry: &feature_flags::RegistryHandle) {
    MODEL_FLAG
        .register(registry)
        .expect("Registering MODEL_FLAG");
}

impl TryFrom<(public_api_proto::CompletionRequest, &String)> for CompletionRequest {
    type Error = tonic::Status;

    /// convert the frontend completion request into the modelhost api
    fn try_from(
        from: (public_api_proto::CompletionRequest, &String),
    ) -> Result<completion::CompletionRequest, tonic::Status> {
        let (req, model_name) = from;
        if req.top_k.map(|f| f < -1).unwrap_or(false) {
            Err(tonic::Status::invalid_argument("invalid top_k value"))
        } else if req
            .top_p
            .map(|f| !(0.0..=1.0).contains(&f))
            .unwrap_or(false)
        {
            Err(tonic::Status::invalid_argument("invalid top_p value"))
        } else if req
            .temperature
            .map(|f| !(0.0..=1.0).contains(&f))
            .unwrap_or(false)
        {
            Err(tonic::Status::invalid_argument("invalid temperature value"))
        } else if req.max_tokens.map(|f| f <= 0 || f >= 512).unwrap_or(false) {
            Err(tonic::Status::invalid_argument("invalid max_tokens value"))
        } else {
            let position: Option<CompletionPosition> =
                match (req.prefix_begin, req.cursor_position, req.suffix_end) {
                    (Some(prefix_begin), Some(cursor_position), Some(suffix_end)) => {
                        Some(CompletionPosition {
                            prefix_begin,
                            cursor_position,
                            suffix_end,
                            blob_name: req.blob_name.unwrap_or_default().to_string(),
                        })
                    }
                    _ => None,
                };

            // When converting the frontend request to the backend request, if
            // there is a workspace specified in delta format, try to convert it
            // into the proto version. This may fail if the blob ids are not
            // valid hex strings.
            #[allow(deprecated)]
            let memory_blob_names: Vec<BlobName> = req
                .memories
                .iter()
                .map(|m| BlobName::new(m).unwrap())
                .collect();
            let blobs = convert_blobs_and_names(&req.blobs, Some(&memory_blob_names))?;

            #[allow(deprecated)]
            Ok(completion::CompletionRequest {
                model_name: model_name.clone(),
                prefix: req.prompt.clone(),
                suffix: req.suffix.unwrap_or_default().clone(),
                path: req.path.clone(),
                blobs: Some(blobs),
                max_tokens: req.max_tokens.unwrap_or(0),
                lang: req.lang.unwrap_or_default(),
                seed: None,
                position,
                truncation: vec![],
                sequence_id: req.sequence_id.unwrap_or(0),
                probe_only: req.probe_only.unwrap_or(false),
                recency_info: req.recency_info.map(|r| RecencyInfo {
                    tab_switch_events: r
                        .tab_switch_events
                        .iter()
                        .map(|e| TabSwitchEvent {
                            path: e.path.to_string(),
                            file_blob_name: e.file_blob_name.to_string(),
                        })
                        .collect(),
                    git_diff_file_info: r
                        .git_diff_file_info
                        .iter()
                        .map(|i| GitDiffFileInfo {
                            content_blob_name: i.content_blob_name.to_string(),
                            file_blob_name: i.file_blob_name.to_string(),
                        })
                        .collect(),
                    recent_changes: convert_replacement_text(&r.recent_changes),
                    viewed_contents: r
                        .viewed_contents
                        .iter()
                        .map(|v| ViewedContentEvent {
                            path: v.path.to_string(),
                            file_blob_name: v.file_blob_name.to_string(),
                            visible_content: v.visible_content.to_string(),
                            line_start: v.line_start,
                            line_end: v.line_end,
                            char_start: v.char_start,
                            char_end: v.char_end,
                            timestamp: v.timestamp,
                        })
                        .collect(),
                }),
                filter_threshold: req.filter_threshold,
                edit_events: req
                    .edit_events
                    .into_iter()
                    .map(TryFrom::try_from)
                    .collect::<Result<Vec<GranularEditEvent>, _>>()
                    .ok()
                    .unwrap_or_default(),
            })
        }
    }
}

fn completion_request_to_request_insight(
    r: &public_api_proto::CompletionRequest,
    session_id: &RequestSessionId,
    user_agent: &str,
) -> request_insight::InferRequest {
    // When recording the frontend request for request insight, it should not be possible for the
    // conversion of the workspace blob names from the delta format to cause an error, because
    // the same conversion was necessary to create the backend completion request. For completeness, however,
    // we also handle the possible error here by recording an empty delta.
    let blobs = match &r.blobs {
        Some(b) => match b.try_into() {
            Ok(delta) => Some(delta),
            Err(_e) => {
                tracing::warn!("Invalid name in blobs: unable to decode");
                None
            }
        },
        None => None,
    };

    request_insight::InferRequest {
        model: r.model.as_deref().unwrap_or_default().to_string(),
        prompt: r.prompt.clone(),
        path: r.path.clone(),
        suffix: r.suffix.as_deref().unwrap_or_default().to_string(),
        #[allow(deprecated)]
        memories: r.memories.iter().map(|m| m.to_string()).collect(),
        top_k: r.top_k.unwrap_or(0),
        top_p: r.top_p.unwrap_or(0.0),
        temperature: r.temperature.unwrap_or(0.0),
        max_tokens: r.max_tokens.unwrap_or(0),
        session_id: session_id.to_string(),
        lang: match &r.lang {
            Some(lang) => lang.to_string(),
            None => "".into(),
        },
        user_agent: user_agent.to_string(),
        sequence_id: r.sequence_id.unwrap_or(0),
        #[allow(deprecated)]
        probe_only: r.probe_only.unwrap_or(false),
        blobs,
        recency_info: r.recency_info.as_ref().map(|r| {
            request_insight_publisher::completion::RecencyInfo {
                tab_switch_events: r
                    .tab_switch_events
                    .iter()
                    .map(|t| request_insight_publisher::completion::TabSwitchEvent {
                        path: t.path.clone(),
                        file_blob_name: t.file_blob_name.clone(),
                    })
                    .collect(),
                git_diff_file_info: r
                    .git_diff_file_info
                    .iter()
                    .map(|g| request_insight_publisher::completion::GitDiffFileInfo {
                        content_blob_name: g.content_blob_name.clone(),
                        file_blob_name: g.file_blob_name.clone(),
                    })
                    .collect(),
                recent_changes: r
                    .recent_changes
                    .iter()
                    .map(|r| request_insight_publisher::completion::ReplacementText {
                        blob_name: r.blob_name.to_string(),
                        path: r.path.to_string(),
                        char_start: r.char_start,
                        char_end: r.char_end,
                        replacement_text: r.replacement_text.to_string(),
                        present_in_blob: r.present_in_blob,
                        expected_blob_name: r.expected_blob_name.as_ref().map(|n| n.to_string()),
                        timestamp: r.timestamp,
                    })
                    .collect(),
                viewed_contents: r.viewed_contents.iter().map(|v| request_insight_publisher::completion::ViewedContentEvent {
                    path: v.path.clone(),
                    file_blob_name: v.file_blob_name.clone(),
                    visible_content: v.visible_content.clone(),
                    line_start: v.line_start,
                    line_end: v.line_end,
                    char_start: v.char_start,
                    char_end: v.char_end,
                    timestamp: v.timestamp,
                }).collect(),
            }
        }),
        user_filter_threshold: r.filter_threshold,
        edit_events: r
            .edit_events
            .clone()
            .into_iter()
            .map(TryFrom::try_from)
            .collect::<Result<Vec<request_insight_publisher::base::diff_utils::GranularEditEvent>, _>>()
            .ok()
            .unwrap_or_default(),
    }
}

impl From<(completion::CompletionResponse, u32, u32)> for public_api_proto::CompletionResponse {
    /// transfer from a completion response to the public API completion response.
    fn from(f: (completion::CompletionResponse, u32, u32)) -> Self {
        let (resp, suggested_prefix_char_count, suggested_suffix_char_count) = f;
        let mut items = vec![];
        let values = [
            resp.text.clone(),
            resp.skipped_suffix.clone(),
            resp.suffix_replacement_text.clone(),
        ];
        if !values.is_empty() {
            let item = public_api_proto::CompletionItem {
                text: resp.text.clone(),
                skipped_suffix: resp.skipped_suffix,
                suffix_replacement_text: resp.suffix_replacement_text,
                filter_score: resp.filter_score,
            };
            items = vec![item];
        }

        public_api_proto::CompletionResponse {
            text: resp.text,
            unknown_memory_names: resp.unknown_blob_names,
            completion_items: items,
            suggested_prefix_char_count,
            suggested_suffix_char_count,
            checkpoint_not_found: resp.checkpoint_not_found,
            // Unused optional fields: unknown_checkpoint_id, completion_timeout_ms
            ..Default::default()
        }
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::CompletionRequest> for Handler<MR, CNC>
{
    fn get_retry_policy(&self, req: &HttpRequest) -> RetryPolicy {
        self.get_retry_policy_from_default_flags(req)
    }

    /// run a completion and return a http response in the success case or a status in the error case
    async fn handle(
        &self,
        req: &HttpRequest,
        comp_request: public_api_proto::CompletionRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("completion request model={:?}", comp_request.model,);

        let (user, tenant_info, request_context, start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_COMPLETION, &feature_flags, req, &tenant_info)?;

        // check for slow payload
        let slow_payload_threshold_ms =
            COMPLETION_SLOW_PAYLOAD_THRESHOLD_MS.get_from(&feature_flags) as u64;
        if slow_payload_threshold_ms > 0
            && start_time.elapsed() > Duration::from_millis(slow_payload_threshold_ms)
        {
            tracing::warn!(
                "Completion request payload took more than {}ms to arrive",
                slow_payload_threshold_ms
            );
            return Ok(HttpResponse::RequestTimeout().finish()); // HTTP 408
        }

        // check rate limit
        if COMPLETION_THROTTLE_FLAG.get_from(&feature_flags) {
            let fill_rate_per_second =
                COMPLETION_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags);
            let capacity = COMPLETION_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64;
            self.throttle_cache
                .should_throttle(
                    &user.user_id,
                    "completion",
                    fill_rate_per_second,
                    capacity,
                    1.0,
                )
                .await?;
        }

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::Completion,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let user_agent = req
                .headers()
                .get("user-agent")
                .map(|h| h.to_str().unwrap_or("not-utf8"))
                .unwrap_or("");
            self.request_insight_publisher
                .record_infer_request(
                    &request_context,
                    &tenant_info,
                    completion_request_to_request_insight(
                        &comp_request,
                        &request_context.request_session_id(),
                        user_agent,
                    ),
                )
                .await;

            let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

            let (com, mi) = get_model::<MR>(
                comp_request.model.as_ref(),
                &self.model_registry,
                &feature_flags,
                &MODEL_FLAG,
                ModelType::Inference,
            )
            .await?;

            let inference = match mi.model_config {
                Some(Inference(inference)) => inference,
                _ => return Err(tonic::Status::internal("Model is not an inference model")),
            };
            let com = match com {
                Client::Completion(c) => c.clone(),
                _ => return Err(tonic::Status::internal("Model is not a completion model")),
            };
            let comp_request: CompletionRequest = (comp_request, &mi.name).try_into()?;

            let result = com.complete(&request_context, comp_request, true).await?;
            let result = public_api_proto::CompletionResponse::from((
                result,
                inference.suggested_prefix_char_count,
                inference.suggested_suffix_char_count,
            ));
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use crate::augment::model_instance_config::ModelType;
    use crate::base::blob_names as blob_names_proto;
    use crate::completion::{
        CompletionPosition, CompletionRequest, GitDiffFileInfo, RecencyInfo, ReplacementText,
        TabSwitchEvent,
    };
    use crate::handler_utils::handle_api_auth;
    use crate::handler_utils::tests::{
        create_model_instance_config, new_root_span, setup_app_state, setup_req,
        DEFAULT_PREFIX_CHAR_COUNT, DEFAULT_SUFFIX_CHAR_COUNT,
    };
    use crate::model_registry::tests::FakeClientFactory;
    use crate::model_registry::{DynamicModelRegistry, ModelRegistry};
    use actix_web::{body, http, web};
    use blob_names::BlobName;

    use super::*;

    async fn get_last_comp_request(
        model_name: &str,
        fake_client_factory: Arc<FakeClientFactory>,
    ) -> CompletionRequest {
        fake_client_factory
            .fake_clients
            .lock()
            .unwrap()
            .iter()
            .find(|c| c.model_name.as_ref() == model_name)
            .expect("No client for model")
            .get_last_comp_request()
    }

    async fn add_inference_models(registry: &DynamicModelRegistry) {
        registry
            .update_models(vec![
                create_model_instance_config("model1", ModelType::Inference, 10),
                create_model_instance_config("model2", ModelType::Inference, 1),
            ])
            .await
            .unwrap();
    }

    async fn run_completion(
        request: &public_api_proto::CompletionRequest,
    ) -> (CompletionRequest, public_api_proto::CompletionResponse) {
        let (resp, fake_client_factory) = run_completion_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let result: public_api_proto::CompletionResponse =
            serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap();
        let modelhost_request = get_last_comp_request("model1", fake_client_factory).await;
        (modelhost_request, result)
    }

    // Note that the CompletionRequest may not be valid if http::StatusCode is
    // not OK
    async fn run_completion_raw(
        request: &public_api_proto::CompletionRequest,
    ) -> (HttpResponse, Arc<FakeClientFactory>) {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_inference_models(&registry).await;
        }
        let root_span = new_root_span();

        let resp = handle_api_auth(
            app_state.clone(),
            req,
            web::Json(request.clone()),
            root_span,
        )
        .await;
        (resp, fake_client_factory)
    }

    #[actix_web::test]
    async fn test_complete_model() {
        let (modelhost_request, response) = run_completion(&public_api_proto::CompletionRequest {
            model: Some("model1".to_string()),
            prompt: "def quicksort".to_string(),
            ..Default::default()
        })
        .await;

        assert_eq!(response.text, "hello");
        assert_eq!(
            response.suggested_prefix_char_count,
            DEFAULT_PREFIX_CHAR_COUNT
        );
        assert_eq!(
            response.suggested_suffix_char_count,
            DEFAULT_SUFFIX_CHAR_COUNT
        );
        print!("{modelhost_request:?}");
        // default values filled in
        assert_eq!(
            modelhost_request,
            CompletionRequest {
                model_name: "model1".to_string(),
                prefix: "def quicksort".to_string(),
                sequence_id: 0,
                probe_only: false,
                blobs: Some(blob_names_proto::Blobs::default()),
                ..Default::default()
            }
        );
    }

    #[actix_web::test]
    async fn test_complete_model_with_blobs() {
        let default_frontend_req = public_api_proto::CompletionRequest {
            model: Some("model1".to_string()),
            prompt: "def quicksort".to_string(),
            suffix: None,
            path: "quicksort.py".to_string(),
            ..Default::default()
        };

        let default_completion_req = CompletionRequest {
            model_name: "model1".to_string(),
            prefix: "def quicksort".to_string(),
            path: "quicksort.py".to_string(),
            ..CompletionRequest::default()
        };

        let blob1 = BlobName::from_bytes(&[0; 32]).unwrap();
        let blob2 = BlobName::from_bytes(&[1; 32]).unwrap();

        for use_memories in [true, false] {
            for use_blobs in [true, false] {
                println!("use_memories: {}, use_blobs: {}", use_memories, use_blobs);

                // Note that blobs are not sorted in the input
                let blobs = if use_blobs {
                    Some(public_api_proto::Blobs {
                        checkpoint_id: None,
                        added_blobs: vec![String::from(&blob1), String::from(&blob2)],
                        deleted_blobs: vec![],
                    })
                } else {
                    None
                };
                let memories = if use_memories {
                    vec![String::from(&blob2), String::from(&blob1)]
                } else {
                    vec![]
                };

                // No matter how the API gets the blobs from the client, we want
                // to send this to the rest of the backend.
                // NOTE: blobs is not sorted in the inputs, but is sorted here
                let blobs_out = if use_blobs || use_memories {
                    blob_names_proto::Blobs {
                        baseline_checkpoint_id: None,
                        added: vec![blob1.as_bytes().to_vec(), blob2.as_bytes().to_vec()],
                        deleted: vec![],
                    }
                } else {
                    blob_names_proto::Blobs {
                        baseline_checkpoint_id: None,
                        added: vec![],
                        deleted: vec![],
                    }
                };

                #[allow(deprecated)]
                let frontend_req = public_api_proto::CompletionRequest {
                    memories,
                    blobs,
                    ..default_frontend_req.clone()
                };

                let completion_req = CompletionRequest {
                    blobs: Some(blobs_out),
                    ..default_completion_req.clone()
                };

                let (modelhost_request, response) = run_completion(&frontend_req).await;

                assert_eq!(response.text, "hello");
                assert_eq!(
                    response.suggested_prefix_char_count,
                    DEFAULT_PREFIX_CHAR_COUNT
                );
                assert_eq!(
                    response.suggested_suffix_char_count,
                    DEFAULT_SUFFIX_CHAR_COUNT
                );
                print!("{modelhost_request:?}");
                assert_eq!(modelhost_request, completion_req);
            }
        }
    }

    #[actix_web::test]
    async fn test_complete_model_with_suffix() {
        let (modelhost_request, response) = run_completion(&public_api_proto::CompletionRequest {
            model: Some("model1".to_string()),
            prompt: "def quicksort".to_string(),
            suffix: Some("partition(array, leftmostIndex, rightmostIndex)".to_string()),
            path: "quicksort.py".to_string(),
            ..Default::default()
        })
        .await;

        assert_eq!(response.text, "hello");
        assert_eq!(
            response.suggested_prefix_char_count,
            DEFAULT_PREFIX_CHAR_COUNT
        );
        assert_eq!(
            response.suggested_suffix_char_count,
            DEFAULT_SUFFIX_CHAR_COUNT
        );
        print!("{modelhost_request:?}");
        // default values filled in
        assert_eq!(
            modelhost_request,
            CompletionRequest {
                model_name: "model1".to_string(),
                prefix: "def quicksort".to_string(),
                suffix: "partition(array, leftmostIndex, rightmostIndex)".to_string(),
                path: "quicksort.py".to_string(),
                sequence_id: 0,
                probe_only: false,
                blobs: Some(blob_names_proto::Blobs::default()),
                ..Default::default()
            }
        );
    }

    #[actix_web::test]
    async fn test_complete_model_with_language() {
        let (modelhost_request, response) = run_completion(&public_api_proto::CompletionRequest {
            model: Some("model1".to_string()),
            prompt: "def quicksort".to_string(),
            suffix: None,
            path: "quicksort.py".to_string(),
            lang: Some("Python".into()),
            ..Default::default()
        })
        .await;

        assert_eq!(response.text, "hello");
        assert_eq!(
            response.suggested_prefix_char_count,
            DEFAULT_PREFIX_CHAR_COUNT
        );
        assert_eq!(
            response.suggested_suffix_char_count,
            DEFAULT_SUFFIX_CHAR_COUNT
        );
        print!("{modelhost_request:?}");
        // default values filled in
        assert_eq!(
            modelhost_request,
            CompletionRequest {
                model_name: "model1".to_string(),
                prefix: "def quicksort".to_string(),
                suffix: "".into(),
                path: "quicksort.py".to_string(),
                lang: "Python".to_string(),
                sequence_id: 0,
                probe_only: false,
                blobs: Some(blob_names_proto::Blobs::default()),
                ..Default::default()
            }
        );
    }

    #[actix_web::test]
    async fn test_complete_model_position() {
        let (modelhost_request, response) = run_completion(&public_api_proto::CompletionRequest {
            model: Some("model1".to_string()),
            prompt: "def quicksort".to_string(),
            path: "quicksort.py".to_string(),
            lang: Some("Python".into()),
            prefix_begin: Some(100),
            cursor_position: Some(200),
            suffix_end: Some(300),
            blob_name: Some("123e".to_string()),
            ..Default::default()
        })
        .await;

        assert_eq!(response.text, "hello");
        assert_eq!(
            response.suggested_prefix_char_count,
            DEFAULT_PREFIX_CHAR_COUNT
        );
        assert_eq!(
            response.suggested_suffix_char_count,
            DEFAULT_SUFFIX_CHAR_COUNT
        );
        print!("{modelhost_request:?}");
        // default values filled in
        assert_eq!(
            modelhost_request,
            CompletionRequest {
                model_name: "model1".to_string(),
                prefix: "def quicksort".to_string(),
                suffix: "".into(),
                path: "quicksort.py".to_string(),
                lang: "Python".to_string(),
                position: Some(CompletionPosition {
                    prefix_begin: 100,
                    cursor_position: 200,
                    suffix_end: 300,
                    blob_name: "123e".into()
                }),
                sequence_id: 0,
                probe_only: false,
                blobs: Some(blob_names_proto::Blobs::default()),
                ..Default::default()
            }
        );
    }

    #[actix_web::test]
    async fn test_complete_probe_only() {
        #[allow(deprecated)]
        let (modelhost_request, _response) = run_completion(&public_api_proto::CompletionRequest {
            model: Some("model1".to_string()),
            prompt: "".to_string(),
            probe_only: Some(true),
            ..Default::default()
        })
        .await;

        // default values filled in
        assert_eq!(
            modelhost_request,
            CompletionRequest {
                model_name: "model1".to_string(),
                prefix: "".to_string(),
                suffix: "".to_string(),
                path: "".to_string(),
                max_tokens: 0,
                lang: "".to_string(),
                seed: None,
                truncation: vec![],
                sequence_id: 0,
                probe_only: true,
                blobs: Some(blob_names_proto::Blobs::default()),
                ..Default::default()
            }
        );
    }

    #[actix_web::test]
    async fn test_completion_filter_threshold() {
        let (modelhost_request, _response) = run_completion(&public_api_proto::CompletionRequest {
            model: Some("model1".to_string()),
            prompt: "".to_string(),
            filter_threshold: Some(0.5),
            ..Default::default()
        })
        .await;

        // default values filled in
        assert_eq!(
            modelhost_request,
            CompletionRequest {
                model_name: "model1".to_string(),
                prefix: "".to_string(),
                suffix: "".to_string(),
                path: "".to_string(),
                max_tokens: 0,
                lang: "".to_string(),
                seed: None,
                truncation: vec![],
                sequence_id: 0,
                filter_threshold: Some(0.5),
                blobs: Some(blob_names_proto::Blobs::default()),
                ..Default::default()
            }
        );
    }

    #[actix_web::test]
    async fn test_complete_model_with_recency_info() {
        let (modelhost_request, response) = run_completion(&public_api_proto::CompletionRequest {
            model: Some("model1".to_string()),
            prompt: "def quicksort".to_string(),
            path: "quicksort.py".to_string(),
            blob_name: Some("123e".to_string()),
            recency_info: Some(public_api_proto::RecencyInfo {
                tab_switch_events: vec![
                    public_api_proto::TabSwitchEvent {
                        path: "path1".to_string(),
                        file_blob_name: "blob1".to_string(),
                    },
                    public_api_proto::TabSwitchEvent {
                        path: "path2".to_string(),
                        file_blob_name: "blob2".to_string(),
                    },
                ],
                viewed_contents: vec![public_api_proto::ViewedContentEvent {
                    path: "path3".to_string(),
                    file_blob_name: "blob3".to_string(),
                    visible_content: "def foo():\n    pass".to_string(),
                    line_start: 10,
                    line_end: 25,
                    char_start: 100,
                    char_end: 200,
                    timestamp: Some(prost_wkt_types::Timestamp {
                        seconds: 123456,
                        nanos: 789000000,
                    }),
                }],
                git_diff_file_info: vec![
                    public_api_proto::GitDiffFileInfo {
                        content_blob_name: "blob1".to_string(),
                        file_blob_name: "blob1".to_string(),
                    },
                    public_api_proto::GitDiffFileInfo {
                        content_blob_name: "blob2".to_string(),
                        file_blob_name: "blob2".to_string(),
                    },
                ],
                recent_changes: vec![public_api_proto::ReplacementText {
                    blob_name: "blob1".to_string(),
                    path: "file.py".to_string(),
                    replacement_text: "content1".to_string(),
                    char_start: 0,
                    char_end: 10,
                    present_in_blob: false,
                    expected_blob_name: None,
                    timestamp: Some(prost_wkt_types::Timestamp {
                        seconds: 1234567890,
                        nanos: 123456789,
                    }),
                }],
            }),
            ..Default::default()
        })
        .await;

        assert_eq!(response.text, "hello");
        assert_eq!(
            response.suggested_prefix_char_count,
            DEFAULT_PREFIX_CHAR_COUNT
        );
        assert_eq!(
            response.suggested_suffix_char_count,
            DEFAULT_SUFFIX_CHAR_COUNT
        );
        print!("{modelhost_request:?}");
        // default values filled in
        assert_eq!(
            modelhost_request,
            CompletionRequest {
                model_name: "model1".to_string(),
                prefix: "def quicksort".to_string(),
                suffix: "".into(),
                path: "quicksort.py".to_string(),
                recency_info: Some(RecencyInfo {
                    tab_switch_events: vec![
                        TabSwitchEvent {
                            path: "path1".to_string(),
                            file_blob_name: "blob1".to_string(),
                        },
                        TabSwitchEvent {
                            path: "path2".to_string(),
                            file_blob_name: "blob2".to_string(),
                        },
                    ],
                    viewed_contents: vec![ViewedContentEvent {
                        path: "path3".to_string(),
                        file_blob_name: "blob3".to_string(),
                        visible_content: "def foo():\n    pass".to_string(),
                        line_start: 10,
                        line_end: 25,
                        char_start: 100,
                        char_end: 200,
                        timestamp: Some(prost_wkt_types::Timestamp {
                            seconds: 123456,
                            nanos: 789000000,
                        }),
                    }],
                    git_diff_file_info: vec![
                        GitDiffFileInfo {
                            content_blob_name: "blob1".to_string(),
                            file_blob_name: "blob1".to_string(),
                        },
                        GitDiffFileInfo {
                            content_blob_name: "blob2".to_string(),
                            file_blob_name: "blob2".to_string(),
                        },
                    ],
                    recent_changes: vec![ReplacementText {
                        blob_name: "blob1".to_string(),
                        path: "file.py".to_string(),
                        replacement_text: "content1".to_string(),
                        char_start: 0,
                        char_end: 10,
                        present_in_blob: false,
                        expected_blob_name: None,
                        timestamp: Some(prost_wkt_types::Timestamp {
                            seconds: 1234567890,
                            nanos: 123456789,
                        }),
                    }],
                }),
                blobs: Some(blob_names_proto::Blobs::default()),
                ..Default::default()
            }
        );
    }

    #[actix_web::test]
    async fn test_complete_model_with_explicit_sampling_values() {
        let (modelhost_request, response) = run_completion(&public_api_proto::CompletionRequest {
            model: Some("model1".to_string()),
            prompt: "def quicksort".to_string(),
            suffix: None,
            path: "quicksort.py".to_string(),
            top_k: Some(1),
            top_p: Some(1.0),
            temperature: Some(1.0),
            max_tokens: Some(100),
            ..Default::default()
        })
        .await;

        assert_eq!(response.text, "hello");
        assert_eq!(
            response.suggested_prefix_char_count,
            DEFAULT_PREFIX_CHAR_COUNT
        );
        assert_eq!(
            response.suggested_suffix_char_count,
            DEFAULT_SUFFIX_CHAR_COUNT
        );

        print!("{modelhost_request:?}");
        // default values filled in
        assert_eq!(
            modelhost_request,
            CompletionRequest {
                model_name: "model1".to_string(),
                prefix: "def quicksort".to_string(),
                suffix: "".to_string(),
                path: "quicksort.py".to_string(),
                blobs: Some(blob_names_proto::Blobs::default()),
                max_tokens: 100,
                lang: "".to_string(),
                sequence_id: 0,
                probe_only: false,
                ..Default::default()
            }
        );
    }

    #[actix_web::test]
    async fn test_complete_model_not_specified() {
        let (modelhost_request, response) = run_completion(&public_api_proto::CompletionRequest {
            model: None,
            prompt: "def quicksort".to_string(),
            ..Default::default()
        })
        .await;

        assert_eq!(response.text, "hello");
        assert_eq!(
            response.suggested_prefix_char_count,
            DEFAULT_PREFIX_CHAR_COUNT
        );
        assert_eq!(
            response.suggested_suffix_char_count,
            DEFAULT_SUFFIX_CHAR_COUNT
        );
        print!("{modelhost_request:?}");
        // default values, including model_name, filled in
        assert_eq!(
            modelhost_request,
            CompletionRequest {
                model_name: "model1".to_string(),
                prefix: "def quicksort".to_string(),
                sequence_id: 0,
                probe_only: false,
                blobs: Some(blob_names_proto::Blobs::default()),
                ..Default::default()
            }
        );
    }

    #[actix_web::test]
    async fn test_complete_empty_model_name() {
        let (modelhost_request, response) = run_completion(&public_api_proto::CompletionRequest {
            model: Some("".to_string()),
            prompt: "def quicksort".to_string(),
            ..Default::default()
        })
        .await;

        assert_eq!(response.text, "hello");
        assert_eq!(
            response.suggested_prefix_char_count,
            DEFAULT_PREFIX_CHAR_COUNT
        );
        assert_eq!(
            response.suggested_suffix_char_count,
            DEFAULT_SUFFIX_CHAR_COUNT
        );
        print!("{modelhost_request:?}");
        // default values, including model_name, filled in
        assert_eq!(
            modelhost_request,
            CompletionRequest {
                model_name: "model1".to_string(),
                prefix: "def quicksort".to_string(),
                sequence_id: 0,
                probe_only: false,
                blobs: Some(blob_names_proto::Blobs::default()),
                ..Default::default()
            }
        );
    }
}
