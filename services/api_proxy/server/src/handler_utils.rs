use std::collections::HashMap;
use std::future::Future;
use std::time::{Duration, Instant};

use crate::public_api_proto::get_models_response::UserTier;
use actix_web::{http, web, HttpMessage, HttpRequest, HttpResponse, HttpResponseBuilder};
use chrono::{DateTime, Utc};
use feature_flags::FeatureFlagsServiceHandle;
use futures::stream::Stream;
use hmac::{Hmac, Mac};
use moka::sync::Cache;
use rand::Rng;
use regex::Regex;
use request_context::TenantInfo;
use serde::{Deserialize, Serialize};
use sha2::Sha256;
use std::pin::Pin;
use std::task::{Context, Poll};
use tokenbucket::TokenBucket;
use tokio::sync::mpsc::Receiver;
use tokio_stream::{wrappers::ReceiverStream, StreamExt};
use tonic::metadata::{<PERSON><PERSON><PERSON>, <PERSON>ada<PERSON><PERSON><PERSON>, MetadataValue};

use crate::agents_client::AgentsClient;
use crate::api_auth::{self, User};
use crate::augment::model_instance_config::ModelInstanceConfig;
use crate::augment::model_instance_config::ModelType;
use crate::base::blob_names as blob_names_proto;
use crate::base::diff_utils::{GranularEditEvent, SingleEdit};
use crate::completion::ReplacementText;
use crate::config::Config;
use crate::error_details_proto;
use crate::generation_clients::{Client, ResponseStatusCode};
use crate::handlers_external_sources::ExternalSourceClients;
use crate::metrics::{
    CIRCUIT_BREAKER_OPEN_COLLECTOR, HANDLER_LATENCY_COLLECTOR, HANDLER_RETRY_COLLECTOR,
    THROTTLE_COUNT_COLLECTOR, THROTTLE_EVICTION_COUNT_COLLECTOR,
};
use crate::model_registry::ModelRegistry;
use crate::public_api_proto;
use crate::request_insight_util::RequestInsightPublisher;
use auth_central_client::AuthCentralClient;
use auth_entities_proto::auth_entities::user_id::UserIdType;
use content_manager_client::ContentManagerClient;

use crate::metrics::STREAM_LATENCY_COLLECTOR;
use blob_names::BlobName;
use moka::notification::RemovalCause;
use moka::policy;
use moka::sync::CacheBuilder;
use request_context::{RequestContext, RequestId, NIL_REQUEST_ID};
use rustls::{ClientConfig, RootCertStore};
use rustls_native_certs::load_native_certs;
use secrecy::{ExposeSecret, SecretString};
use std::sync::Arc;

/// Parse the User-Agent string to extract client type and version
/// Returns a tuple of (client_type, client_version) where either or both can be None
/// Handles various User-Agent formats from different clients:
///
/// 1. IntelliJ format: "augment.intellij/0.75.0 (Mac OS X; aarch64; 10.15.7) IntelliJ IDEA/2023.1"
/// 2. VSCode format: "Augment.vscode-augment/0.75.0 (darwin; arm64; 20.3.0) vscode/1.90.2"
/// 3. Vim format: "Augment.vim/0.5.0 neovim/0.9.0"
/// 4. Python format: "api_proxy_client/0 (Python)"
pub fn parse_user_agent(user_agent: &str) -> Option<(Option<&str>, Option<&str>)> {
    // Define a regex to match user agent patterns
    // Format: "prefix/version" or "prefix/version rest..."
    // Captures the prefix and version
    lazy_static::lazy_static! {
        static ref USER_AGENT_REGEX: Regex = Regex::new(
            r"^([^/]+)/([^\s]+)"
        ).expect("Failed to compile user agent regex");

        // List of known client identifiers to match against
        // The first element is the pattern to match in the user agent
        // The second element is the client type to return
        static ref KNOWN_CLIENTS: Vec<(&'static str, &'static str)> = vec![
            ("augment.intellij", "intellij"),
            ("augment.vscode-augment", "vscode"),
            ("augment.vim", "vim"),
            ("augment.cli", "cli"),
            ("api_proxy_client", "python"),
        ];
    }

    // Try to match the user agent string against our regex
    if let Some(captures) = USER_AGENT_REGEX.captures(user_agent) {
        // Extract the prefix and version from the captures
        if let (Some(prefix_match), Some(version_match)) = (captures.get(1), captures.get(2)) {
            let prefix = prefix_match.as_str();
            let version = version_match.as_str();
            let prefix_lower = prefix.to_lowercase();

            // Check if this is one of our known client types
            // Use case-insensitive matching for more robustness
            for (identifier, client_type) in KNOWN_CLIENTS.iter() {
                if prefix_lower.contains(&identifier.to_lowercase()) {
                    return Some((Some(client_type), Some(version)));
                }
            }
        }
    }

    // Couldn't parse the User-Agent
    None
}

/// Compare two semantic version strings
/// Returns true if current_version >= min_version, false otherwise
/// Handles version strings like "0.487.1", "1.2.3-beta", etc.
pub fn is_version_gte(current_version: &str, min_version: &str) -> bool {
    if min_version.is_empty() {
        return true; // No minimum version requirement
    }

    if current_version.is_empty() {
        return false; // No current version available
    }

    // Parse version strings into (major, minor, patch) tuples
    let parse_version = |version: &str| -> Option<(u32, u32, u32)> {
        // Remove any pre-release suffix (e.g., "-beta", "-SNAPSHOT")
        let version_core = version.split('-').next().unwrap_or(version);

        let parts: Vec<&str> = version_core.split('.').collect();
        if parts.len() >= 3 {
            let major = parts[0].parse::<u32>().ok()?;
            let minor = parts[1].parse::<u32>().ok()?;
            let patch = parts[2].parse::<u32>().ok()?;
            Some((major, minor, patch))
        } else {
            None
        }
    };

    let current = match parse_version(current_version) {
        Some(v) => v,
        None => {
            tracing::warn!("Failed to parse current version: {}", current_version);
            return false;
        }
    };

    let minimum = match parse_version(min_version) {
        Some(v) => v,
        None => {
            tracing::warn!("Failed to parse minimum version: {}", min_version);
            return true; // If we can't parse the minimum, allow the request
        }
    };

    // Compare versions: current >= minimum
    current >= minimum
}

/// Check if a trial user needs to upgrade their client version
/// Returns true if their version is below the minimum required
/// Note: This function assumes the caller has already verified the user is on trial
pub fn should_enforce_version_upgrade(
    user_agent: Option<&str>,
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
) -> bool {
    // If user-agent header is missing, force upgrade for trial users
    let user_agent_str = match user_agent {
        Some(ua) => ua,
        None => return true, // Force upgrade if no user-agent header
    };

    // Parse the user agent to get client type and version
    let (client_type, client_version) = match parse_user_agent(user_agent_str) {
        Some((Some(client), Some(version))) => (client, version),
        _ => {
            // Unknown user agent - log and allow (don't force upgrade)
            tracing::info!(
                "Unknown user agent '{}' - not enforcing version upgrade for trial user",
                user_agent_str
            );
            return false;
        }
    };

    // Get the minimum version for this client type
    let min_version = match client_type {
        "vscode" => crate::handlers_chat::TRIAL_MIN_VSCODE_VERSION.get_from(feature_flags),
        "intellij" => crate::handlers_chat::TRIAL_MIN_INTELLIJ_VERSION.get_from(feature_flags),
        _ => {
            // Recognized client (like vim, python) but not subject to version enforcement
            tracing::info!(
                "Client type '{}' not subject to version enforcement, not enforcing upgrade",
                client_type
            );
            return false; // Don't enforce for other recognized clients
        }
    };

    // If no minimum version is set, don't enforce
    if min_version.is_empty() {
        return false;
    }

    // Check if current version is below minimum
    !is_version_gte(client_version, &min_version)
}

use std::sync::Mutex;
use tracing_actix_web::RootSpan;

pub const RETRY_DELAY_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_retry_delay_ms", 100);

pub const RETRY_MAX_DELAY_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_retry_max_delay_ms", 200);

pub const MAX_RETRIES_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_max_retries", 1);

// Feature flags for request timeouts. Every endpoint should get its own flag.
pub const UPLOAD_BLOB_CONTENT_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("upload_blob_content_timeout_ms", 60 * 1000);
pub const BATCH_UPLOAD_BLOB_CONTENT_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("batch_upload_blob_content_timeout_ms", 60 * 1000);
pub const FIND_MISSING_BLOBS_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("find_missing_blobs_timeout_ms", 60 * 1000);
pub const CHECKPOINT_BLOBS_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("checkpoint_blobs_timeout_ms", 60 * 1000);

// how long to sleep when we're throttled
pub const THROTTLE_SLEEP_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_throttle_sleep_ms", 1000);

// how long to block for when we're throttled
pub const THROTTLE_BLOCK_DURATION_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_throttle_block_duration_ms", 0);

// whether to add a retry-after header when we're throttled
pub const THROTTLE_ADD_RETRY_AFTER_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_throttle_add_retry_after", true);

/// A regex to match against user agents.
/// the feature flag usually require ^ and $ to match the entire user agent.
pub const SUSPICIOUS_USER_AGENT: feature_flags::StringFlag =
    feature_flags::StringFlag::new("api_proxy_suspicious_user_agent_regex", "");

// Feature flag to enable/disable CLI access
pub const CLI_ACCESS_ENABLED: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_cli_access_enabled", true);

/// Maximum number of IP addresses allowed per user ID within the last hour
/// note that this is per api-proxy. we route from the LB to api proxy with sticky sessions per ClientIP.
/// so if a user has many IP addresses, they are likely to be routed to all api proxies in roughly equal numbers.
pub const MAX_IPS_PER_USER_HOUR: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_suspicious_max_ips_per_user_hour", 16);

// Feature flags for sleep duration when handling blocked requests
pub const BLOCKED_SLEEP_LOWER_BOUND_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("blocked_sleep_lower_bound", 10.0);

pub const BLOCKED_SLEEP_UPPER_BOUND_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("blocked_sleep_upper_bound", 20.0);

pub fn register_handler_utils_flags(registry: &feature_flags::RegistryHandle) {
    UPLOAD_BLOB_CONTENT_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering UPLOAD_BLOB_CONTENT_TIMEOUT_MS_FLAG");
    BATCH_UPLOAD_BLOB_CONTENT_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering BATCH_UPLOAD_BLOB_CONTENT_TIMEOUT_MS_FLAG");
    FIND_MISSING_BLOBS_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering FIND_MISSING_BLOBS_TIMEOUT_MS_FLAG");
    CHECKPOINT_BLOBS_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering CHECKPOINT_BLOBS_TIMEOUT_MS_FLAG");
    BLOCKED_SLEEP_LOWER_BOUND_FLAG
        .register(registry)
        .expect("Registering BLOCKED_SLEEP_LOWER_BOUND_FLAG");
    BLOCKED_SLEEP_UPPER_BOUND_FLAG
        .register(registry)
        .expect("Registering BLOCKED_SLEEP_UPPER_BOUND_FLAG");
    CLI_ACCESS_ENABLED
        .register(registry)
        .expect("Registering CLI_ACCESS_ENABLED");
}

pub struct Handler<MR: ModelRegistry, CNC: ContentManagerClient> {
    pub config: Config,
    pub api_auth: Arc<api_auth::ApiAuth>,
    pub model_registry: Arc<MR>,
    pub request_insight_publisher: Arc<dyn RequestInsightPublisher + Send + Sync>,
    pub content_manager_client: CNC,
    pub external_source_clients: ExternalSourceClients,
    pub feature_flags: feature_flags::FeatureFlagsServiceHandle,
    hmac_secret: Option<SecretString>,
    pub share_client: Arc<dyn share_client::ShareClient + Send + Sync>,
    pub agents_client: Arc<dyn AgentsClient>,
    pub auth_central_client: Arc<dyn AuthCentralClient>,
    pub remote_agents_client: Arc<dyn remote_agents_client::RemoteAgentsClient>,
    pub remote_agent_actions_client: Arc<dyn remote_agent_actions_client::RemoteAgentActionsClient>,
    pub github_processor_client: Arc<dyn github_processor_client::GithubProcessorClient>,
    pub memstore_client: Arc<dyn memstore_client::MemstoreClient>,
    pub notification_client: Arc<notification_client::NotificationClientImpl>,
    pub ssl_config: SslConfig,
    // keyed on user id + endpoint
    pub throttle_cache: Arc<ThrottleCache>,
    pub suspicious_user_check: SuspiciousUserCheck,
}

pub trait EndpointHandler<T> {
    fn get_retry_policy(&self, _req: &HttpRequest) -> RetryPolicy {
        NONE_RETRY_POLICY
    }

    /// handle the request and return a http response in the success case or a status in the error case
    async fn handle(
        &self,
        req: &HttpRequest,
        item: T,
        root_span: RootSpan,
    ) -> tonic::Result<HttpResponse>;
}

pub fn gate_on_circuit_breaker(
    cb_flag: &feature_flags::BoolFlag,
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
    req: &HttpRequest,
    tenant_info: &TenantInfo,
) -> Result<(), tonic::Status> {
    if cb_flag.get_from(feature_flags) {
        tracing::warn!("Circuit breaker is open");
        CIRCUIT_BREAKER_OPEN_COLLECTOR
            .with_label_values(&[req.uri().path(), tenant_info.metrics_tenant_name()])
            .inc();
        return Err(tonic::Status::resource_exhausted("Circuit breaker is open"));
    }
    Ok(())
}

// map from ip address to timestamp
type UserIpMap = HashMap<String, DateTime<Utc>>;

/// maintains a mapping of user id to ip addresses and their timestamps
///
/// we keep the entry for each user id. Given that user ids are very limited
/// in number, this should not be a problem.
pub struct UserIpMapping {
    duration: chrono::Duration,
    // Map of user ID to a map of IP addresses and when they were first seen
    // We use a HashMap to track the IPs and their timestamps
    user_ip_map: Cache<String, Arc<Mutex<UserIpMap>>>,
}

impl UserIpMapping {
    pub fn new(duration: chrono::Duration, cache_size: u64, cache_ttl_seconds: u64) -> Self {
        let listener = |key: Arc<String>, _, cause: RemovalCause| {
            let user_id = key.as_ref();
            match cause {
                RemovalCause::Expired => {
                    // Normal case, no logging needed
                }
                other_cause => {
                    // this should pretty much never happen, so very interesting if it does
                    tracing::warn!(
                        "User IP cache eviction: cause: {:?}, user_id: {}",
                        other_cause,
                        user_id
                    );
                }
            }
        };
        let cache = CacheBuilder::new(cache_size)
            .time_to_idle(Duration::from_secs(cache_ttl_seconds))
            .eviction_policy(policy::EvictionPolicy::lru())
            .eviction_listener(listener)
            .build();

        Self {
            duration,
            user_ip_map: cache,
        }
    }

    // Check if a user has used too many IP addresses within the last hour
    pub fn has_too_many_ips(
        &self,
        feature_flags: &feature_flags::FeatureFlagsServiceHandle,
        user_id: &str,
        client_addr: &str,
    ) -> tonic::Result<bool> {
        if client_addr.is_empty() {
            return Ok(false);
        }

        let current_max_ips = MAX_IPS_PER_USER_HOUR.get_from(feature_flags);

        // If the feature flag is disabled (0 or negative), don't check IP addresses
        if current_max_ips <= 0 {
            return Ok(false);
        }

        // Get the current time
        let now = Utc::now();

        let entry = self
            .user_ip_map
            .get_with(user_id.to_string(), || Arc::new(Mutex::new(HashMap::new())));
        let mut user_ip_map = match entry.lock() {
            Ok(guard) => guard,
            Err(e) => {
                tracing::error!("Failed to acquire mutex lock: {}", e);
                return Err(tonic::Status::internal("Failed to acquire mutex lock"));
            }
        };

        // Clean up old IP entries (older than the lookup window)
        user_ip_map.retain(|_, timestamp| *timestamp > now - self.duration);

        // Add the current IP if it's not already in the map
        user_ip_map.entry(client_addr.to_string()).or_insert(now);

        // Check if the user has used too many IP addresses
        let ip_count = user_ip_map.len() as i64;
        if ip_count > current_max_ips {
            tracing::warn!(
                "User {} has used {} IP addresses in lookup window (max allowed: {})",
                user_id,
                ip_count,
                current_max_ips
            );
            Ok(true)
        } else {
            Ok(false)
        }
    }
}

/// internal state for suspicious user check
#[derive(Clone)]
struct SuspiciousUserState {
    // the last feature flag value we saw
    user_agent_last_feature_flag: String,

    // the compiled regex
    user_agent_regex: Regex,
}

pub struct SuspiciousUserCheck {
    // internal state
    state: Arc<Mutex<Option<SuspiciousUserState>>>,
    user_ip_mapping: UserIpMapping,
}

impl SuspiciousUserCheck {
    pub fn new(user_ip_mapping: UserIpMapping) -> Self {
        Self {
            state: Arc::new(Mutex::new(None)),
            user_ip_mapping,
        }
    }

    // get the current state, or None if there is no regex
    fn get_state(
        &self,
        feature_flags: &feature_flags::FeatureFlagsServiceHandle,
    ) -> Option<SuspiciousUserState> {
        let current_feature_flag = SUSPICIOUS_USER_AGENT.get_from(feature_flags);
        if current_feature_flag.is_empty() {
            // no regex -> no state
            return None;
        }
        let mut guard = self.state.lock().unwrap();
        if let Some(state) = guard.as_ref() {
            if state.user_agent_last_feature_flag == current_feature_flag {
                // same regex -> return state
                return Some(state.clone());
            }
        }
        match Regex::new(&current_feature_flag) {
            Ok(regex) => {
                // new regex -> new state
                let state = SuspiciousUserState {
                    user_agent_last_feature_flag: current_feature_flag.clone(),
                    user_agent_regex: regex,
                };
                *guard = Some(state.clone());
                Some(state)
            }
            Err(e) => {
                tracing::error!("Invalid regex for FORBID_USER_AGENT: {}", e);
                // return last
                guard.as_ref().cloned()
            }
        }
    }

    pub fn is_suspicious(
        &self,
        feature_flags: &feature_flags::FeatureFlagsServiceHandle,
        req: &HttpRequest,
    ) -> Result<bool, tonic::Status> {
        // Check user agent regex
        let state = self.get_state(feature_flags);
        if let Some(state) = state {
            let user_agent = req
                .headers()
                .get("user-agent")
                .map(|h| h.to_str().unwrap_or("not-utf8"))
                .unwrap_or("");
            if state.user_agent_regex.is_match(user_agent) {
                tracing::warn!("Suspicious user agent: {}", user_agent);
                return Ok(true);
            }
        }

        // Check if the user has used too many IP addresses
        if let Ok((user, _, _, _)) = request_context_from_req(req) {
            let client_addr = req
                .connection_info()
                .realip_remote_addr()
                .unwrap_or("")
                .to_owned();

            if self
                .user_ip_mapping
                .has_too_many_ips(feature_flags, &user.user_id, &client_addr)?
            {
                return Ok(true);
            }
        }

        Ok(false)
    }
}

pub struct ThrottleCache {
    cache: Cache<(String, String), Arc<Mutex<ThrottleEntry>>>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
}

impl ThrottleCache {
    pub fn new(
        cache_size: u64,
        cache_ttl_seconds: u64,
        feature_flags: feature_flags::FeatureFlagsServiceHandle,
    ) -> Self {
        // Set up the throttle cache. The cache is keyed on user id + endpoint. See notes
        // later in file on the token bucket throttling approach.
        //
        // Notes on cache setup specifically:
        //  -- the cache and all buckets will reset on redeploy. So we can expect that a redploy
        //    would enable new bursts for certain endpoints. This should be pefectly fine.
        //  -- resetting also has the nice side effect (in addition to the time to idle) of not
        //    accumulating buckets for users that are no longer active.
        //  -- requests are sticky per session. A truly malicious user could multiply their capacity
        //    with multiple sessions, but they'll still be rate limited.
        //  -- we set an LRU eviction policy. Default is LFU.
        //  -- cache size and ttl needs to be set at the global proxy level (no launch darkly)
        tracing::info!(
            "Throttle cache size: {}, time to idle: {}",
            cache_size,
            cache_ttl_seconds
        );

        let listener = |key: Arc<(String, String)>, _, cause: RemovalCause| {
            let (user_id, endpoint) = key.as_ref();
            match cause {
                RemovalCause::Expired => {
                    // Normal case, no logging needed
                }
                other_cause => {
                    // this should pretty much never happen, so very interesting if it does
                    tracing::warn!(
                        "Throttle cache eviction: endpoint: {}, cause: {:?}, user_id: {}",
                        endpoint,
                        other_cause,
                        user_id
                    );
                }
            }
            let cause = format!("{:?}", cause);
            THROTTLE_EVICTION_COUNT_COLLECTOR
                .with_label_values(&[endpoint, &cause])
                .inc();
        };
        let cache = CacheBuilder::new(cache_size)
            .time_to_idle(Duration::from_secs(cache_ttl_seconds))
            .eviction_policy(policy::EvictionPolicy::lru())
            .eviction_listener(listener)
            .build();
        Self {
            cache,
            feature_flags,
        }
    }

    pub async fn should_throttle(
        &self,
        user_id: &str,
        endpoint: &str,
        fill_rate_per_second: f64,
        capacity: f64,
        tokens_to_consume: f64,
    ) -> Result<(), tonic::Status> {
        let entry = self
            .cache
            .get_with((user_id.to_string(), endpoint.to_string()), || {
                let block_duration = Duration::from_millis(
                    THROTTLE_BLOCK_DURATION_MS_FLAG.get_from(&self.feature_flags) as u64,
                );
                let entry = ThrottleEntry::new(fill_rate_per_second, capacity, block_duration);
                Arc::new(Mutex::new(entry))
            });

        let throttle_result = match entry.lock() {
            Ok(mut guard) => guard.acquire(tokens_to_consume),
            Err(e) => {
                tracing::error!("Failed to acquire mutex lock: {}", e);
                return Err(tonic::Status::internal("Failed to acquire mutex lock"));
            }
        };
        match throttle_result {
            ThrottleResult::Throttled(blocked_until) => {
                tracing::warn!(
                    "Throttling user: {}, endpoint: {}, until: {:?}",
                    user_id,
                    endpoint,
                    blocked_until
                );
                let sleep_ms = THROTTLE_SLEEP_MS_FLAG.get_from(&self.feature_flags);
                tokio::time::sleep(Duration::from_millis(sleep_ms as u64)).await;
                THROTTLE_COUNT_COLLECTOR
                    .with_label_values(&[endpoint])
                    .inc();

                let mut status = tonic::Status::resource_exhausted("Too many requests");
                if THROTTLE_ADD_RETRY_AFTER_FLAG.get_from(&self.feature_flags)
                    && blocked_until.is_some()
                {
                    let formatted = blocked_until
                        .unwrap()
                        .format("%a, %d %b %Y %H:%M:%S GMT")
                        .to_string();
                    let key: MetadataKey<Ascii> = MetadataKey::from_static("blocked_until");
                    let value: MetadataValue<Ascii> = MetadataValue::try_from(formatted.as_str())
                        .map_err(|e| {
                        tonic::Status::internal(format!("Failed to format metadata: {}", e))
                    })?;
                    status.metadata_mut().insert(key, value);
                }
                Err(status)
            }
            ThrottleResult::NotThrottled => Ok(()),
        }
    }

    pub async fn try_consume_tokens(&self, user_id: &str, endpoint: &str, tokens_to_consume: f64) {
        let entry = self.cache.get(&(user_id.to_string(), endpoint.to_string()));
        if let Some(entry) = entry {
            let mut entry = entry.lock().unwrap();
            // Ignore the error
            let _ = entry.token_bucket.acquire(tokens_to_consume);
        }
    }
}

pub enum ThrottleResult {
    // throttled until this time
    Throttled(Option<DateTime<Utc>>),
    // not throttled
    NotThrottled,
}

// For basic rate limiting we use a simple token bucket.
// Note: content manager also does rate limiting with a token bucket, but from looking at that
// code there's really not enough code to justify a shared code push.
//
// https://en.wikipedia.org/wiki/Token_bucket
// Essentially this is a rate limiting algorithm that allows bursts up to a certain
// capacity, and then refreshes the allowed capacity at a specified rate.
// This is meant to be a generic infra that can be applied with different capacities
// and rate refreshes via feature flags in a way that suits the needs of different endpoints.
// For instance, chat could have a refresh rate of 1/s, with a max capacity of 1, meaning
// we're imposing a strict rate limit of one request per second. Whearas for file upload,
// we could set a capacity on the order of a million, but set rate
// limits to not fully refresh that capacity for 12 hours
// (or something).
pub struct ThrottleEntry {
    token_bucket: TokenBucket,
    block_duration: Duration,
    blocked_until: Option<DateTime<Utc>>,
}
impl ThrottleEntry {
    fn new(fill_rate_per_second: f64, capacity: f64, block_duration: Duration) -> Self {
        tracing::info!(
            "ThrottleEntry::new: fill_rate_per_second: {}, capacity: {} blocked_duration: {}",
            fill_rate_per_second,
            capacity,
            block_duration.as_secs()
        );
        Self {
            token_bucket: TokenBucket::new(fill_rate_per_second, capacity),
            block_duration,
            blocked_until: None,
        }
    }

    pub fn acquire(&mut self, tokens_to_consume: f64) -> ThrottleResult {
        let now = Utc::now();
        if let Some(blocked_until) = self.blocked_until {
            if blocked_until > now {
                return ThrottleResult::Throttled(Some(blocked_until));
            }
        }
        let throttled = self.token_bucket.acquire(tokens_to_consume).is_err();
        if throttled && self.block_duration > Duration::from_secs(0) {
            self.blocked_until = Some(now + self.block_duration);
        }
        if throttled {
            ThrottleResult::Throttled(self.blocked_until)
        } else {
            ThrottleResult::NotThrottled
        }
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> Handler<MR, CNC> {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        config: Config,
        api_auth: Arc<api_auth::ApiAuth>,
        model_registry: Arc<MR>,
        request_insight_publisher: Arc<dyn RequestInsightPublisher + Send + Sync>,
        content_manager_client: CNC,
        external_source_clients: ExternalSourceClients,
        feature_flags: feature_flags::FeatureFlagsServiceHandle,
        share_client: Arc<dyn share_client::ShareClient + Send + Sync>,
        agents_client: Arc<dyn AgentsClient>,
        auth_central_client: Arc<dyn AuthCentralClient>,
        remote_agents_client: Arc<dyn remote_agents_client::RemoteAgentsClient>,
        remote_agent_actions_client: Arc<dyn remote_agent_actions_client::RemoteAgentActionsClient>,
        github_processor_client: Arc<dyn github_processor_client::GithubProcessorClient>,
        memstore_client: Arc<dyn memstore_client::MemstoreClient>,
        notification_client: Arc<notification_client::NotificationClientImpl>,
        throttle_cache: Arc<ThrottleCache>,
    ) -> Self {
        let hmac_secret = std::fs::read(&config.hmac_secret_path)
            .ok()
            .and_then(|b| String::from_utf8(b).ok())
            .map(SecretString::from);

        let cache_size = config.throttle_cache_size;
        let cache_ttl_seconds = config.throttle_cache_ttl_seconds;

        let user_ip_mapping =
            UserIpMapping::new(chrono::Duration::hours(1), cache_size, cache_ttl_seconds);

        let suspicious_user_check = SuspiciousUserCheck::new(user_ip_mapping);

        Handler {
            config,
            api_auth,
            model_registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags,
            hmac_secret,
            share_client,
            agents_client,
            auth_central_client,
            remote_agents_client,
            remote_agent_actions_client,
            github_processor_client,
            memstore_client,
            notification_client,
            ssl_config: SslConfig::new(),
            throttle_cache,
            suspicious_user_check,
        }
    }

    pub fn get_feature_flags(
        &self,
        user: &User,
        tenant_info: &TenantInfo,
        req: Option<&HttpRequest>,
    ) -> Result<FeatureFlagsServiceHandle, tonic::Status> {
        let mut bound_flags = self
            .feature_flags
            .bind_attribute("tenant_name", &tenant_info.tenant_name)
            .map_err(|e| {
                tracing::error!("get_feature_flags failed: {:?}", e);
                tonic::Status::internal("Failed to bind feature flags to tenant")
            })?;

        if user.opaque_user_id.user_id_type == UserIdType::Augment as i32 {
            // Other types which don't take this branch are (at time of writing)
            // - Api Token
            // - Slack
            bound_flags = bound_flags
                .bind_attribute("user_uuid", &user.opaque_user_id.user_id)
                .map_err(|e| {
                    tracing::error!("get_feature_flags failed: {:?}", e);
                    tonic::Status::internal("Failed to bind feature flags to user uuid")
                })?;
        }

        if user.opaque_user_id.user_id_type == UserIdType::ApiToken as i32 {
            bound_flags = bound_flags
                .bind_attribute("api_key_user_id", &user.opaque_user_id.user_id)
                .map_err(|e| {
                    tracing::error!("get_feature_flags failed: {:?}", e);
                    tonic::Status::internal("Failed to bind feature flags to user uuid")
                })?;
        }

        if let Some(ref hmac_secret) = self.hmac_secret {
            // We hash the user ID with HMAC-SHA-256 to avoid putting PII into git.
            let secret = hex::decode(hmac_secret.expose_secret()).unwrap();
            let mut mac = Hmac::<Sha256>::new_from_slice(&secret).unwrap();
            mac.update(user.user_id.as_bytes());
            let result = mac.finalize();
            let user_id_hmac = hex::encode(result.into_bytes());

            bound_flags = bound_flags
                .bind_attribute("user_id_hmac", &user_id_hmac)
                .map_err(|e| {
                    tracing::error!("get_feature_flags failed: {:?}", e);
                    tonic::Status::internal("Failed to bind feature flags to user hmac")
                })?;
        }

        // Extract client and clientVersion from User-Agent header if available
        if let Some(request) = req {
            if let Some(user_agent) = request.headers().get(http::header::USER_AGENT) {
                if let Ok(user_agent_str) = user_agent.to_str() {
                    // Parse the User-Agent string to extract client type and version
                    if let Some(client_info) = parse_user_agent(user_agent_str) {
                        // Bind client type if available
                        if let Some(client_type) = client_info.0 {
                            bound_flags = bound_flags
                                .bind_attribute("client", client_type)
                                .map_err(|e| {
                                    tracing::error!(
                                        "get_feature_flags failed to bind client: {:?}",
                                        e
                                    );
                                    tonic::Status::internal(
                                        "Failed to bind feature flags to client",
                                    )
                                })?;
                        }

                        // Bind client version if available
                        if let Some(client_version) = client_info.1 {
                            bound_flags = bound_flags
                                .bind_attribute("clientVersion", client_version)
                                .map_err(|e| {
                                    tracing::error!(
                                        "get_feature_flags failed to bind clientVersion: {:?}",
                                        e
                                    );
                                    tonic::Status::internal(
                                        "Failed to bind feature flags to clientVersion",
                                    )
                                })?;
                        }
                    }
                }
            }
        }

        Ok(bound_flags)
    }

    pub fn get_user_tier(&self) -> UserTier {
        // For now, we just return unknown. In the future, we can add logic to determine the user tier.
        let user_tier: UserTier = match self.config.user_tier {
            None => UserTier::Unknown,
            Some(ref user_tier) => match UserTier::from_str_name(user_tier.to_uppercase().as_str())
            {
                Some(user_tier) => user_tier,
                None => {
                    tracing::error!("Invalid user tier: {}", user_tier);
                    UserTier::Unknown
                }
            },
        };
        user_tier
    }

    pub fn get_retry_policy_from_flags(
        &self,
        req: &HttpRequest,
        retry_delay_flag: &feature_flags::IntFlag,
        retry_max_delay_flag: &feature_flags::IntFlag,
        retry_max_count_flag: &feature_flags::IntFlag,
    ) -> RetryPolicy {
        match request_context_from_req(req) {
            Ok((user, tenant_info, _request_context, _start_time)) => {
                let feature_flags = match self.get_feature_flags(&user, &tenant_info, Some(req)) {
                    Ok(feature_flags) => feature_flags,
                    Err(_) => {
                        return NONE_RETRY_POLICY;
                    }
                };
                RetryPolicy::new(
                    retry_delay_flag.get_from(&feature_flags),
                    retry_max_delay_flag.get_from(&feature_flags),
                    retry_max_count_flag.get_from(&feature_flags) as i32,
                )
            }
            Err(_) => NONE_RETRY_POLICY,
        }
    }

    pub fn get_retry_policy_from_default_flags(&self, req: &HttpRequest) -> RetryPolicy {
        self.get_retry_policy_from_flags(
            req,
            &RETRY_DELAY_MS_FLAG,
            &RETRY_MAX_DELAY_MS_FLAG,
            &MAX_RETRIES_FLAG,
        )
    }
}

// Helper to wrap handler functions
pub async fn handle_api_auth<MR: ModelRegistry, CNC: ContentManagerClient, T: Clone>(
    data: web::Data<Handler<MR, CNC>>,
    req: HttpRequest,
    item: web::Json<T>,
    root_span: RootSpan,
) -> HttpResponse
where
    Handler<MR, CNC>: EndpointHandler<T>,
{
    let start = Instant::now();
    let uri: &str = req.uri().path();
    let tenant_name = req
        .extensions()
        .get::<TenantInfo>()
        .map(|t| t.tenant_name.clone())
        .unwrap_or("unknown".to_string());
    let metrics_tenant_name = req
        .extensions()
        .get::<TenantInfo>()
        .map(|t| t.metrics_tenant_name().to_string())
        .unwrap_or("unknown".to_string());
    let opaque_user_id = req
        .extensions()
        .get::<User>()
        .map(|u| u.opaque_user_id.user_id.clone())
        .unwrap_or("unknown".to_string());
    let request_source = req
        .extensions()
        .get::<RequestContext>()
        .map(|s| s.request_source().to_string())
        .unwrap_or("unknown".to_string());

    root_span.record("tenant_name", &tenant_name);
    root_span.record("opaque_user_id", &opaque_user_id);

    let retry_policy = data.get_retry_policy(&req);
    let item = item.into_inner();
    let resp = retry(
        retry_policy,
        || async { data.handle(&req, item.clone(), root_span.clone()).await },
        |e| {
            HANDLER_RETRY_COLLECTOR
                .with_label_values(&[
                    uri,
                    e.code().to_string().as_str(),
                    &request_source,
                    &metrics_tenant_name,
                ])
                .inc();
        },
    )
    .await;
    let res = handle_response(&req, resp).await;
    let duration = start.elapsed();
    let code = res.status().as_u16();

    HANDLER_LATENCY_COLLECTOR
        .with_label_values(&[
            uri,
            &code.to_string(),
            &request_source,
            &metrics_tenant_name,
        ])
        .observe(duration.as_secs_f64());
    res
}

/// structure to return when the handler returns an error
#[derive(Serialize, Deserialize)]
pub struct ResponseError {
    error: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    error_details: Option<error_details_proto::ErrorDetails>,
}

impl ResponseError {
    /// construct a new response error
    pub fn new(error: &str, error_details: Option<error_details_proto::ErrorDetails>) -> Self {
        ResponseError {
            error: error.to_string(),
            error_details,
        }
    }
}

/// Extract error details from gRPC metadata if present
fn extract_error_details_from_metadata(
    status: &tonic::Status,
) -> Option<error_details_proto::ErrorDetails> {
    // Look for error-details in the metadata
    if let Some(error_details_value) = status.metadata().get("error-details") {
        if let Ok(error_details_str) = error_details_value.to_str() {
            // Try to parse the JSON error details using serde
            if let Ok(error_details) =
                serde_json::from_str::<error_details_proto::ErrorDetails>(error_details_str)
            {
                return Some(error_details);
            }
        }
    }
    None
}

/// transfers a status to a HTTP response with the status code set accordingly.
///
/// based on https://chromium.googlesource.com/external/github.com/grpc/grpc/+/refs/tags/v1.21.4-pre1/doc/statuscodes.md
///
/// The error message is returned as a json object with an "error" field.
pub fn status_to_response(status: &tonic::Status) -> HttpResponse {
    // Try to extract error details from gRPC metadata
    let error_details = extract_error_details_from_metadata(status);

    match status.code() {
        tonic::Code::InvalidArgument => HttpResponse::BadRequest().json(ResponseError::new(
            "Unidentified internal error",
            error_details,
        )),
        tonic::Code::FailedPrecondition => HttpResponse::BadRequest().json(ResponseError::new(
            "Unidentified internal error",
            error_details,
        )),
        tonic::Code::OutOfRange => HttpResponse::BadRequest().json(ResponseError::new(
            "Unidentified internal error",
            error_details,
        )),
        tonic::Code::Unimplemented => HttpResponse::NotImplemented()
            .json(ResponseError::new("Not implemented", error_details)),
        tonic::Code::DeadlineExceeded => {
            HttpResponse::GatewayTimeout().json(ResponseError::new("Timeout", error_details))
        }
        tonic::Code::AlreadyExists => HttpResponse::Conflict().json(ResponseError::new(
            "Unidentified internal error",
            error_details,
        )),
        tonic::Code::Aborted => HttpResponse::Conflict().json(ResponseError::new(
            "Unidentified internal error",
            error_details,
        )),
        tonic::Code::Unavailable => HttpResponse::ServiceUnavailable().json(ResponseError::new(
            "Unidentified internal error",
            error_details,
        )),
        tonic::Code::Cancelled => {
            HttpResponseBuilder::new(http::StatusCode::from_u16(499).unwrap())
                .json(ResponseError::new("", None))
        }
        tonic::Code::ResourceExhausted => match status.metadata().get("blocked_until") {
            Some(blocked_until) => HttpResponse::TooManyRequests()
                .append_header(("Retry-After", blocked_until.to_str().unwrap().to_string()))
                .json(ResponseError::new("Too many requests", error_details)),
            None => HttpResponse::TooManyRequests()
                .json(ResponseError::new("Too many requests", error_details)),
        },
        tonic::Code::NotFound => HttpResponse::NotFound().json(ResponseError::new(
            "Unidentified internal error",
            error_details,
        )),
        tonic::Code::PermissionDenied => {
            HttpResponse::Forbidden().json(ResponseError::new("Permission denied", error_details))
        }
        tonic::Code::Unauthenticated => {
            // This is a special error, we don't want to return it if some
            // internal server runs into an Unauthenticated error (e.g. from
            // token-exchange) since it will log users out of their extensions.
            // Real unauthenticated errors will come from AuthCheckMiddleware,
            // which does not use this function.
            HttpResponse::InternalServerError().json(ResponseError::new(
                "Unidentified internal error",
                error_details,
            ))
        }
        tonic::Code::Ok => HttpResponse::Ok().json(ResponseError::new("", None)),
        _ => HttpResponse::InternalServerError().json(ResponseError::new(
            "Unidentified internal error",
            error_details,
        )),
    }
}

pub fn request_context_from_req(
    req: &HttpRequest,
) -> Result<(User, TenantInfo, RequestContext, Instant), tonic::Status> {
    let user = match req.extensions().get::<User>() {
        Some(user) => Ok(user.clone()),
        None => Err(tonic::Status::internal(
            "User should have been set by middleware",
        )),
    }?;

    let tenant_info = match req.extensions().get::<TenantInfo>() {
        Some(tenant_info) => Ok(tenant_info.clone()),
        None => Err(tonic::Status::internal(
            "Tenant info should have been set by middleware",
        )),
    }?;

    let context = match req.extensions().get::<RequestContext>() {
        Some(context) => Ok(context.clone()),
        None => Err(tonic::Status::internal(
            "Request context should have been set by middleware",
        )),
    }?;

    let start_time = match req.extensions().get::<Instant>() {
        Some(start_time) => Ok(*start_time),
        None => Err(tonic::Status::internal(
            "Request start time should have been set by middleware",
        )),
    }?;

    Ok((user, tenant_info, context, start_time))
}

impl TryFrom<public_api_proto::FileEditEvent> for GranularEditEvent {
    type Error = tonic::Status;
    fn try_from(event: public_api_proto::FileEditEvent) -> Result<Self, Self::Error> {
        Ok(GranularEditEvent {
            path: event.path.clone(),
            before_blob_name: event.before_blob_name.clone(),
            after_blob_name: event.after_blob_name.clone(),
            edits: event
                .edits
                .into_iter()
                .map(|e| SingleEdit {
                    before_start: e.before_start,
                    after_start: e.after_start,
                    before_text: e.before_text.clone(),
                    after_text: e.after_text.clone(),
                })
                .collect(),
        })
    }
}

pub fn convert_replacement_text(
    recent_changes: &[public_api_proto::ReplacementText],
) -> Vec<ReplacementText> {
    recent_changes.iter().map(|r| r.into()).collect()
}

/// This function expects to get the direct inputs from the client, whether empty
/// or not. This handles backwards compatibility between the old
/// memories/blob_names format and the new blobs format, and returns a blobs
/// object for the rest of the backend.
pub fn convert_blobs_and_names(
    blobs: &Option<public_api_proto::Blobs>,
    blob_names: Option<&Vec<BlobName>>,
) -> Result<blob_names_proto::Blobs, tonic::Status> {
    // 0. At least one of the two fields should exist
    if blobs.is_none() && blob_names.is_none() {
        return Err(tonic::Status::invalid_argument(
            "Missing one of memories/blob_names and blobs",
        ));
    }

    // 1. If blobs exists and has values, use that
    if let Some(b) = blobs.as_ref() {
        if b.checkpoint_id.is_some() || !b.added_blobs.is_empty() {
            match b.try_into() {
                Ok(b) => return Ok(b),
                Err(_e) => return Err(tonic::Status::invalid_argument("Invalid blob name")),
            }
        }
    }

    // 2. Otherwise, if blob_names exists, convert it to blobs.added and use that
    if let Some(b) = blob_names {
        let mut added: Vec<Vec<u8>> = b.iter().map(|blob| Vec::from(blob.as_bytes())).collect();

        // Make sure to sort before putting into Blobs, just like
        // public_api_proto::Blobs.try_into<Blobs> does.
        added.sort();

        return Ok(blob_names_proto::Blobs {
            added,
            ..Default::default()
        });
    }

    // 3. If we get here, then we need to use blobs, even though it is empty
    match blobs.as_ref().unwrap().try_into() {
        Ok(b) => Ok(b),
        Err(_e) => Err(tonic::Status::invalid_argument("Invalid blob name")),
    }
}

/// Convert a frontend blob-names checkpoint into a backend proto version.
/// Blob names must be hex strings.
/// Failure to decode a blob name will result in an error.
///
/// The blobs are also expected to be sorted for newer formats, where a newer
/// format is one with a checkpoint ID. This corresponds to extension versions
/// that do sorting. If there is a checkpoint ID but the blobs are not sorted,
/// that will result in an error.
impl TryFrom<&public_api_proto::Blobs> for blob_names_proto::Blobs {
    type Error = tonic::Status;

    fn try_from(f: &public_api_proto::Blobs) -> Result<Self, Self::Error> {
        // Sort the provided blobs. Newer versions of the extension should be sorting their blobs
        // anyway, but for the sake of client convenience it's nice to ensure this on the server
        // instead of sending back an error.
        let mut added = f.added_blobs.clone();
        let mut deleted = f.deleted_blobs.clone();
        added.sort();
        deleted.sort();

        let added: Result<Vec<Vec<u8>>, _> = added
            .iter()
            .map(|hex_str| match hex::decode(hex_str) {
                Ok(id) => Ok(id),
                Err(_e) => Err(tonic::Status::invalid_argument("Invalid blob name")),
            })
            .collect();

        let deleted: Result<Vec<Vec<u8>>, _> = deleted
            .iter()
            .map(|hex_str| match hex::decode(hex_str) {
                Ok(id) => Ok(id),
                Err(_e) => Err(tonic::Status::invalid_argument("Invalid blob name")),
            })
            .collect();

        Ok(blob_names_proto::Blobs {
            baseline_checkpoint_id: f.checkpoint_id.clone(),
            added: added?,
            deleted: deleted?,
        })
    }
}

impl TryFrom<&public_api_proto::Blobs> for request_insight_publisher::base::blob_names::Blobs {
    type Error = hex::FromHexError;

    fn try_from(f: &public_api_proto::Blobs) -> Result<Self, Self::Error> {
        let added: Result<Vec<Vec<u8>>, _> = f.added_blobs.iter().map(hex::decode).collect();

        let deleted: Result<Vec<Vec<u8>>, _> = f.deleted_blobs.iter().map(hex::decode).collect();

        Ok(request_insight_publisher::base::blob_names::Blobs {
            baseline_checkpoint_id: f.checkpoint_id.clone(),
            added: added?,
            deleted: deleted?,
        })
    }
}

pub async fn handle_response(
    req: &HttpRequest,
    resp: Result<HttpResponse, tonic::Status>,
) -> HttpResponse {
    // Use the RequestId object, which is set by the first middleware and is the
    // most likely to be there.
    let extensions = req.extensions();
    let request_id = match extensions.get::<RequestId>() {
        Some(context) => context,
        None => &NIL_REQUEST_ID,
    };

    match resp {
        Err(status) => {
            if status.code() != tonic::Code::Cancelled {
                tracing::error!("Request {} failed: {}", request_id, status);
            } else {
                tracing::info!("Request {} cancelled: {}", request_id, status);
            }
            status_to_response(&status)
        }
        Ok(resp) => resp,
    }
}

/// A stream that emits a payload every second that there's
/// no activity from the main stream
struct HeartbeatStream<S> {
    main_stream: Pin<Box<S>>,
    heartbeat_interval: tokio::time::Interval,
    payload: web::Bytes,
}

impl<S> HeartbeatStream<S>
where
    S: Stream,
{
    fn new(main_stream: Pin<Box<S>>, payload: web::Bytes) -> Self {
        let mut heartbeat_interval = tokio::time::interval(Duration::from_secs(1));
        heartbeat_interval.set_missed_tick_behavior(tokio::time::MissedTickBehavior::Skip);

        Self {
            main_stream,
            heartbeat_interval,
            payload,
        }
    }
}

impl<S> Stream for HeartbeatStream<S>
where
    S: Stream<Item = Result<web::Bytes, tonic::Status>>,
{
    type Item = Result<web::Bytes, tonic::Status>;

    fn poll_next(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        match self.main_stream.as_mut().poll_next(cx) {
            Poll::Ready(Some(item)) => {
                self.heartbeat_interval.reset();
                return Poll::Ready(Some(item));
            }
            Poll::Ready(None) => {
                return Poll::Ready(None);
            }
            Poll::Pending => {
                // Main stream has no data ready, check if we should emit a heartbeat
            }
        }

        match self.heartbeat_interval.poll_tick(cx) {
            Poll::Ready(_) => Poll::Ready(Some(Ok(self.payload.clone()))),
            Poll::Pending => Poll::Pending,
        }
    }
}

pub async fn streaming_http_response_from_receiver<ModelResp, FrontResp>(
    endpoint: &str,
    receiver_result: tonic::Result<Receiver<tonic::Result<ModelResp>>>,
    request_context: RequestContext,
    tenant_info: TenantInfo,
    request_insight_publisher: Arc<dyn RequestInsightPublisher + Send + Sync>,
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
    enable_heartbeat: bool,
) -> HttpResponse
where
    ModelResp: TryInto<ResponseStatusCode> + Clone + 'static,
    FrontResp: From<ModelResp> + Serialize,
{
    let endpoint = endpoint.to_string();
    let start_time = Instant::now();
    let mut response_builder = HttpResponseBuilder::new(http::StatusCode::OK);
    response_builder.append_header(("content-type", "application/json"));

    let receiver = match receiver_result {
        Ok(mut receiver) => {
            let first_response = receiver.recv().await;
            match first_response {
                Some(Ok(first_response)) => {
                    let error_code = (first_response.clone()).try_into();
                    match error_code {
                        Ok(ResponseStatusCode::ExceedContextLength) => {
                            let response = HttpResponse::PayloadTooLarge().finish();
                            request_insight_publisher
                                .record_http_response(
                                    &request_context,
                                    &tenant_info,
                                    response.status().as_u16(),
                                    response.status().canonical_reason(),
                                )
                                .await;
                            return response;
                        }
                        Ok(ResponseStatusCode::WasBlocked) => {
                            // Sleep to push back on the client
                            let sleep_lower_bound =
                                BLOCKED_SLEEP_LOWER_BOUND_FLAG.get_from(feature_flags);
                            let sleep_upper_bound =
                                BLOCKED_SLEEP_UPPER_BOUND_FLAG.get_from(feature_flags);

                            if sleep_upper_bound > 0.0 {
                                let mut rng = rand::thread_rng();
                                let sleep_time =
                                    rng.gen_range(sleep_lower_bound..=sleep_upper_bound);
                                let sleep_duration = Duration::from_secs_f64(sleep_time);

                                if !sleep_duration.is_zero() {
                                    tracing::info!(
                                        "Sleeping for {:.3}s before responding to blocked request",
                                        sleep_time
                                    );
                                    tokio::time::sleep(sleep_duration).await;
                                }
                            }

                            // Continue with the response after sleeping
                            let receiver_stream = ReceiverStream::new(receiver);
                            tokio_stream::once(Ok(first_response)).chain(receiver_stream)
                        }
                        Ok(ResponseStatusCode::Ok) => {
                            let receiver_stream = ReceiverStream::new(receiver);
                            tokio_stream::once(Ok(first_response)).chain(receiver_stream)
                        }
                        Err(_) => {
                            let response = HttpResponse::InternalServerError().finish();
                            request_insight_publisher
                                .record_http_response(
                                    &request_context,
                                    &tenant_info,
                                    response.status().as_u16(),
                                    response.status().canonical_reason(),
                                )
                                .await;
                            return response;
                        }
                    }
                }
                Some(Err(e)) => {
                    let response = status_to_response(&e);
                    // No need to log cancellations.
                    // EOS case with stream_mux is Err(Status(Ok))
                    if e.code() == tonic::Code::Ok {
                        // Log EOS case
                        tracing::info!("EOS received for stream: {:?}", e);
                        // Return empty stream
                        return response_builder.streaming(tokio_stream::empty::<
                            Result<web::Bytes, actix_web::error::Error>,
                        >());
                    }
                    if e.code() != tonic::Code::Cancelled {
                        tracing::error!("Error getting first response {:?}", e);
                    }
                    request_insight_publisher
                        .record_http_response(
                            &request_context,
                            &tenant_info,
                            response.status().as_u16(),
                            response.status().canonical_reason(),
                        )
                        .await;
                    return response;
                }
                None => {
                    // Empty stream is an expected case for some requests
                    return response_builder.streaming(tokio_stream::empty::<
                        Result<web::Bytes, actix_web::error::Error>,
                    >());
                }
            }
        }
        Err(e) => {
            let response = status_to_response(&e);
            // No need to log cancellations.
            if e.code() != tonic::Code::Cancelled {
                tracing::error!("Error getting receiver {:?}", e);
            }
            request_insight_publisher
                .record_http_response(
                    &request_context,
                    &tenant_info,
                    response.status().as_u16(),
                    response.status().canonical_reason(),
                )
                .await;
            return response;
        }
    };
    let stream_with_end_signal = receiver
        // Add an artifical EOS signal to the end of the stream so we can do cleanup handling
        // in the callback below (StreamExt callbacks don't normally get called on EOS)
        .chain(tokio_stream::once(Err(tonic::Status::ok("EOS"))))
        // Convert model results to bytes for the HTTP response
        .map(|model_result| {
            match model_result {
                Ok(model_response) => {
                    let front_response = FrontResp::from(model_response);
                    match serde_json::to_string(&front_response) {
                        Ok(front_response_string) => {
                            let front_response_string = front_response_string + "\n"; // Newline Delimited JSON
                            let front_response_bytes =
                                web::Bytes::from(front_response_string.into_bytes());
                            Ok(front_response_bytes)
                        }
                        Err(e) => Err(tonic::Status::internal(format!(
                            "Error serializing front_response: {:?}",
                            e
                        ))),
                    }
                }
                Err(e) => Err(e),
            }
        })
        // Handle expected "error" signals (EOS and cancel) that are clean exits from the client's
        // perspective, and record RI information about the response.
        .then(move |result_bytes| {
            let status: Option<tonic::Status> = result_bytes.clone().err();
            let http_result = match result_bytes {
                Ok(response_bytes) => Ok(response_bytes),
                Err(e) if e.code() == tonic::Code::Ok || e.code() == tonic::Code::Cancelled => {
                    Ok(web::Bytes::new())
                }
                Err(e) => Err(e),
            };

            let endpoint = endpoint.clone();
            let request_context = request_context.clone();
            let tenant_info = tenant_info.clone();
            let request_insight_publisher = request_insight_publisher.clone();

            async move {
                if let Some(e) = status {
                    // Hack: reuse our logic to translate gRPC status to HTTP status
                    let http_code = status_to_response(&e).status().as_u16();
                    STREAM_LATENCY_COLLECTOR
                        .with_label_values(&[
                            &endpoint,
                            &http_code.to_string(),
                            &request_context.request_source().to_string(),
                            &tenant_info.metrics_tenant_name().to_string(),
                        ])
                        .observe(start_time.elapsed().as_secs_f64());
                    tracing::info!("Finishing stream with status: {:?}", e);

                    request_insight_publisher
                        .record_http_response(
                            &request_context,
                            &tenant_info,
                            http_code,
                            Some(e.message()),
                        )
                        .await;
                }
                http_result
            }
        });

    // Conditionally wrap with heartbeat stream
    let final_stream: Pin<Box<dyn Stream<Item = Result<web::Bytes, tonic::Status>>>> =
        if enable_heartbeat {
            Box::pin(HeartbeatStream::new(
                Box::pin(stream_with_end_signal),
                web::Bytes::from(" "),
            ))
        } else {
            Box::pin(stream_with_end_signal)
        };

    let mut stream_has_err: Box<bool> = Box::new(false);
    response_builder.streaming(final_stream.map_while(move |res| {
        if *stream_has_err {
            return None;
        }
        let stream_elem = match res {
            Ok(bytes) => Ok::<_, actix_web::error::Error>(bytes),
            Err(_) => {
                *stream_has_err = true;
                Err(actix_web::error::InternalError::from_response(
                    "Unidentified internal error",
                    HttpResponse::InternalServerError().finish(),
                )
                .into())
            }
        };
        Some(stream_elem)
    }))
}

pub async fn get_model<MR: ModelRegistry>(
    request_model_name: Option<&String>,
    model_registry: &MR,
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
    flag: &feature_flags::StringFlag,
    model_type: ModelType,
) -> Result<(Client, ModelInstanceConfig), tonic::Status> {
    let (model_name, from_feature_flag) = match request_model_name {
        Some(model) if !model.is_empty() => (model.clone(), false),
        _ => (flag.get_from(feature_flags), true),
    };

    if !model_name.is_empty() {
        match model_registry.get_model(&model_name).await {
            Ok(model) => Ok(model),
            Err(e) => {
                tracing::warn!("Failed to get flag-based model: {}", model_name);
                if e.code() == tonic::Code::NotFound && from_feature_flag {
                    // Translate to a 500 because this wasn't a problem with the request.
                    Err(tonic::Status::internal(format!(
                        "Feature flag-based model not found: {}",
                        e.message()
                    )))
                } else {
                    Err(e)
                }
            }
        }
    } else {
        model_registry.get_default_model(model_type).await
    }
}

const RETRYABLE_ERROR_CODES: [tonic::Code; 2] = [tonic::Code::Unavailable, tonic::Code::Unknown];

#[derive(Clone, Debug)]
pub struct RetryPolicy {
    pub min_retry_backoff_milliseconds: i64,
    pub max_retry_backoff_milliseconds: i64,
    pub max_retry_count: i32,
    pub count: i32,
}

impl RetryPolicy {
    pub fn new(
        min_retry_backoff_milliseconds: i64,
        max_retry_backoff_milliseconds: i64,
        max_retry_count: i32,
    ) -> Self {
        RetryPolicy {
            min_retry_backoff_milliseconds,
            max_retry_backoff_milliseconds,
            max_retry_count,
            count: 0,
        }
    }

    /// Returns the backoff time if the status is retryable, otherwise returns None.
    pub fn should_retry(&mut self, status: &tonic::Status) -> Option<Duration> {
        if self.count >= self.max_retry_count {
            return None;
        }
        self.count += 1;
        if RETRYABLE_ERROR_CODES.contains(&status.code()) {
            // we assume that we do not retry more than few times, so exponential backoff is not needed
            // but we want to avoid retrying too fast and add some jittering to avoid hearding.
            let backoff_milliseconds = rand::thread_rng().gen_range(
                self.min_retry_backoff_milliseconds..self.max_retry_backoff_milliseconds,
            );
            Some(Duration::from_millis(backoff_milliseconds as u64))
        } else {
            None
        }
    }
}

pub const NONE_RETRY_POLICY: RetryPolicy = RetryPolicy {
    min_retry_backoff_milliseconds: 0,
    max_retry_backoff_milliseconds: 0,
    max_retry_count: 0,
    count: 0,
};

/// Executes an asynchronous operation with a retry mechanism.
///
/// This function attempts to execute the provided asynchronous operation, retrying on certain errors
/// based on the specified retry policy.
///
/// # Arguments
///
/// * `retry_policy` - A `RetryPolicy` struct that defines the retry behavior.
/// * `f` - A closure that returns a `Future` representing the operation to be executed.
/// * `on_retry` - A closure that is called when a retry occurs, receiving the error status.
///
/// # Returns
///
/// Returns a `Result<T, tonic::Status>` where `T` is the successful result type of the operation.
///
/// # Type Parameters
///
/// * `T` - The type of the successful result.
/// * `F` - The type of the closure that returns the `Future`.
/// * `Fut` - The type of the `Future` returned by `F`.
/// * `RetryFn` - The type of the retry callback closure.
///
/// # Examples
///
/// ```
/// let result = retry(
///     RetryPolicy::new(100, 1000, 3),
///     || async { /* Your async operation here */ },
///     |error| println!("Retrying due to error: {:?}", error)
/// ).await;
/// ```
pub async fn retry<T, F, Fut, RetryFn>(
    mut retry_policy: RetryPolicy,
    f: F,
    on_retry: RetryFn,
) -> Result<T, tonic::Status>
where
    F: Fn() -> Fut,
    Fut: Future<Output = Result<T, tonic::Status>>,
    RetryFn: Fn(&tonic::Status),
{
    loop {
        let r = f().await;
        if let Err(e) = r.as_ref() {
            if let Some(retry_backoff) = retry_policy.should_retry(e) {
                tokio::time::sleep(retry_backoff).await;
                on_retry(e);
                continue;
            }
        }
        return r;
    }
}

pub struct SslConfig {
    ssl_config: ClientConfig,
}

impl SslConfig {
    pub fn new() -> Self {
        let mut root_store = RootCertStore::empty();
        for cert in load_native_certs().expect("Could not load platform certs") {
            root_store.add(cert).unwrap();
        }
        let ssl_config = ClientConfig::builder()
            .with_root_certificates(root_store)
            .with_no_client_auth();

        SslConfig { ssl_config }
    }

    pub fn get_config(&self) -> ClientConfig {
        self.ssl_config.clone()
    }
}

#[cfg(test)]
pub mod tests {
    use super::*;
    use crate::agents_client::MockAgentsClient;
    use crate::api_auth::{ApiAuth, User};
    use crate::augment::model_instance_config::{
        model_instance_config::ModelConfig, ChatModelConfig, EditModelConfig, InferenceModelConfig,
        Language, ModelInstanceConfig, ModelType, NextEditGenModelConfig,
    };
    use crate::config::Config;
    use crate::model_registry::tests::{create_registry, FakeClientFactory};
    use crate::model_registry::DynamicModelRegistry;
    use crate::request_insight_util::tests::FakeRequestInsightPublisher;
    use actix_web::{
        body::{self, MessageBody},
        http::header,
        rt::pin,
        web::Bytes,
    };
    use auth_central_client::MockAuthCentralClient;
    use auth_entities_proto::auth_entities::{user_id::UserIdType, UserId};
    use auth_query_client::auth_query::{
        get_token_info_response::Subscription, ActiveSubscription,
    };
    use content_manager_client::MockContentManagerClient;
    use futures::future;
    use memstore_client::MockMemstoreClient;
    use remote_agent_actions_client::MockRemoteAgentActionsClient;
    use remote_agents_client::MockRemoteAgentsClient;
    use serde::Serialize;
    use share_client::MockShareClient;

    use std::collections::HashMap;
    use tokio::sync::mpsc;

    const FAKE_USER_ID: &str = "1234567890";

    pub const DEFAULT_PREFIX_CHAR_COUNT: u32 = 200;
    pub const DEFAULT_SUFFIX_CHAR_COUNT: u32 = 100;

    pub fn create_model_instance_config(
        name: impl AsRef<str>,
        model_type: ModelType,
        model_priority: i64,
    ) -> ModelInstanceConfig {
        ModelInstanceConfig {
            name: name.as_ref().to_string(),
            model_type: model_type.into(),
            model_config: match model_type {
                ModelType::Chat => Some(ModelConfig::Chat(ChatModelConfig {
                    suggested_prefix_char_count: DEFAULT_PREFIX_CHAR_COUNT,
                    suggested_suffix_char_count: DEFAULT_SUFFIX_CHAR_COUNT,
                    model_priority,
                    chat_endpoint: "".to_string(),
                })),
                ModelType::Edit => Some(ModelConfig::Edit(EditModelConfig {
                    suggested_prefix_char_count: DEFAULT_PREFIX_CHAR_COUNT,
                    suggested_suffix_char_count: DEFAULT_SUFFIX_CHAR_COUNT,
                    model_priority,
                    edit_endpoint: "".to_string(),
                })),
                ModelType::Inference => Some(ModelConfig::Inference(InferenceModelConfig {
                    suggested_prefix_char_count: DEFAULT_PREFIX_CHAR_COUNT,
                    suggested_suffix_char_count: DEFAULT_SUFFIX_CHAR_COUNT,
                    model_priority,
                    completion_endpoint: "".to_string(),
                    languages: vec![Language {
                        name: "C".to_string(),
                        vscode_name: "c".to_string(),
                        extensions: vec![".c".to_string()],
                    }],
                    ..Default::default()
                })),
                ModelType::NextEdit => Some(ModelConfig::NextEdit(NextEditGenModelConfig {
                    suggested_prefix_char_count: DEFAULT_PREFIX_CHAR_COUNT,
                    suggested_suffix_char_count: DEFAULT_SUFFIX_CHAR_COUNT,
                    model_priority,
                    next_edit_endpoint: "".to_string(),
                })),
            },
            handler_names: vec![],
        }
    }

    // Helpers to share across handlers_* files
    pub fn new_api_auth() -> Arc<ApiAuth> {
        Arc::new(ApiAuth::new_for_test(HashMap::from([(
            "token1".to_string(),
            FAKE_USER_ID.to_string(),
        )])))
    }

    pub fn new_root_span() -> tracing_actix_web::RootSpan {
        let span = tracing::info_span!("call");
        tracing_actix_web::RootSpan::new(span)
    }

    /// AppStateBuilder provides a builder pattern for creating an AppState for testing.
    /// This class was added to reduce a large amount of test initialization boilerplate.
    /// If you're test needs a reference to one of the fields of the built Handler struct,
    /// you should construct the field yourself then pass it into the app state builder
    /// with an appropriate "with_*" method.
    pub struct AppStateBuilder {
        fake_client_factory: Arc<FakeClientFactory>,
        config: Option<Config>,
        request_insight_publisher: Option<Arc<FakeRequestInsightPublisher>>,
        content_manager_client: Option<MockContentManagerClient>,
        feature_flags: Option<feature_flags::FeatureFlagsServiceHandle>,
    }

    impl AppStateBuilder {
        pub fn new(fake_client_factory: Arc<FakeClientFactory>) -> Self {
            Self {
                fake_client_factory,
                config: None,
                request_insight_publisher: None,
                content_manager_client: None,
                feature_flags: None,
            }
        }

        pub fn with_config(mut self, config: Config) -> Self {
            self.config = Some(config);
            self
        }

        pub fn with_request_insight_publisher(
            mut self,
            publisher: Arc<FakeRequestInsightPublisher>,
        ) -> Self {
            self.request_insight_publisher = Some(publisher);
            self
        }

        pub fn with_content_manager_client(mut self, client: MockContentManagerClient) -> Self {
            self.content_manager_client = Some(client);
            self
        }

        pub fn with_feature_flags(
            mut self,
            feature_flags: feature_flags::FeatureFlagsServiceHandle,
        ) -> Self {
            self.feature_flags = Some(feature_flags);
            self
        }

        // Silence clippy "redundant_closure" as it incorrectly claims closures are redundant, but
        // without them the functions don't satisfy the requisite type to pass to unwrap_or_else.
        #[allow(clippy::redundant_closure)]
        pub fn build(self) -> web::Data<Handler<DynamicModelRegistry, MockContentManagerClient>> {
            let registry = Arc::new(create_registry(self.fake_client_factory.clone()));
            // Extra curly braces around closure function is annoying, but necessary to silence clippy "redundant_closure"
            // warning (even though the closure is *not* redundant as it lets the function type check properly).
            let config = self.config.unwrap_or_else(|| Config::default());
            let api_auth = new_api_auth();
            let request_insight_publisher = self
                .request_insight_publisher
                .unwrap_or_else(|| Arc::new(FakeRequestInsightPublisher::new()));
            let content_manager_client = self
                .content_manager_client
                .unwrap_or_else(|| MockContentManagerClient::new());
            let external_source_clients = ExternalSourceClients::new_for_test();
            let feature_flags = self
                .feature_flags
                .unwrap_or_else(|| feature_flags::setup_local());
            let share_client = Arc::new(share_client::MockShareClient::new());
            let agents_client = Arc::new(MockAgentsClient::new());
            let remote_agents_client = Arc::new(MockRemoteAgentsClient::new());
            web::Data::new(Handler::new(
                config.clone(),
                api_auth,
                registry,
                request_insight_publisher,
                content_manager_client,
                external_source_clients,
                feature_flags.clone(),
                share_client,
                agents_client,
                Arc::new(MockAuthCentralClient::new()),
                remote_agents_client,
                Arc::new(MockRemoteAgentActionsClient::new()),
                Arc::new(github_processor_client::MockGithubProcessorClient::new()),
                Arc::new(MockMemstoreClient::new()),
                Arc::new(notification_client::NotificationClientImpl::new(
                    "localhost:8080",
                    None,
                    Duration::from_secs(10),
                )),
                Arc::new(ThrottleCache::new(
                    config.throttle_cache_size,
                    config.throttle_cache_ttl_seconds,
                    feature_flags,
                )),
            ))
        }
    }

    pub fn setup_app_state(
        fake_client_factory: Arc<FakeClientFactory>,
    ) -> web::Data<Handler<DynamicModelRegistry, MockContentManagerClient>> {
        AppStateBuilder::new(fake_client_factory).build()
    }

    /// Set up a request with the extensions that are usually set up by
    /// middleware
    pub fn setup_req() -> HttpRequest {
        let request_context = RequestContext::new_for_test();
        let req = actix_web::test::TestRequest::default()
            .insert_header((header::AUTHORIZATION, "Bearer token1"))
            .insert_header((
                request_context::REQUEST_SESSION_ID_HEADER_NAME,
                request_context.request_session_id().to_string(),
            ))
            .to_http_request();
        req.extensions_mut().insert(request_context.request_id());
        req.extensions_mut().insert(request_context);
        req.extensions_mut().insert(User {
            user_id: "34567".to_string(),
            opaque_user_id: UserId {
                user_id_type: UserIdType::ApiToken.into(),
                user_id: "34567".to_string(),
            },
            user_email: None,
            subscription: Some(Subscription::ActiveSubscription(ActiveSubscription {
                end_date: None,
                usage_balance_depleted: false,
            })),
            suspensions: vec![],
        });
        req.extensions_mut().insert(TenantInfo {
            tenant_id: Some("12345".to_string().into()),
            tenant_name: "dev".to_string(),
            shard_namespace: "dev".to_string(),
            cloud: "dev-cloud".to_string(),
            scopes: vec![],
            user_id: Some("12345".to_string().into()),
            opaque_user_id: Some(UserId {
                user_id_type: UserIdType::ApiToken.into(),
                user_id: "12345".to_string(),
            }),
            user_email: Some("<EMAIL>".to_string().into()),
            service_name: None,
            genie_id: None,
        });
        req.extensions_mut().insert(Instant::now());
        req
    }

    #[derive(Clone)]
    struct FakeModelResp {
        foo: String,
    }

    #[derive(Serialize)]
    struct FakeFrontResp {
        foo: String,
    }

    impl From<FakeModelResp> for FakeFrontResp {
        fn from(model_resp: FakeModelResp) -> Self {
            Self {
                foo: model_resp.foo,
            }
        }
    }

    impl TryFrom<FakeModelResp> for ResponseStatusCode {
        type Error = ();
        fn try_from(_resp: FakeModelResp) -> Result<Self, Self::Error> {
            Ok(ResponseStatusCode::Ok)
        }
    }

    #[actix_web::test]
    async fn test_streaming_http_response_from_receiver_ok() {
        // Create a channel to simulate streaming data
        let (tx, rx) = mpsc::channel(32);

        // Send some data through the channel
        tokio::spawn(async move {
            tx.send(Ok(FakeModelResp {
                foo: "bar 1".to_string(),
            }))
            .await
            .unwrap();
            tx.send(Ok(FakeModelResp {
                foo: "bar 2".to_string(),
            }))
            .await
            .unwrap();
        });

        // Call the function under test
        let request_context = RequestContext::new_for_test();
        let tenant_info = TenantInfo::new_for_test();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let feature_flags = feature_flags::setup_local();
        let response = streaming_http_response_from_receiver::<FakeModelResp, FakeFrontResp>(
            "test",
            Ok(rx),
            request_context,
            tenant_info,
            request_insight_publisher,
            &feature_flags,
            false, // Disable heartbeat for test
        )
        .await;

        // Assert the response status
        assert_eq!(response.status(), actix_web::http::StatusCode::OK);

        // Collect the streamed body
        let box_body: body::BoxBody = response.into_body();
        pin!(box_body);

        // first chunk
        let bytes: web::Bytes = future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .unwrap()
            .unwrap();
        assert_eq!(bytes, Bytes::from("{\"foo\":\"bar 1\"}\n"));
        // second chunk
        let bytes: web::Bytes = future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .unwrap()
            .unwrap();
        assert_eq!(bytes, Bytes::from("{\"foo\":\"bar 2\"}\n"));
        // EOS
        assert!(future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .is_none());
    }

    #[actix_web::test]
    async fn test_streaming_http_response_from_receiver_err() {
        // Create a channel to simulate streaming data
        let (tx, rx) = mpsc::channel(32);

        // Send some data through the channel
        tokio::spawn(async move {
            tx.send(Ok(FakeModelResp {
                foo: "bar 1".to_string(),
            }))
            .await
            .unwrap();
            tx.send(Err(tonic::Status::internal("test error")))
                .await
                .unwrap();
            tx.send(Ok(FakeModelResp {
                foo: "bar 2".to_string(),
            }))
            .await
            .unwrap();
        });

        // Call the function under test
        let request_context = RequestContext::new_for_test();
        let tenant_info = TenantInfo::new_for_test();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let feature_flags = feature_flags::setup_local();
        let response = streaming_http_response_from_receiver::<FakeModelResp, FakeFrontResp>(
            "test",
            Ok(rx),
            request_context,
            tenant_info,
            request_insight_publisher,
            &feature_flags,
            false, // Disable heartbeat for test
        )
        .await;

        // Assert the response status
        assert_eq!(response.status(), actix_web::http::StatusCode::OK);

        // Collect the streamed body
        let box_body: body::BoxBody = response.into_body();
        pin!(box_body);

        // first chunk
        let bytes: web::Bytes = future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .unwrap()
            .unwrap();
        assert_eq!(bytes, Bytes::from("{\"foo\":\"bar 1\"}\n"));
        // error
        assert!(future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .unwrap()
            .is_err());
        // EOS
        assert!(future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .is_none());
    }

    #[actix_web::test]
    async fn test_streaming_http_response_from_receiver_eos() {
        // Create a channel to simulate streaming data
        let (tx, rx) = mpsc::channel(32);

        // Send an EOS error (Status with Ok code) as the first response
        tokio::spawn(async move {
            tx.send(Err(tonic::Status::new(tonic::Code::Ok, "EOS")))
                .await
                .unwrap();
        });

        // Call the function under test
        let request_context = RequestContext::new_for_test();
        let tenant_info = TenantInfo::new_for_test();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let feature_flags = feature_flags::setup_local();
        let response = streaming_http_response_from_receiver::<FakeModelResp, FakeFrontResp>(
            "test",
            Ok(rx),
            request_context,
            tenant_info,
            request_insight_publisher,
            &feature_flags,
            false, // Disable heartbeat for test
        )
        .await;

        // Assert the response status - should be OK for EOS case
        assert_eq!(response.status(), actix_web::http::StatusCode::OK);

        // Collect the streamed body - should be empty for EOS case
        let box_body: body::BoxBody = response.into_body();
        pin!(box_body);

        // Should immediately return None (empty stream)
        assert!(future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .is_none());
    }

    #[tokio::test(start_paused = true)]
    async fn test_heartbeat_stream() {
        use futures::StreamExt as FuturesStreamExt;
        use tokio_stream::wrappers::ReceiverStream;

        let (tx, rx) = tokio::sync::mpsc::channel::<Result<web::Bytes, tonic::Status>>(10);
        let slow_stream = ReceiverStream::new(rx);

        let heartbeat_bytes = web::Bytes::from(" ");
        let heartbeat_stream = HeartbeatStream::new(Box::pin(slow_stream), heartbeat_bytes.clone());
        let mut heartbeat_stream = Box::pin(heartbeat_stream);

        // Spawn a task to collect items from the heartbeat stream
        let collector = tokio::spawn(async move {
            let mut collected_items = Vec::new();
            while let Some(item) = FuturesStreamExt::next(&mut heartbeat_stream).await {
                collected_items.push(item);
            }
            collected_items
        });

        let mut expected_items: Vec<Result<web::Bytes, tonic::Status>> = Vec::new();
        tokio::time::advance(Duration::from_secs(1)).await;
        tokio::task::yield_now().await;
        expected_items.push(Ok(heartbeat_bytes.clone()));

        tokio::time::advance(Duration::from_millis(900)).await;
        tokio::task::yield_now().await;
        tx.send(Ok(web::Bytes::from("data1"))).await.unwrap();
        tokio::task::yield_now().await;
        tokio::time::advance(Duration::from_millis(900)).await;
        tokio::task::yield_now().await;
        tx.send(Ok(web::Bytes::from("data2"))).await.unwrap();
        tokio::task::yield_now().await;
        expected_items.push(Ok(web::Bytes::from("data1")));
        expected_items.push(Ok(web::Bytes::from("data2")));

        for _ in 0..5 {
            tokio::time::advance(Duration::from_millis(900)).await;
            tokio::task::yield_now().await;
        }
        tx.send(Ok(web::Bytes::from("data3"))).await.unwrap();
        tokio::task::yield_now().await;
        for _ in 0..4 {
            expected_items.push(Ok(heartbeat_bytes.clone()));
        }
        expected_items.push(Ok(web::Bytes::from("data3")));

        tokio::time::advance(Duration::from_millis(1100)).await;
        tokio::task::yield_now().await;
        expected_items.push(Ok(heartbeat_bytes.clone())); // Expect third heartbeat

        // Close the channel to end the stream
        drop(tx);

        // Wait for the collector to finish and get the collected items
        let collected_items = collector.await.unwrap();

        // Verify the exact sequence matches our expected items
        assert_eq!(
            collected_items.len(),
            expected_items.len(),
            "Expected {} items, got {}",
            expected_items.len(),
            collected_items.len()
        );

        // Compare each item against the expected sequence
        for (i, (actual, expected)) in collected_items
            .iter()
            .zip(expected_items.iter())
            .enumerate()
        {
            assert!(
                matches!((actual, expected), (Ok(actual_bytes), Ok(expected_bytes)) if actual_bytes == expected_bytes),
                "Item {} mismatch: expected {:?}, got {:?}",
                i,
                expected,
                actual
            );
        }
    }

    #[actix_web::test]
    async fn test_should_throttle() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let handler = Handler::new(
            Config::default(),
            new_api_auth(),
            Arc::new(create_registry(fake_client_factory.clone())),
            Arc::new(FakeRequestInsightPublisher::new()),
            MockContentManagerClient::new(),
            ExternalSourceClients::new_for_test(),
            feature_flags::setup_local(),
            Arc::new(MockShareClient::new()),
            Arc::new(MockAgentsClient::new()),
            Arc::new(MockAuthCentralClient::new()),
            Arc::new(MockRemoteAgentsClient::new()),
            Arc::new(remote_agent_actions_client::MockRemoteAgentActionsClient::new()),
            Arc::new(github_processor_client::MockGithubProcessorClient::new()),
            Arc::new(MockMemstoreClient::new()),
            Arc::new(notification_client::NotificationClientImpl::new(
                "localhost:8080",
                None,
                Duration::from_secs(10),
            )),
            Arc::new(ThrottleCache::new(
                1000,
                3600000,
                feature_flags::setup_local(),
            )),
        );

        let user_id = "test_user";
        let endpoint = "test_endpoint";
        let fill_rate = 0.000000000001;
        let capacity = 1.0;

        // First request should not be throttled
        assert!(handler
            .throttle_cache
            .should_throttle(user_id, endpoint, fill_rate, capacity, 1.0)
            .await
            .is_ok());

        // Immediate second request should be throttled
        assert!(handler
            .throttle_cache
            .should_throttle(user_id, endpoint, fill_rate, capacity, 1.0)
            .await
            .is_err());

        // Different user should not be throttled even immediately
        let different_user = "different_user";
        assert!(handler
            .throttle_cache
            .should_throttle(different_user, endpoint, fill_rate, capacity, 1.0)
            .await
            .is_ok());

        // Different endpoint should not be throttled even immediately
        assert!(handler
            .throttle_cache
            .should_throttle(user_id, "different_endpoint", fill_rate, capacity, 1.0)
            .await
            .is_ok());

        // now, increase the capacity and the request should not be throttled
        let high_cap_user = "high_cap_user";
        let high_cap_capacity = 10.0;
        for _ in 0..10 {
            assert!(handler
                .throttle_cache
                .should_throttle(high_cap_user, endpoint, fill_rate, high_cap_capacity, 1.0)
                .await
                .is_ok());
        }
        // now it should be throttled
        assert!(handler
            .throttle_cache
            .should_throttle(high_cap_user, endpoint, fill_rate, capacity, 1.0)
            .await
            .is_err());

        // finally, high fill rate should not be throttled even with a low capacity
        let high_fill_rate_user = "high_fill_rate_user";
        let high_fill_rate = 1000000000.0;
        for _ in 0..10 {
            // hate to sleep in tests but this is the only way to test this...don't see a way to
            // mock time in Rust for built ins like tokenbucket
            tokio::time::sleep(Duration::from_millis(10)).await;
            assert!(handler
                .throttle_cache
                .should_throttle(high_fill_rate_user, endpoint, high_fill_rate, capacity, 1.0)
                .await
                .is_ok());
        }
    }

    // Test that if the cache is empty (could be due to a variety of factors)
    // we will continue to not throttle entries, since they're being
    // added for the first time to the cache.
    #[actix_web::test]
    async fn test_should_throttle_empty_cache() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let handler = Handler::new(
            Config::default(),
            new_api_auth(),
            Arc::new(create_registry(fake_client_factory.clone())),
            Arc::new(FakeRequestInsightPublisher::new()),
            MockContentManagerClient::new(),
            ExternalSourceClients::new_for_test(),
            feature_flags::setup_local(),
            Arc::new(MockShareClient::new()),
            Arc::new(MockAgentsClient::new()),
            Arc::new(MockAuthCentralClient::new()),
            Arc::new(MockRemoteAgentsClient::new()),
            Arc::new(remote_agent_actions_client::MockRemoteAgentActionsClient::new()),
            Arc::new(github_processor_client::MockGithubProcessorClient::new()),
            Arc::new(MockMemstoreClient::new()),
            Arc::new(notification_client::NotificationClientImpl::new(
                "localhost:8080",
                None,
                Duration::from_secs(10),
            )),
            Arc::new(ThrottleCache::new(0, 0, feature_flags::setup_local())),
        );

        let user_id = "test_user";
        let endpoint = "test_endpoint";
        let fill_rate = 0.000000000001;
        let capacity = 1.0;

        // cache eviction is such that we'll never be throttled (no retention)
        // Run it a few times and ensure we're not throttled, despite the low fill rate
        // and capacity. There's nothing special about 10, but we can't meainginfully
        // run it infinite times.
        for _ in 0..10 {
            assert!(handler
                .throttle_cache
                .should_throttle(user_id, endpoint, fill_rate, capacity, 1.0)
                .await
                .is_ok());
        }
    }

    #[actix_web::test]
    async fn test_should_throttle_with_token_size() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let handler = Handler::new(
            Config::default(),
            new_api_auth(),
            Arc::new(create_registry(fake_client_factory.clone())),
            Arc::new(FakeRequestInsightPublisher::new()),
            MockContentManagerClient::new(),
            ExternalSourceClients::new_for_test(),
            feature_flags::setup_local(),
            Arc::new(MockShareClient::new()),
            Arc::new(MockAgentsClient::new()),
            Arc::new(MockAuthCentralClient::new()),
            Arc::new(MockRemoteAgentsClient::new()),
            Arc::new(remote_agent_actions_client::MockRemoteAgentActionsClient::new()),
            Arc::new(github_processor_client::MockGithubProcessorClient::new()),
            Arc::new(MockMemstoreClient::new()),
            Arc::new(notification_client::NotificationClientImpl::new(
                "localhost:8080",
                None,
                Duration::from_secs(10),
            )),
            Arc::new(ThrottleCache::new(
                1000,
                3600000,
                feature_flags::setup_local(),
            )),
        );

        let user_id = "test_user";
        let endpoint = "test_endpoint";
        let fill_rate = 0.000000000001;
        let capacity = 3.0;

        // Too large req will be throttled; smaller one is ok
        assert!(handler
            .throttle_cache
            .should_throttle(user_id, endpoint, fill_rate, capacity, 4.0)
            .await
            .is_err());
        assert!(handler
            .throttle_cache
            .should_throttle(user_id, endpoint, fill_rate, capacity, 3.0)
            .await
            .is_ok());
        // Now no more space
        assert!(handler
            .throttle_cache
            .should_throttle(user_id, endpoint, fill_rate, capacity, 1.0)
            .await
            .is_err());

        // test slowly reaching the throttled point
        let new_user = "new_test_user"; // different user
        let new_capacity = 5.0;
        let new_fill_rate = 0.000000000001;
        for _ in 0..5 {
            assert!(handler
                .throttle_cache
                .should_throttle(new_user, endpoint, new_fill_rate, new_capacity, 1.0)
                .await
                .is_ok());
        }
        // Fourth request should be throttled
        assert!(handler
            .throttle_cache
            .should_throttle(new_user, endpoint, new_fill_rate, new_capacity, 1.0)
            .await
            .is_err());
    }

    #[test]
    fn test_error_details_serialization() {
        // Test serialization of ResponseError with error details
        let error = ResponseError::new(
            "Invalid request",
            Some(error_details_proto::ErrorDetails {
                code: 1, // INVALID_TOOL_DEFINITION
                message: "Invalid tool definition".to_string(),
                detail: "Tool name contains invalid characters".to_string(),
                help_uri: Some(
                    "https://docs.augmentcode.com/errors/invalid-tool-definition".to_string(),
                ),
            }),
        );

        let json = serde_json::to_string(&error).unwrap();

        // Verify the JSON contains all the expected fields
        let parsed: serde_json::Value = serde_json::from_str(&json).unwrap();
        assert_eq!(parsed["error"], "Invalid request");

        let details = &parsed["error_details"];
        assert_eq!(details["code"], 1);
        assert_eq!(details["message"], "Invalid tool definition");
        assert_eq!(details["detail"], "Tool name contains invalid characters"); // Note: field name is "detail" in proto
        assert_eq!(
            details["help_uri"],
            "https://docs.augmentcode.com/errors/invalid-tool-definition"
        ); // Note: field name is "help_uri" in proto

        // Test serialization of ResponseError without error details
        let simple_error = ResponseError::new("Simple error message", None);
        let simple_json = serde_json::to_string(&simple_error).unwrap();

        // Verify the JSON only contains the error field
        let simple_parsed: serde_json::Value = serde_json::from_str(&simple_json).unwrap();
        assert_eq!(simple_parsed["error"], "Simple error message");
        assert!(simple_parsed.get("error_details").is_none());
    }

    #[test]
    fn test_extract_error_details_from_metadata() {
        use std::str::FromStr;

        // Create a mock gRPC status with error details in metadata
        let mut metadata = tonic::metadata::MetadataMap::new();
        let error_details_json = r#"{"code":1,"message":"Invalid tool definition","detail":"Tool name contains invalid characters","help_uri":"https://docs.augmentcode.com/errors/invalid-tool-definition"}"#;
        metadata.insert(
            "error-details",
            tonic::metadata::MetadataValue::<tonic::metadata::Ascii>::from_str(error_details_json)
                .unwrap(),
        );

        let status =
            tonic::Status::with_metadata(tonic::Code::InvalidArgument, "Test error", metadata);

        // Extract error details
        let extracted = extract_error_details_from_metadata(&status);

        // Verify the details were extracted correctly
        assert!(extracted.is_some());
        let details = extracted.unwrap();
        assert_eq!(details.code, 1);
        assert_eq!(details.message, "Invalid tool definition");
        assert_eq!(details.detail, "Tool name contains invalid characters");
        assert_eq!(
            details.help_uri,
            Some("https://docs.augmentcode.com/errors/invalid-tool-definition".to_string())
        );
    }

    #[test]
    fn test_extract_error_details_without_help_uri() {
        use std::str::FromStr;

        let mut metadata = tonic::metadata::MetadataMap::new();
        let error_details_json = r#"{"code":7,"message":"Prompt length exceeded","detail":"The input length exceeds the model's context limit"}"#;
        metadata.insert(
            "error-details",
            tonic::metadata::MetadataValue::<tonic::metadata::Ascii>::from_str(error_details_json)
                .unwrap(),
        );

        let status =
            tonic::Status::with_metadata(tonic::Code::InvalidArgument, "Test error", metadata);

        let extracted = extract_error_details_from_metadata(&status);

        assert!(extracted.is_some());
        let details = extracted.unwrap();
        assert_eq!(details.code, 7);
        assert_eq!(details.message, "Prompt length exceeded");
        assert_eq!(
            details.detail,
            "The input length exceeds the model's context limit"
        );
        assert_eq!(details.help_uri, None);
    }

    #[test]
    fn test_extract_error_details_no_metadata() {
        let status = tonic::Status::new(tonic::Code::InvalidArgument, "Test error");
        let extracted = extract_error_details_from_metadata(&status);
        assert!(extracted.is_none());
    }

    #[test]
    fn test_extract_error_details_invalid_json() {
        use std::str::FromStr;

        let mut metadata = tonic::metadata::MetadataMap::new();
        metadata.insert(
            "error-details",
            tonic::metadata::MetadataValue::<tonic::metadata::Ascii>::from_str("invalid json")
                .unwrap(),
        );

        let status =
            tonic::Status::with_metadata(tonic::Code::InvalidArgument, "Test error", metadata);

        let extracted = extract_error_details_from_metadata(&status);
        assert!(extracted.is_none());
    }

    #[actix_web::test]
    async fn test_status_to_response_with_error_details() {
        use actix_web::body::MessageBody;
        use std::str::FromStr;

        let mut metadata = tonic::metadata::MetadataMap::new();
        let error_details_json = r#"{"code":2,"message":"Duplicate tool names","detail":"Tool names must be unique within a request","help_uri":"https://docs.augmentcode.com/errors/duplicate-tool-names"}"#;
        metadata.insert(
            "error-details",
            tonic::metadata::MetadataValue::<tonic::metadata::Ascii>::from_str(error_details_json)
                .unwrap(),
        );

        let status =
            tonic::Status::with_metadata(tonic::Code::InvalidArgument, "Test error", metadata);

        let response = status_to_response(&status);

        // The response should be a BadRequest
        assert_eq!(response.status(), actix_web::http::StatusCode::BAD_REQUEST);

        // Test that the response body contains the error details
        let body_bytes = response.into_body().try_into_bytes().unwrap();
        let body_str = std::str::from_utf8(&body_bytes).unwrap();
        let response_json: serde_json::Value = serde_json::from_str(body_str).unwrap();

        // Verify the main error message (status_to_response uses standardized messages)
        assert_eq!(response_json["error"], "Unidentified internal error");

        // Verify the error details are included
        let error_details = &response_json["error_details"];
        assert_eq!(error_details["code"], 2);
        assert_eq!(error_details["message"], "Duplicate tool names");
        assert_eq!(
            error_details["detail"],
            "Tool names must be unique within a request"
        );
        assert_eq!(
            error_details["help_uri"],
            "https://docs.augmentcode.com/errors/duplicate-tool-names"
        );
    }
}

#[cfg(test)]
mod suspicious_user_check_tests {
    use super::*;
    use actix_web::test::TestRequest;

    #[test]
    fn test_suspicious_user_check() {
        // Test new() method
        let check = SuspiciousUserCheck::new(UserIpMapping::new(
            chrono::Duration::hours(1),
            1000,
            3600000,
        ));
        assert!(check.state.lock().unwrap().is_none());

        // Setup mock feature flags
        let feature_flags = feature_flags::setup_local();

        // Test with empty regex
        SUSPICIOUS_USER_AGENT.set_local(&feature_flags, "");
        let state = check.get_state(&feature_flags);
        assert!(state.is_none());

        // Test with valid regex
        SUSPICIOUS_USER_AGENT.set_local(&feature_flags, "^bad-agent$");
        let state = check.get_state(&feature_flags);
        assert!(state.is_some());
        let state = state.unwrap();
        assert_eq!(state.user_agent_last_feature_flag, "^bad-agent$");

        // Test with invalid regex
        SUSPICIOUS_USER_AGENT.set_local(&feature_flags, "[invalid(");
        let state = check.get_state(&feature_flags);
        // Should return the last valid state
        assert!(state.is_some());
        let state = state.unwrap();
        assert_eq!(state.user_agent_last_feature_flag, "^bad-agent$");
    }

    #[test]
    fn test_suspicious_user_check_is_suspicious() {
        let check = SuspiciousUserCheck::new(UserIpMapping::new(
            chrono::Duration::hours(1),
            1000,
            3600000,
        ));
        let feature_flags = feature_flags::setup_local();

        // Set up a regex that matches a specific user agent
        SUSPICIOUS_USER_AGENT.set_local(&feature_flags, "^bad-agent$");

        // Create a request with a matching user agent
        let mut req = TestRequest::default();
        req = req.insert_header(("user-agent", "bad-agent"));
        let req = req.to_http_request();

        // Test with matching user agent
        let result = check.is_suspicious(&feature_flags, &req);
        assert!(result.is_ok());
        assert!(result.unwrap());

        // Create a request with a non-matching user agent
        let mut req = TestRequest::default();
        req = req.insert_header(("user-agent", "good-agent"));
        let req = req.to_http_request();

        // Test with non-matching user agent
        let result = check.is_suspicious(&feature_flags, &req);
        assert!(result.is_ok());
        assert!(!result.unwrap());

        // Create a request with no user agent
        let req = TestRequest::default().to_http_request();

        // Test with no user agent
        let result = check.is_suspicious(&feature_flags, &req);
        assert!(result.is_ok());
        assert!(!result.unwrap());

        // Test with empty regex
        SUSPICIOUS_USER_AGENT.set_local(&feature_flags, "");
        let result = check.is_suspicious(&feature_flags, &req);
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }

    #[test]
    fn test_user_ip_mapping_too_many_ips() {
        let check = UserIpMapping::new(chrono::Duration::hours(1), 1000, 3600000);
        let feature_flags = feature_flags::setup_local();

        // Set the max IPs per user hour to 2
        MAX_IPS_PER_USER_HOUR.set_local(&feature_flags, 2);

        // Test with a single IP
        let user_id = "test_user";
        let client_addr = "***********";
        let result = check
            .has_too_many_ips(&feature_flags, user_id, client_addr)
            .unwrap();
        assert!(!result);

        // Test with a second IP for the same user
        let client_addr2 = "***********";
        let result = check
            .has_too_many_ips(&feature_flags, user_id, client_addr2)
            .unwrap();
        assert!(!result);

        // Test with a third IP for the same user (should be suspicious)
        let client_addr3 = "***********";
        let result = check
            .has_too_many_ips(&feature_flags, user_id, client_addr3)
            .unwrap();
        assert!(result);

        // Test with a different user (should not be suspicious)
        let user_id2 = "test_user2";
        let result = check
            .has_too_many_ips(&feature_flags, user_id2, client_addr)
            .unwrap();
        assert!(!result);

        // Test with disabled feature flag (should not be suspicious)
        MAX_IPS_PER_USER_HOUR.set_local(&feature_flags, 0);
        let result = check
            .has_too_many_ips(&feature_flags, user_id, client_addr3)
            .unwrap();
        assert!(!result);
    }

    #[actix_web::test]
    async fn test_parse_user_agent() {
        // Test with IntelliJ format
        let user_agent =
            "augment.intellij/0.75.0 (Mac OS X; aarch64; 10.15.7) IntelliJ IDEA/2023.1";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, Some((Some("intellij"), Some("0.75.0"))));

        // Test with VSCode format
        let user_agent = "Augment.vscode-augment/0.75.0 (darwin; arm64; 20.3.0) vscode/1.90.2";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, Some((Some("vscode"), Some("0.75.0"))));

        // Test with Vim format
        let user_agent = "Augment.vim/0.5.0 neovim/0.9.0";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, Some((Some("vim"), Some("0.5.0"))));

        // Test with Python format
        let user_agent = "api_proxy_client/0 (Python)";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, Some((Some("python"), Some("0"))));

        // Test with CLI format (old format)
        let user_agent = "augment.cli/0.1";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, Some((Some("cli"), Some("0.1"))));

        // Test with CLI format (new format with interactive/noninteractive suffix)
        let user_agent = "augment.cli/0.1.3/interactive";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, Some((Some("cli"), Some("0.1.3/interactive"))));

        let user_agent = "augment.cli/0.1.3/noninteractive";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, Some((Some("cli"), Some("0.1.3/noninteractive"))));

        // Test with version numbers with suffixes
        let user_agent =
            "augment.intellij/0.75.0-beta3 (Mac OS X; aarch64; 10.15.7) IntelliJ IDEA/2023.1";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, Some((Some("intellij"), Some("0.75.0-beta3"))));

        let user_agent =
            "Augment.vscode-augment/1.0.0-SNAPSHOT (darwin; arm64; 20.3.0) vscode/1.90.2";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, Some((Some("vscode"), Some("1.0.0-SNAPSHOT"))));

        let user_agent = "Augment.vim/0.5.0-dev neovim/0.9.0";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, Some((Some("vim"), Some("0.5.0-dev"))));

        // Test with case insensitivity
        let user_agent = "AUGMENT.intellij/0.76.0 (Windows NT 10.0)";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, Some((Some("intellij"), Some("0.76.0"))));

        // Test with beachhead format (remote agents) - now treated as unknown
        let user_agent = "beachhead/c9fb569";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, None);

        // Test with unknown but valid format (contains .augment)
        // Since it's unknown it should still return None
        let user_agent = "augmentcode.augment/1.2.3 (Linux)";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, None);

        // Test with unknown format that should not be matched
        let user_agent = "new-client/2.0.0";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, None);

        // Test with no recognizable format
        let user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36";
        let result = parse_user_agent(user_agent);
        assert_eq!(result, None);
    }

    #[actix_web::test]
    async fn test_cli_access_feature_flag() {
        // Test that the CLI access feature flag is properly defined and can be used
        let feature_flags = feature_flags::setup_local();

        // Test default value (should be true)
        assert!(
            CLI_ACCESS_ENABLED.get_from(&feature_flags),
            "CLI access should be enabled by default"
        );

        // Test that the flag can be bound with client attribute
        let bound_flags = feature_flags
            .bind_attribute("tenant_name", "test_tenant")
            .unwrap()
            .bind_attribute("client", "cli")
            .unwrap();

        // The flag should still be accessible after binding
        assert!(
            CLI_ACCESS_ENABLED.get_from(&bound_flags),
            "CLI access flag should work with bound attributes"
        );
    }

    #[test]
    fn test_is_version_gte() {
        // Test equal versions
        assert!(is_version_gte("1.0.0", "1.0.0"));
        assert!(is_version_gte("0.487.1", "0.487.1"));

        // Test current version greater than minimum
        assert!(is_version_gte("1.0.1", "1.0.0"));
        assert!(is_version_gte("1.1.0", "1.0.0"));
        assert!(is_version_gte("2.0.0", "1.0.0"));
        assert!(is_version_gte("0.488.0", "0.487.1"));

        // Test current version less than minimum
        assert!(!is_version_gte("1.0.0", "1.0.1"));
        assert!(!is_version_gte("0.487.0", "0.487.1"));
        assert!(!is_version_gte("0.486.9", "0.487.1"));

        // Test with pre-release versions
        assert!(is_version_gte("1.0.0-beta", "1.0.0"));
        assert!(is_version_gte("1.0.1-SNAPSHOT", "1.0.0"));
        assert!(!is_version_gte("0.9.9-beta", "1.0.0"));

        // Test edge cases
        assert!(is_version_gte("1.0.0", "")); // Empty minimum version
        assert!(!is_version_gte("", "1.0.0")); // Empty current version
        assert!(is_version_gte("", "")); // Both empty

        // Test invalid version formats
        assert!(!is_version_gte("invalid", "1.0.0"));
        assert!(is_version_gte("1.0.0", "invalid")); // Invalid minimum allows request
    }

    #[test]
    fn test_should_enforce_version_upgrade() {
        // Create a mock feature flags service
        let feature_flags = feature_flags::setup_local();

        // Set up feature flags for testing
        crate::handlers_chat::TRIAL_MIN_VSCODE_VERSION.set_local(&feature_flags, "0.487.0");
        crate::handlers_chat::TRIAL_MIN_INTELLIJ_VERSION.set_local(&feature_flags, "0.75.0");

        // Test cases with outdated VSCode (should require upgrade)
        let outdated_vscode_ua =
            "Augment.vscode-augment/0.486.0 (darwin; arm64; 20.3.0) vscode/1.90.2";
        assert!(should_enforce_version_upgrade(
            Some(outdated_vscode_ua),
            &feature_flags
        ));

        // Test cases with current VSCode (should not require upgrade)
        let current_vscode_ua =
            "Augment.vscode-augment/0.487.1 (darwin; arm64; 20.3.0) vscode/1.90.2";
        assert!(!should_enforce_version_upgrade(
            Some(current_vscode_ua),
            &feature_flags
        ));

        // Test cases with outdated IntelliJ (should require upgrade)
        let outdated_intellij_ua =
            "augment.intellij/0.74.0 (Mac OS X; aarch64; 10.15.7) IntelliJ IDEA/2023.1";
        assert!(should_enforce_version_upgrade(
            Some(outdated_intellij_ua),
            &feature_flags
        ));

        // Test cases with current IntelliJ (should not require upgrade)
        let current_intellij_ua =
            "augment.intellij/0.75.1 (Mac OS X; aarch64; 10.15.7) IntelliJ IDEA/2023.1";
        assert!(!should_enforce_version_upgrade(
            Some(current_intellij_ua),
            &feature_flags
        ));

        // Test with recognized client that's not subject to version enforcement (should not force upgrade)
        let vim_ua = "Augment.vim/0.5.0 neovim/0.9.0";
        assert!(!should_enforce_version_upgrade(
            Some(vim_ua),
            &feature_flags
        ));

        // Test with beachhead client (remote agents, should not force upgrade)
        let beachhead_ua = "beachhead/c9fb569";
        assert!(!should_enforce_version_upgrade(
            Some(beachhead_ua),
            &feature_flags
        ));

        // Test with missing user-agent header (should force upgrade)
        assert!(should_enforce_version_upgrade(None, &feature_flags));

        // Test with unparseable user-agent (should NOT force upgrade)
        let unparseable_ua = "invalid-user-agent-string";
        assert!(!should_enforce_version_upgrade(
            Some(unparseable_ua),
            &feature_flags
        ));

        // Test with browser user-agent (unparseable, should NOT force upgrade)
        let browser_ua = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36";
        assert!(!should_enforce_version_upgrade(
            Some(browser_ua),
            &feature_flags
        ));

        // Test with unknown client format (should NOT force upgrade)
        let unknown_client_ua = "SomeNewClient/1.0.0 (platform info)";
        assert!(!should_enforce_version_upgrade(
            Some(unknown_client_ua),
            &feature_flags
        ));
    }
}
