package main

import (
	"context"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	proto "github.com/augmentcode/augment/services/working_set/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var (
	blobsAddedMetric = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "au_working_set_blobs_added",
		Help:    "Number of blobs added in the last stats period",
		Buckets: prometheus.ExponentialBucketsRange(10, 1000000, 21),
	}, []string{"tenant"})
	blobsDeletedMetric = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "au_working_set_blobs_deleted",
		Help:    "Number of blobs deleted in the last stats period",
		Buckets: prometheus.ExponentialBucketsRange(10, 1000000, 21),
	}, []string{"tenant"})
	blobCountMetric = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "au_working_set_blob_count",
		Help:    "Histogram of the number of blobs",
		Buckets: prometheus.ExponentialBucketsRange(100, 10000000, 21),
	}, []string{"tenant"})
	blobCountBySessionMetric = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "au_working_set_blob_count_by_session",
		Help:    "Histogram of the number of blobs by session",
		Buckets: prometheus.ExponentialBucketsRange(100, 10000000, 21),
	}, []string{"tenant"})
	hourlyResidentBlobsMetric = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "au_working_set_hourly_resident_blobs",
		Help: "Number of blobs seen in this tenant in the last hour",
	}, []string{"tenant"})
	blobsIndexStatusByTfKeyMetric = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "au_working_set_blob_index_status",
		Help: "Estimated number of blobs in each indexer state tracked per transformation key",
	}, []string{"tenant", "status", "transformation_key"})
	checkpointSizeMetric = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "au_working_set_checkpoint_size",
		Help:    "Size of checkpoints in number of blobs",
		Buckets: prometheus.ExponentialBucketsRange(100, 10000000, 21),
	}, []string{"tenant"})

	// Service metrics
	startupTimeMetric = prometheus.NewHistogram(prometheus.HistogramOpts{
		Name:    "au_working_set_startup_time_seconds",
		Help:    "Time taken for the workingset service to start up in seconds",
		Buckets: prometheus.ExponentialBucketsRange(1, 1800, 15), // from 1s to 30min
	})
	rpcCallsMetric = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "au_working_set_rpc_calls",
		Help: "Total number of RPC calls by type",
	}, []string{"tenant_id", "rpc_type"})

	// Cache metrics
	blobsetCacheEntriesMetric = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "au_working_set_blobset_cache_entries",
		Help: "Number of entries in the blobset cache by cache type",
	}, []string{"cache_type"})
	blobsetCacheMemoryMetric = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "au_working_set_blobset_cache_memory_bytes",
		Help: "Memory allocation for the blobset cache in bytes by cache type",
	}, []string{"cache_type"})
	blobsetCacheUsageMetric = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "au_working_set_blobset_cache_usage",
		Help: "Number of blobset cache hits and misses by cache type",
	}, []string{"cache_type", "outcome"})

	// ANN index metrics
	annIndexOpsMetric = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "au_working_set_ann_index_ops",
		Help: "Total number of ANN index operations by type",
	}, []string{"tenant", "transformation_key", "op_type"})
	annIndexReuseDeltaMetric = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "au_working_set_index_reuse_delta",
		Help:    "Delta between the checkpoint and the reused ANN index",
		Buckets: prometheus.ExponentialBuckets(1, 2, 15), // from 1 to 32k
	}, []string{"tenant", "transformation_key"})
	annIndexRequestQueueDepth = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "au_working_set_ann_index_request_queue_depth",
		Help: "Current number of ANN index creation requests in the queue",
	})

	// LSH metrics
	lshIndexEntriesMetric = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "au_working_set_lsh_index_entries",
		Help: "Number of ANN indexes in the LSH index",
	}, []string{"tenant", "transformation_key"})
	lshSearchNumCandidatesMetric = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "au_working_set_lsh_search_num_candidates",
		Help:    "Number of candidates found via LSH search",
		Buckets: prometheus.ExponentialBuckets(1, 2, 15), // from 1 to 32k
	}, []string{"tenant", "transformation_key"})
	lshSearchLatencyMetric = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "au_working_set_lsh_search_latency",
		Help:    "Latency of LSH search in seconds",
		Buckets: prometheus.ExponentialBuckets(0.001, 2, 15), // from 1ms to 32s
	}, []string{"tenant", "transformation_key"})

	// Store size metrics
	activeCheckpointsMetric = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "au_working_set_num_active_checkpoints",
		Help: "Number of active checkpoints in the working set store",
	}, []string{"tenant"})
	activeTransformationKeysMetric = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "au_working_set_num_active_transformation_keys",
		Help: "Number of active transformation keys in the working set store",
	}, []string{"tenant"})
	pendingAnnIndexesMetric = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "au_working_set_num_pending_ann_indexes",
		Help: "Number of pending ANN indexes in the working set store",
	}, []string{"tenant", "transformation_key"})

	// Latency metrics
	resolutionLatencyMetric = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "au_working_set_resolution_latency",
		Help:    "Latency of resolution in seconds by type",
		Buckets: prometheus.ExponentialBuckets(0.001, 2, 15), // from 1ms to 32s
	}, []string{"tenant", "transformation_key", "type"})
	annIndexCreationLatencyMetric = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "au_working_set_ann_index_creation_latency",
		Help:    "Latency of ANN index creation in seconds",
		Buckets: prometheus.ExponentialBuckets(0.001, 2, 20), // from 1ms to ~16 minutes
	}, []string{"tenant", "transformation_key"})
	annIndexSearchLatencyMetric = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "au_working_set_ann_index_search_latency",
		Help:    "Latency of ANN index search in seconds",
		Buckets: prometheus.ExponentialBuckets(0.001, 2, 12), // from 1ms to 4s
	}, []string{"tenant", "transformation_key"})
)

const (
	blobIndexStatusUnindexed = "unindexed"
	blobIndexStatusIndexed   = "indexed"
)

// Feature flag to control whether to persist workingset state to Bigtable
var PersistToBigtableFlag = featureflags.NewBoolFlag("workingset_persist_to_bigtable", false)

type workingSetManagerServer struct {
	featureFlagHandle featureflags.FeatureFlagHandle
	workingSetStore   WorkingSetStore
}

func init() {
	prometheus.Register(blobsAddedMetric)
	prometheus.Register(blobsDeletedMetric)
	prometheus.Register(blobCountMetric)
	prometheus.Register(blobCountBySessionMetric)
	prometheus.Register(hourlyResidentBlobsMetric)
	prometheus.Register(blobsIndexStatusByTfKeyMetric)
	prometheus.Register(blobsetCacheEntriesMetric)
	prometheus.Register(blobsetCacheMemoryMetric)
	prometheus.Register(blobsetCacheUsageMetric)
	prometheus.Register(lshIndexEntriesMetric)
	prometheus.Register(annIndexOpsMetric)
	prometheus.Register(lshSearchNumCandidatesMetric)
	prometheus.Register(startupTimeMetric)
	prometheus.Register(checkpointSizeMetric)
	prometheus.Register(rpcCallsMetric)
	prometheus.Register(resolutionLatencyMetric)
	prometheus.Register(annIndexReuseDeltaMetric)
	prometheus.Register(lshSearchLatencyMetric)
	prometheus.Register(annIndexCreationLatencyMetric)
	prometheus.Register(annIndexRequestQueueDepth)
	prometheus.Register(activeCheckpointsMetric)
	prometheus.Register(activeTransformationKeysMetric)
	prometheus.Register(pendingAnnIndexesMetric)
}

func (s *workingSetManagerServer) RegisterWorkingSet(ctx context.Context, req *proto.RegisterWorkingSetRequest) (*proto.RegisterWorkingSetResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)
	claims, _ := auth.GetAugmentClaims(ctx)
	ctx = claims.AnnotateLogContext(ctx)

	tenantID, err := claims.GetTenantID()
	if err != nil {
		return nil, err
	}
	sessionId := requestContext.RequestSessionId
	requestId := requestContext.RequestId

	log.Ctx(ctx).Info().Msgf("Register working set: %v for session %v by request %v", formatBlobsList(req.Blobs), sessionId, string(requestId))
	rpcCallsMetric.WithLabelValues(tenantID, "register_working_set").Inc()

	if sessionId == "" {
		return nil, status.Error(codes.InvalidArgument, "session_id is required")
	}
	if req.Blobs == nil {
		return nil, status.Error(codes.InvalidArgument, "blobs is required")
	}

	err = s.workingSetStore.RegisterWorkingSet(ctx, tenantID, sessionId, req.Blobs)
	if err != nil {
		return nil, err
	}

	resp := &proto.RegisterWorkingSetResponse{}
	return resp, nil
}

func (s *workingSetManagerServer) CreateAnnIndexForCheckpoint(ctx context.Context, req *proto.CreateAnnIndexForCheckpointRequest) (*proto.CreateAnnIndexForCheckpointResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)
	claims, _ := auth.GetAugmentClaims(ctx)
	ctx = claims.AnnotateLogContext(ctx)

	tenantID, err := claims.GetTenantID()
	if err != nil {
		return nil, err
	}
	checkpointID := req.CheckpointId
	transformationKey := req.TransformationKey

	log.Ctx(ctx).Info().Msgf("Request to create ANN index for checkpoint: %s for transformation key: %s (tenant %s)",
		checkpointID, transformationKey, tenantID)
	rpcCallsMetric.WithLabelValues(tenantID, "create_ann_index_for_checkpoint").Inc()

	if checkpointID == "" {
		return nil, status.Error(codes.InvalidArgument, "checkpoint_id is required")
	}
	if transformationKey == "" {
		return nil, status.Error(codes.InvalidArgument, "transformation_key is required")
	}

	err = s.workingSetStore.CreateAnnIndexForCheckpoint(ctx, checkpointID, tenantID, transformationKey)
	if err != nil {
		return nil, err
	}

	resp := &proto.CreateAnnIndexForCheckpointResponse{}
	return resp, nil
}

func (s *workingSetManagerServer) GetActiveCheckpoints(ctx context.Context, req *proto.GetActiveCheckpointsRequest) (*proto.GetActiveCheckpointsResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)
	claims, _ := auth.GetAugmentClaims(ctx)
	ctx = claims.AnnotateLogContext(ctx)

	tenantID, err := claims.GetTenantID()
	if err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Request to get active checkpoints for tenant %s", tenantID)
	rpcCallsMetric.WithLabelValues(tenantID, "get_active_checkpoints").Inc()

	checkpointIDs, err := s.workingSetStore.GetActiveCheckpoints(tenantID)
	if err != nil {
		return nil, err
	}

	resp := &proto.GetActiveCheckpointsResponse{
		CheckpointIds: checkpointIDs,
	}
	return resp, nil
}

func (s *workingSetManagerServer) GetActiveTransformationKeys(ctx context.Context, req *proto.GetActiveTransformationKeysRequest) (*proto.GetActiveTransformationKeysResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)
	claims, _ := auth.GetAugmentClaims(ctx)
	ctx = claims.AnnotateLogContext(ctx)

	tenantID, err := claims.GetTenantID()
	if err != nil {
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Request to get active transformation keys for tenant %s", tenantID)
	rpcCallsMetric.WithLabelValues(tenantID, "get_active_transformation_keys").Inc()

	transformationKeys, err := s.workingSetStore.GetActiveTransformationKeys(tenantID)
	if err != nil {
		return nil, err
	}

	resp := &proto.GetActiveTransformationKeysResponse{
		TransformationKeys: transformationKeys,
	}
	return resp, nil
}

func NewWorkingSetManagerServer(featureFlagHandle featureflags.FeatureFlagHandle, workingSetStore WorkingSetStore) *workingSetManagerServer {
	return &workingSetManagerServer{
		featureFlagHandle: featureFlagHandle,
		workingSetStore:   workingSetStore,
	}
}
