import { Flex, Text, TextArea, RadioGroup } from "@radix-ui/themes";
import {
  CANCELLATION_REASON_LABELS,
  MAX_ADDITIONAL_FEEDBACK_LENGTH,
} from "app/schemas/cancellation-feedback";

export interface CancellationSurveyFormData {
  cancellationReason: string;
  additionalFeedback: string;
  error: string;
}

interface CancellationSurveyProps {
  formData: CancellationSurveyFormData;
  setFormData: React.Dispatch<React.SetStateAction<CancellationSurveyFormData>>;
}

export function CancellationSurvey({
  formData,
  setFormData,
}: CancellationSurveyProps) {
  const { cancellationReason, additionalFeedback, error } = formData;

  return (
    <Flex direction="column" gap="4">
      <Text size="2">
        Before you go, we&apos;d love to understand why you&apos;re canceling.
        Your feedback helps us improve Augment for everyone.
      </Text>

      <Flex direction="column" gap="3">
        <Text weight="medium" size="2">
          What&apos;s the main reason for canceling? *
        </Text>
        <RadioGroup.Root
          value={cancellationReason}
          onValueChange={(value) => {
            setFormData((oldState) => ({
              ...oldState,
              cancellationReason: value,
              error: "",
            }));
          }}
        >
          <Flex direction="column" gap="2">
            {CANCELLATION_REASON_LABELS.map((reason) => (
              <Text key={reason.value} as="label" size="2">
                <Flex align="center" gap="2">
                  <RadioGroup.Item value={reason.value} />
                  {reason.label}
                </Flex>
              </Text>
            ))}
          </Flex>
        </RadioGroup.Root>
        {error && (
          <Text size="1" color="red">
            {error}
          </Text>
        )}
      </Flex>

      <Flex direction="column" gap="2">
        <Text weight="medium" size="2">
          {cancellationReason === "OTHER"
            ? "Please provide more detail *"
            : "Any additional feedback? (optional)"}
        </Text>
        <TextArea
          placeholder="Tell us more about your experience..."
          value={additionalFeedback}
          onChange={(e) => {
            setFormData((oldState) => ({
              ...oldState,
              additionalFeedback: e.target.value,
            }));
          }}
          rows={4}
          style={{ resize: "vertical" }}
          maxLength={MAX_ADDITIONAL_FEEDBACK_LENGTH}
        />
      </Flex>
    </Flex>
  );
}
