/**
 * Handler that will be called during the execution of a PostLogin flow.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
exports.onExecutePostLogin = async (event, api) => {
  const firstLogin = event.stats?.logins_count <= 1;
  var grandfathered = event.user?.user_metadata?.grandfathered;
  if (grandfathered === undefined) {
    grandfathered = !firstLogin;
    api.user.setUserMetadata("grandfathered", grandfathered);
  }

  const controller = new AbortController();
  const options = {
    method: "POST",
    signal: controller.signal,
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
      Accept: "application/json",
      Authorization: `Bearer ${event.secrets.API_KEY}`,
      "User-Agent": "auth-central-allow-check/1",
    },
    body: JSON.stringify({
      event_type: "post-login-action",
      event: Object.fromEntries(
        Object.entries(event).filter(
          ([k, v]) => k !== "secrets" && k !== "configuration",
        ),
      ),
    }),
  };

  const timeout = setTimeout(() => controller.abort(), 5000);

  try {
    const response = await fetch(
      "https://auth-central.dev-costa.us-central1.dev.augmentcode.com/api/notify_auth0_action",
      options,
    );
    if (response.ok) {
      const resp = await response.json();
      if (!grandfathered && resp.verdict === "block") {
        api.access.deny("Login or signup rejected");
        return;
      }
    } else {
      console.log("Response status: ", response.status);
    }
  } catch (e) {
    console.log(e);
  }

  // We're going ot allow this login
  api.user.setUserMetadata("grandfathered", true);
};

/**
 * Handler that will be invoked when this action is resuming after an external redirect. If your
 * onExecutePostLogin function does not perform a redirect, this function can be safely ignored.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
// exports.onContinuePostLogin = async (event, api) => {
// };
