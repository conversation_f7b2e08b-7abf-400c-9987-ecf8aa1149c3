use lazy_static::lazy_static;
use prometheus::{
    register_histogram_vec, register_int_counter, register_int_counter_vec, register_int_gauge,
    register_int_gauge_vec, HistogramVec, IntCounter, IntCounterVec, IntGauge, IntGaugeVec, Opts,
};

lazy_static! {
    /// Histogram of request latencies to auth query endpoints. This can also be used to
    /// calculate throughput because Prometheus histograms keep a count.
    pub static ref RESPONSE_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_rpc_latency_histogram",
        "Histogram of RPC latencies",
        &["service", "endpoint", "status_code", "request_source", "tenant_name"]
    )
    .expect("metric can be created");

    /// The number of currently active requests
    pub static ref ACTIVE_REQUESTS_COLLECTOR: IntGaugeVec = register_int_gauge_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_rpc_active_requests_gauge",
        "The number of currently active requests",
        &["service", "endpoint", "tenant_name"],
    )
    .expect("metric can be created");

    /// The number of requests initiated
    pub static ref STARTED_REQUESTS_COUNTER: IntCounterVec = register_int_counter_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_rpc_started_requests_total",
        "Total number of requests initiated",
        &["service", "endpoint"],
    )
    .expect("metric can be created");

    /// The number of requests completed
    pub static ref HANDLED_REQUESTS_COUNTER: IntCounterVec = register_int_counter_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_rpc_handled_requests_total",
        "Total number of requests completed",
        &["service", "endpoint", "status_code"],
    )
    .expect("metric can be created");

    pub static ref TENANT_NOT_FOUND_COUNTER: IntCounter = register_int_counter!(
        "au_auth_query_tenant_not_found_count",
        "Number of times a tenant was not found"
    )
    .expect("metric can be created");

    pub static ref CACHE_LOOKUP_COUNT_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_auth_query_cache_lookup_count",
            "Cache lookup count"
        ),
        &["result"]
    )
    .expect("metric can be created");

    pub static ref CACHE_ENTRY_COUNT: IntGauge = register_int_gauge!(
        "au_auth_query_cache_entry_count",
        "Number of entries in the cache",
    )
    .expect("metric can be created");
}
