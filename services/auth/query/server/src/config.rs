use clap::Parser;
use serde::{Deserialize, Serialize};
use std::{fs::File, io, path::Path};

use crate::api_token_db;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TokenConfig {
    pub endpoint: String,
    pub request_timeout_secs: f32,
}

/// structure representing the configuration information in the configuration file, i.e.
/// the configmap of the auth query pod.
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Config {
    // the address to bind the rpc server to, e.g. 0.0.0.0:50051
    pub bind_address: String,
    pub namespace: String,

    // the path to the feature flags sdk key
    pub feature_flags_sdk_key_path: Option<std::path::PathBuf>,
    pub dynamic_feature_flags_endpoint: Option<String>,

    // Configure the HTTP server that returns Prometheus metrics.
    pub metrics_server_bind_address: String,
    pub metrics_server_port: u16,

    // if client MTLS should be used.
    pub client_mtls: bool,

    // if client MTLS is used, the paths to the certificates and keys required
    pub client_ca_path: String,
    pub client_key_path: String,
    pub client_cert_path: String,

    // if server MTLS should be used.
    pub server_mtls: bool,

    // if server MTLS is used, the paths to the certificates and keys required
    pub server_ca_path: String,
    pub server_key_path: String,
    pub server_cert_path: String,

    pub token: TokenConfig,

    // the endpoint of the tenant watcher service
    // the value null is treated specially with a test implementation
    pub tenant_watcher_endpoint: String,

    pub auth_central_endpoint: String,
    pub token_info_cache_ttl_secs: f32,

    // Service token cache TTL in seconds
    pub service_token_cache_ttl_secs: f32,

    pub migrated_tenant_ids: Vec<String>,

    pub max_concurrent_streams: u32,

    pub max_connection_age_secs: u64,
}

impl Config {
    /// read the configuration from a file
    pub fn read(path: &Path) -> Result<Config, tonic::Status> {
        let file = File::open(path).map_err(|e| tonic::Status::internal(e.to_string()))?;

        let config: Config =
            serde_json::from_reader(file).map_err(|e| tonic::Status::internal(e.to_string()))?;
        Ok(config)
    }
}

/// structure representing hashes of the API token secrets used by auth query
#[derive(Deserialize)]
pub struct Secrets {
    pub api_tokens: Vec<api_token_db::ApiTokenDbEntry>,
}

impl Secrets {
    /// read the secrets from a file
    pub fn read<R: io::Read>(reader: R) -> Result<Secrets, tonic::Status> {
        let secrets: Secrets =
            serde_json::from_reader(reader).map_err(|e| tonic::Status::internal(e.to_string()))?;
        Ok(secrets)
    }
}

/// Search for a pattern in a file and display the lines that contain it.
#[derive(Parser, Debug)]
pub struct CliArguments {
    /// path to the configuration file
    #[arg(long)]
    pub config_file: std::path::PathBuf,

    /// path to the api token file
    #[arg(long)]
    pub secrets_file: std::path::PathBuf,
}

#[cfg(test)]
mod tests {
    use secrecy::ExposeSecret;

    #[test]
    fn test_secrets_read() {
        let input = br#"{ "api_tokens": [ { "token_sha256": "CAFEEE", "user_id": "0" } ] }"#;
        let result = super::Secrets::read(&input[..]);
        assert!(result.is_err()); // Missing tenant name

        let input = br#"{ "api_tokens": [ { "token_sha256": "CAFEEE", "user_id": "0", "tenant_name": "dev-augie" } ] }"#;
        let secrets = super::Secrets::read(&input[..]).unwrap();
        assert_eq!(secrets.api_tokens.len(), 1);
        assert_eq!(secrets.api_tokens[0].token_sha256.expose_secret(), "CAFEEE");
        assert_eq!(secrets.api_tokens[0].user_id, "0");
        assert_eq!(secrets.api_tokens[0].tenant_name, "dev-augie");
    }
}
