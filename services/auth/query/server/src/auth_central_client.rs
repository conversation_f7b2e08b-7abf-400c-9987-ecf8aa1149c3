use std::{sync::Arc, time::Duration};

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::create_channel;
use moka::future::Cache;
use request_context::RequestContext;
use secrecy::{ExposeSecret, SecretString};
use tonic::transport::ClientTlsConfig;
use tracing_tonic::client::TracingService;

use crate::metrics::{CACHE_ENTRY_COUNT, CACHE_LOOKUP_COUNT_COLLECTOR};
use crate::proto::{auth_central, auth_query};

// Convert from auth_central subscription to auth_query subscription
pub fn convert_subscription(
    from_subscription: Option<auth_central::get_token_info_response::Subscription>,
) -> Option<auth_query::get_token_info_response::Subscription> {
    // Convert from auth_central subscription to auth_query subscription
    match from_subscription {
        None => None,
        Some(subscription) => match subscription {
            auth_central::get_token_info_response::Subscription::Enterprise(_) => Some(
                auth_query::get_token_info_response::Subscription::Enterprise(
                    auth_query::EnterpriseSubscription {},
                ),
            ),
            auth_central::get_token_info_response::Subscription::ActiveSubscription(active) => {
                Some(
                    auth_query::get_token_info_response::Subscription::ActiveSubscription(
                        auth_query::ActiveSubscription {
                            end_date: active.end_date,
                            usage_balance_depleted: active.usage_balance_depleted,
                        },
                    ),
                )
            }
            auth_central::get_token_info_response::Subscription::Trial(trial) => Some(
                auth_query::get_token_info_response::Subscription::Trial(auth_query::Trial {
                    trial_end: trial.trial_end,
                }),
            ),
            auth_central::get_token_info_response::Subscription::InactiveSubscription(
                _inactive,
            ) => Some(
                auth_query::get_token_info_response::Subscription::InactiveSubscription(
                    auth_query::InactiveSubscription {},
                ),
            ),
        },
    }
}

#[async_trait]
pub trait AuthCentralClient {
    async fn get_token_info(
        &self,
        token: SecretString,
        request_context: &RequestContext,
    ) -> tonic::Result<auth_central::GetTokenInfoResponse>;
}

#[derive(Clone)]
pub struct AuthCentralClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    request_timeout: Duration,
    // Querying the token happens on every API call and may cross GCP regions,
    // so it's important to cache the result (including negative results while
    // we're in the process of migrating to this path).
    token_info_cache: Cache<String, tonic::Result<auth_central::GetTokenInfoResponse>>,
    client:
        Arc<Mutex<Option<auth_central::auth_service_client::AuthServiceClient<TracingService>>>>,
}

#[async_trait]
impl AuthCentralClient for AuthCentralClientImpl {
    async fn get_token_info(
        &self,
        token: SecretString,
        request_context: &RequestContext,
    ) -> tonic::Result<auth_central::GetTokenInfoResponse> {
        CACHE_ENTRY_COUNT.set(self.token_info_cache.entry_count() as i64);

        if let Some(res) = self
            .token_info_cache
            .get(&token.expose_secret().to_string())
            .await
        {
            CACHE_LOOKUP_COUNT_COLLECTOR
                .with_label_values(&["hit"])
                .inc();
            return res;
        }
        CACHE_LOOKUP_COUNT_COLLECTOR
            .with_label_values(&["miss"])
            .inc();

        let mut client = match self.get_client().await {
            Ok(c) => c,
            Err(e) => {
                tracing::error!("auth central client not ready: {}", e);
                return Err(tonic::Status::unavailable("auth central not ready"));
            }
        };

        let mut request = tonic::Request::new(auth_central::GetTokenInfoRequest {
            token: token.expose_secret().to_string(),
            requestor: "auth_query".to_string(), // TODO: ?
        });
        request_context.annotate(request.metadata_mut());

        let res = client
            .get_token_info(request)
            .await
            .map(|resp| resp.into_inner());

        match res {
            Ok(_) => {
                self.token_info_cache
                    .insert(token.expose_secret().to_string(), res.clone())
                    .await;
            }
            Err(ref s) if s.code() == tonic::Code::NotFound => {
                self.token_info_cache
                    .insert(token.expose_secret().to_string(), res.clone())
                    .await;
            }
            Err(_) => {}
        }
        res
    }
}

impl AuthCentralClientImpl {
    pub fn new(
        endpoint: &str,
        tls_config: Option<ClientTlsConfig>,
        token_info_cache_ttl: Duration,
        request_timeout: Duration,
    ) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            request_timeout,
            token_info_cache: Cache::builder()
                // Cache up to 100k tokens (per tenant)
                .max_capacity(100_000)
                // Cache entries live for up to 15 minutes
                .time_to_live(token_info_cache_ttl)
                .build(),
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<
        auth_central::auth_service_client::AuthServiceClient<TracingService>,
        tonic::transport::Error,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel = create_channel(
                    self.endpoint.to_string(),
                    Some(self.request_timeout),
                    &self.tls_config,
                )
                .await?;
                let client = auth_central::auth_service_client::AuthServiceClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }
}
