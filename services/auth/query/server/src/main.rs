use std::{net::SocketAddr, time::Duration};

use crate::{
    api_token_db::UserEntry,
    auth_central_client::{AuthCentralClient, AuthCentralClientImpl},
    proto::auth_query::{
        auth_query_server::{AuthQuery, AuthQueryServer},
        GetTokenInfoRequest, GetTokenInfoResponse,
    },
};
use auth_entities_proto::auth_entities::{user_id::UserIdType, UserId};
use clap::Parser;
use config::CliArguments;
use grpc_metrics::MetricsMiddlewareLayer;
use request_context::RequestContext;
use secrecy::{ExposeSecret, SecretString};
use std::sync::Arc;
use std::time::Instant;
use struct_logging::setup_struct_logging;
use tenant_watcher_client::TenantCacheClient;
use tenant_watcher_client::TenantWatcherClient;
use tokio::select;
use tokio::signal::unix::{signal, SignalKind};
use tokio::sync::Mutex;
use tonic::transport::{Certificate, Identity};
use tonic::transport::{ClientTlsConfig, Server, ServerTlsConfig};
use tonic_reflection::server::ServerReflectionServer;

use crate::config::Config;
use crate::metrics::{
    ACTIVE_REQUESTS_COLLECTOR, HANDLED_REQUESTS_COUNTER, RESPONSE_LATENCY_COLLECTOR,
    STARTED_REQUESTS_COUNTER, TENANT_NOT_FOUND_COUNTER,
};
use token_exchange_client::{TokenExchangeClient, TokenExchangeClientImpl};

/// Cached service token with expiration time
///
/// This struct holds a service token with AUTH_R scope that is cached to avoid
/// repeatedly requesting the same token from the token exchange service.
/// The token is cached for the duration specified in the configuration
/// (service_token_cache_ttl_secs, default 5 minutes).
#[derive(Clone)]
struct CachedServiceToken {
    token: SecretString,
    expires_at: Instant,
}

/// State of the service token cache to prevent concurrent token fetching
enum ServiceTokenCacheState {
    /// No token cached or token is expired
    Empty,
    /// Valid token is cached
    Cached(CachedServiceToken),
    /// Token fetch is in progress, contains a shared future
    Fetching(Arc<tokio::sync::Notify>),
}

/// Abstraction over time to enable deterministic testing
trait Clock: Send + Sync {
    fn now(&self) -> Instant;
}

struct RealClock;
impl Clock for RealClock {
    fn now(&self) -> Instant {
        Instant::now()
    }
}

mod api_token_db;
mod auth_central_client;
mod config;
mod metrics;

pub mod proto {
    pub mod auth_central {
        tonic::include_proto!("auth");
    }
    pub mod auth_query {
        tonic::include_proto!("auth_query");
    }

    pub(crate) const FILE_DESCRIPTOR_SET: &[u8] =
        tonic::include_file_descriptor_set!("auth_query_descriptor");
}

fn read_all(f: &str) -> Result<String, tonic::Status> {
    match std::fs::read_to_string(f) {
        Err(e) => {
            tracing::error!("Failed to read file {}: {}", f, e);
            Err(tonic::Status::internal(e.to_string()))
        }
        Ok(s) => Ok(s),
    }
}

fn get_client_tls_creds(config: &Config) -> Result<Option<ClientTlsConfig>, tonic::Status> {
    if config.client_mtls {
        let client_key_data = read_all(&config.client_key_path)?;
        let client_cert_data = read_all(&config.client_cert_path)?;
        let client_ca_data = read_all(&config.client_ca_path)?;

        let client_identity = Identity::from_pem(client_cert_data, client_key_data);

        let tls = ClientTlsConfig::new()
            .ca_certificate(Certificate::from_pem(client_ca_data))
            .identity(client_identity);
        Ok(Some(tls))
    } else {
        Ok(None)
    }
}

fn get_server_tls_creds(config: &Config) -> Result<Option<ServerTlsConfig>, tonic::Status> {
    if config.server_mtls {
        let server_key_data = read_all(&config.server_key_path)?;
        let server_cert_data = read_all(&config.server_cert_path)?;
        let server_ca_data = read_all(&config.server_ca_path)?;

        let server_identity = Identity::from_pem(server_cert_data, server_key_data);

        let tls = ServerTlsConfig::new()
            .client_ca_root(Certificate::from_pem(server_ca_data))
            .identity(server_identity);
        Ok(Some(tls))
    } else {
        Ok(None)
    }
}

struct AuthQueryImpl {
    config: Config,
    api_token_db: Arc<api_token_db::ApiTokenDb>,
    token_exchange: Arc<dyn TokenExchangeClient + Send + Sync>,
    tenant_cache: Arc<dyn TenantCacheClient + Send + Sync>,
    auth_central_client: Arc<dyn AuthCentralClient + Send + Sync>,
    // Cache for service token with AUTH_R scope
    service_token_cache: Arc<Mutex<ServiceTokenCacheState>>,
    // Clock for time operations (injectable for tests)
    clock: Arc<dyn Clock>,
}

impl AuthQueryImpl {
    fn new(
        config: Config,
        api_token_db: Arc<api_token_db::ApiTokenDb>,
        token_exchange: Arc<dyn TokenExchangeClient + Send + Sync>,
        tenant_cache: Arc<dyn TenantCacheClient + Send + Sync>,
        auth_central_client: Arc<dyn AuthCentralClient + Send + Sync>,
    ) -> Self {
        AuthQueryImpl {
            config,
            api_token_db,
            token_exchange,
            tenant_cache,
            auth_central_client,
            service_token_cache: Arc::new(Mutex::new(ServiceTokenCacheState::Empty)),
            clock: Arc::new(RealClock),
        }
    }

    #[cfg(test)]
    fn with_clock(mut self, clock: Arc<dyn Clock>) -> Self {
        self.clock = clock;
        self
    }

    pub fn new_server(self) -> AuthQueryServer<Self> {
        AuthQueryServer::new(self)
    }

    /// Get a cached service token with AUTH_R scope, or create a new one if not cached or expired
    ///
    /// This method implements a time-based cache for service tokens with concurrency control. It:
    /// 1. Checks if we have a valid cached token that hasn't expired
    /// 2. If yes, returns the cached token
    /// 3. If no (cache miss or expired), ensures only one request fetches a new token
    /// 4. Other concurrent requests wait for the token fetch to complete
    /// 5. Caches the new token with the configured TTL
    ///
    /// The cache TTL is configured via `service_token_cache_ttl_secs` (default: 5 minutes)
    async fn get_cached_service_token(
        &self,
        request_context: &RequestContext,
    ) -> Result<SecretString, tonic::Status> {
        let cache_ttl = Duration::from_secs_f32(self.config.service_token_cache_ttl_secs);

        loop {
            let now = self.clock.now();

            let notify = {
                let mut cache_guard = self.service_token_cache.lock().await;

                match &*cache_guard {
                    ServiceTokenCacheState::Cached(cached) => {
                        if now < cached.expires_at {
                            tracing::debug!("Using cached service token");
                            return Ok(cached.token.clone());
                        }
                        // Token expired, need to fetch new one
                        let notify = Arc::new(tokio::sync::Notify::new());
                        *cache_guard = ServiceTokenCacheState::Fetching(notify.clone());
                        notify
                    }
                    ServiceTokenCacheState::Empty => {
                        // No token cached, need to fetch new one
                        let notify = Arc::new(tokio::sync::Notify::new());
                        *cache_guard = ServiceTokenCacheState::Fetching(notify.clone());
                        notify
                    }
                    ServiceTokenCacheState::Fetching(notify) => {
                        // Another request is already fetching, wait for it
                        let notify = notify.clone();
                        drop(cache_guard);
                        tracing::debug!("Waiting for concurrent token fetch to complete");
                        notify.notified().await;
                        continue; // Re-check the cache state
                    }
                }
            };

            // We are the request responsible for fetching the token - kick this
            // off in the background so it doesn't get interrupted by request
            // cancellation, or whatever else might happen to this gRPC request,
            // since this fetch affects all callers.
            let token_exchange = self.token_exchange.clone();
            let service_token_cache = self.service_token_cache.clone();
            let rc_for_task = request_context.clone();
            let clock = self.clock.clone();
            tokio::spawn(async move {
                tracing::info!(
                    "Service token cache miss or expired, requesting new token (bg task)"
                );
                let fetch_result = token_exchange
                    .get_signed_token_for_service(
                        "".to_string(),
                        &[token_exchange_client::token_scopes::Scope::AuthR],
                        &rc_for_task,
                    )
                    .await;

                let now = clock.now();
                let mut cache_guard = service_token_cache.lock().await;
                match fetch_result {
                    Ok(service_token) => {
                        let cached_token = CachedServiceToken {
                            token: service_token.clone(),
                            expires_at: now + cache_ttl,
                        };
                        *cache_guard = ServiceTokenCacheState::Cached(cached_token);
                        tracing::info!(
                            "Cached new service token for {} seconds",
                            cache_ttl.as_secs()
                        );
                        notify.notify_waiters();
                    }
                    Err(e) => {
                        *cache_guard = ServiceTokenCacheState::Empty;
                        tracing::error!("Failed to get signed token for service (bg task): {}", e);
                        notify.notify_waiters();
                    }
                }
            });
        }
    }

    async fn get_user_entry(
        &self,
        token: &SecretString,
        request_context: &RequestContext,
    ) -> Result<UserEntry, tonic::Status> {
        if let Ok(user) = self.api_token_db.get_user_id(token).await {
            return Ok(user);
        }

        tracing::debug!("Token user info not found in API token database, trying auth central");

        // Get cached service token with AUTH_R scope
        let service_token = self.get_cached_service_token(request_context).await?;
        let request_context: RequestContext = request_context.clone().with_token(service_token);

        let auth_central_response = self
            .auth_central_client
            .get_token_info(token.clone(), &request_context)
            .await;
        match auth_central_response {
            Err(e) if e.code() == tonic::Code::NotFound => {
                tracing::info!("User token info not found in auth central");
                Err(tonic::Status::not_found("User token info not found"))
            }
            Err(e) => {
                tracing::error!("Failed to get token user info: {}", e);
                Err(tonic::Status::internal(
                    "unexpected error getting user token info",
                ))
            }
            Ok(auth_central_response) => {
                tracing::debug!("Got token user info from auth central");
                Ok(UserEntry {
                    user_id: auth_central_response.user_id,
                    opaque_user_id: UserId {
                        user_id_type: UserIdType::Augment.into(),
                        user_id: auth_central_response.augment_user_id,
                    },
                    user_email: Some(auth_central_response.user_email),
                    tenant_id: auth_central_response.tenant_id,
                    // Convert the subscription from auth central response
                    subscription: crate::auth_central_client::convert_subscription(
                        auth_central_response.subscription,
                    ),
                    suspensions: auth_central_response.suspensions,
                    feature_gating_info: auth_central_response.feature_gating_info,
                })
            }
        }
    }
}

#[tonic::async_trait]
impl AuthQuery for AuthQueryImpl {
    /// Get token info
    ///
    /// Request token info from auth central if not in local cache
    ///
    /// Returns the user id and signed token for the given token.
    ///
    /// Currently the tenant id and tenant name is statically configured for the pod (see config.rs)
    /// The information should come dynamically from the tenant watcher.
    ///
    async fn get_token_info(
        &self,
        request: tonic::Request<GetTokenInfoRequest>,
    ) -> Result<tonic::Response<GetTokenInfoResponse>, tonic::Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let token = SecretString::new(request.into_inner().token);

        let user = self.get_user_entry(&token, &request_context).await?;

        if self.config.migrated_tenant_ids.contains(&user.tenant_id) {
            tracing::info!(
                "User is in migrated tenant {}, logging out user",
                user.tenant_id
            );
            return Err(tonic::Status::not_found("migrating tenants"));
        }

        if let Some(tenant) = self.tenant_cache.get_tenant(user.tenant_id.as_str()).await {
            // Disable API tokens for disabled tenants except for the
            // health check (useful for disabled legacy namespaces)
            if !tenant.deleted_at.is_empty() && !user.user_id.starts_with("health-check-") {
                tracing::warn!("Tenant is deleted: {}", user.tenant_id);
                return Err(tonic::Status::not_found("tenant is deleted"));
            }
            if tenant.shard_namespace == self.config.namespace
                || tenant.other_namespace == self.config.namespace
            {
                let signed_token: SecretString = match self
                    .token_exchange
                    .get_signed_token_for_user(
                        user.user_id.clone(),
                        user.opaque_user_id.clone(),
                        user.user_email.clone(),
                        user.tenant_id.clone(),
                        &request_context,
                    )
                    .await
                {
                    Ok(signed_token) => signed_token,
                    Err(e) => {
                        tracing::error!("Failed to get signed token for user: {}", e);
                        return Err(tonic::Status::internal(
                            "failed to get signed token for user",
                        ));
                    }
                };

                // Use migrated_tenant_ids to force logouts for users in a particular namespace
                Ok(tonic::Response::new(GetTokenInfoResponse {
                    user_id: user.user_id,
                    opaque_user_id: Some(user.opaque_user_id),
                    user_email: user.user_email,
                    signed_token: signed_token.expose_secret().to_string(),
                    tenant_id: user.tenant_id.clone(),
                    tenant_name: tenant.name,
                    subscription: user.subscription,
                    suspensions: user.suspensions,
                    feature_gating_info: user.feature_gating_info,
                }))
            } else {
                tracing::error!(
                    "found unexpected tenant: name={} id={} namespace={} other_namespace={}",
                    tenant.name,
                    tenant.id,
                    tenant.shard_namespace,
                    tenant.other_namespace
                );
                Err(tonic::Status::permission_denied(
                    "error finding tenant for authenticated user",
                ))
            }
        } else {
            TENANT_NOT_FOUND_COUNTER.inc();
            tracing::error!("Tenant ID not found: {}", user.tenant_id);
            Err(tonic::Status::internal(
                "tenant not found for authenticated user",
            ))
        }
    }
}

async fn run(args: CliArguments) -> Result<(), Box<dyn std::error::Error>> {
    let config = Config::read(&args.config_file).expect("Failed to read config file");
    tracing::info!("{:?}", config);

    let namespace = match std::env::var("POD_NAMESPACE") {
        Ok(name) => name,
        Err(_) => panic!("POD_NAMESPACE environment variable must be set."),
    };

    // Remove underscores from feature flags stuff when it's actually used
    let _feature_flags = feature_flags::setup(
        "auth_query",
        "0.0.0",
        config.feature_flags_sdk_key_path.as_ref(),
        config.dynamic_feature_flags_endpoint.as_deref(),
    )
    .await;

    let _feature_flag_registry = feature_flags::new_registry();

    let client_tls_config =
        get_client_tls_creds(&config).expect("Failed to create client TLS config");

    let token_exchange: Arc<dyn TokenExchangeClient + Send + Sync> =
        Arc::new(TokenExchangeClientImpl::new(
            &config.token.endpoint,
            namespace.clone(),
            client_tls_config.clone(),
            Duration::from_secs_f32(config.token.request_timeout_secs),
        ));

    let tenant_watcher_client = create_tenant_watcher_client(
        config.tenant_watcher_endpoint.as_str(),
        client_tls_config.clone(),
    );
    let tenant_cache: Arc<dyn TenantCacheClient + Send + Sync> =
        Arc::new(tenant_watcher_client::WatchTenantCache::new(
            tenant_watcher_client.clone(),
            namespace.clone(),
        ));

    let api_token_db = Arc::new(
        crate::api_token_db::ApiTokenDb::from_file(&args.secrets_file, tenant_cache.clone())
            .await?,
    );

    let auth_central_client = Arc::new(AuthCentralClientImpl::new(
        &config.auth_central_endpoint,
        client_tls_config, // yes, this is a central config
        Duration::from_secs_f32(config.token_info_cache_ttl_secs),
        Duration::from_secs_f32(10.0),
    ));

    let auth_query = AuthQueryImpl::new(
        config.clone(),
        api_token_db.clone(),
        token_exchange,
        tenant_cache.clone(),
        auth_central_client,
    );

    let (_health_reporter, health_service) = tonic_health::server::health_reporter();

    metrics_server::setup_default_metrics();

    let metrics_server = metrics_server::setup_metrics_http_server(
        &config.metrics_server_bind_address,
        config.metrics_server_port,
    )?;

    let server_tls_config =
        get_server_tls_creds(&config).expect("Failed to create server TLS config");

    let addr: SocketAddr = config.bind_address.parse()?;

    let server = match server_tls_config {
        None => Server::builder(),
        Some(server_tls_config) => Server::builder()
            .tls_config(server_tls_config)
            .expect("Failed to create rpc server"),
    };

    let tc = tenant_cache.clone();

    let server_future = async move {
        // only start the rpc server once the tenant cache is initialized
        tracing::info!("Waiting for tenant cache to be initialized");
        tc.wait_until_initialized().await.map_err(|e| {
            tracing::error!("Failed to wait for tenant cache to be initialized: {}", e);
            tonic::Status::internal("Failed to wait for tenant cache to be initialized")
        })?;
        tracing::info!("Tenant cache is initialized");

        let listener = tokio::net::TcpListener::bind(addr)
            .await
            .expect("Failed to bind");
        tracing::info!(
            "Listening on {:?}",
            listener.local_addr().expect("Failed to get local address")
        );

        let reflection_service: ServerReflectionServer<_> =
            tonic_reflection::server::Builder::configure()
                .register_encoded_file_descriptor_set(proto::FILE_DESCRIPTOR_SET)
                .build_v1()
                .map_err(|e| {
                    tonic::Status::internal(format!("Failed to build reflection service: {}", e))
                })?;

        let mut sigterm_notifier = signal(SignalKind::terminate()).expect("handle SIGTERM");

        let server = server
            .concurrency_limit_per_connection(config.max_concurrent_streams as usize)
            .max_concurrent_streams(config.max_concurrent_streams)
            .max_connection_age(Duration::from_secs(config.max_connection_age_secs))
            .timeout(Duration::from_secs(300))
            .trace_fn(tracing_tonic::server::trace_fn)
            .layer(
                tower::ServiceBuilder::new()
                    .layer(MetricsMiddlewareLayer::new(
                        &RESPONSE_LATENCY_COLLECTOR,
                        &ACTIVE_REQUESTS_COLLECTOR,
                        &STARTED_REQUESTS_COUNTER,
                        &HANDLED_REQUESTS_COUNTER,
                    ))
                    .into_inner(),
            )
            .add_service(auth_query.new_server())
            .add_service(health_service)
            .add_service(reflection_service)
            .serve_with_incoming_shutdown(
                tokio_stream::wrappers::TcpListenerStream::new(listener),
                async move {
                    sigterm_notifier.recv().await;
                },
            );
        server.await.map_err(|e| {
            tracing::error!("Failed to start rpc server: {}", e);
            tonic::Status::internal("Failed to start rpc server")
        })
    };

    let servers = futures::future::join(server_future, metrics_server);

    select! {
        servers = servers => {
            panic!("servers done: {servers:?}");
        },
        api_token_db = api_token_db.run() => {
            panic!("api_token_db failed: {api_token_db:?}");
        },
        tenant_cache = tenant_cache.run() => {
            panic!("tenant_cache failed: {tenant_cache:?}");
        }
    };
}

fn create_tenant_watcher_client(
    tenant_watcher_endpoint: &str,
    client_tls_config: Option<ClientTlsConfig>,
) -> Arc<dyn TenantWatcherClient + Send + Sync> {
    tracing::info!("Creating tenant watcher client: endpoint={tenant_watcher_endpoint}",);
    Arc::new(tenant_watcher_client::TenantWatcherClientImpl::new(
        tenant_watcher_endpoint,
        client_tls_config,
        Duration::from_secs(120),
    ))
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    setup_struct_logging().expect("Failed to setup logging");

    let args = CliArguments::parse();
    tracing::info!("{:?}", args);

    run(args).await
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use std::sync::atomic::{AtomicUsize, Ordering};
    use std::sync::Arc;
    use std::time::Duration;

    // ---------- Test doubles ----------

    struct DummyTenantCache;

    #[async_trait]
    impl tenant_watcher_client::TenantCacheClient for DummyTenantCache {
        async fn get_tenant(
            &self,
            _tenant_id: &str,
        ) -> Option<tenant_watcher_client::tenant_watcher::Tenant> {
            None
        }
        async fn get_tenant_by_name(
            &self,
            _tenant_name: &str,
        ) -> Option<tenant_watcher_client::tenant_watcher::Tenant> {
            None
        }
        async fn run(&self) -> Result<(), tonic::Status> {
            Ok(())
        }
        async fn wait_until_initialized(&self) -> Result<(), tonic::Status> {
            Ok(())
        }
        async fn wait_until_initialized_with_timeout(
            &self,
            _timeout: Duration,
        ) -> Result<(), tonic::Status> {
            Ok(())
        }
    }

    struct DummyAuthCentralClient;

    #[async_trait]
    impl AuthCentralClient for DummyAuthCentralClient {
        async fn get_token_info(
            &self,
            _token: SecretString,
            _request_context: &RequestContext,
        ) -> tonic::Result<proto::auth_central::GetTokenInfoResponse> {
            Err(tonic::Status::not_found("not used in tests"))
        }
    }

    #[derive(Clone)]
    struct FakeTokenExchangeClient {
        token: SecretString,
        calls: Arc<AtomicUsize>,
    }

    #[async_trait]
    impl TokenExchangeClient for FakeTokenExchangeClient {
        async fn get_signed_token_for_user(
            &self,
            _user_id: String,
            _opaque_user_id: auth_entities_proto::auth_entities::UserId,
            _user_email: Option<String>,
            _tenant_id: String,
            _request_context: &RequestContext,
        ) -> Result<SecretString, tonic::Status> {
            Err(tonic::Status::unimplemented("not used in tests"))
        }

        async fn get_signed_token_for_service(
            &self,
            _tenant_id: String,
            _scopes: &[token_exchange_client::token_scopes::Scope],
            _request_context: &RequestContext,
        ) -> Result<SecretString, tonic::Status> {
            self.calls.fetch_add(1, Ordering::SeqCst);
            Ok(self.token.clone())
        }
        async fn get_verification_key(&self) -> Result<Vec<u8>, tonic::Status> {
            Ok(vec![])
        }
    }

    fn make_config(ttl_secs: f32) -> Config {
        Config {
            bind_address: "127.0.0.1:0".to_string(),
            namespace: "test-ns".to_string(),
            feature_flags_sdk_key_path: None,
            dynamic_feature_flags_endpoint: None,
            metrics_server_bind_address: "127.0.0.1".to_string(),
            metrics_server_port: 0,
            client_mtls: false,
            client_ca_path: "".to_string(),
            client_key_path: "".to_string(),
            client_cert_path: "".to_string(),
            server_mtls: false,
            server_ca_path: "".to_string(),
            server_key_path: "".to_string(),
            server_cert_path: "".to_string(),
            token: crate::config::TokenConfig {
                endpoint: "".to_string(),
                request_timeout_secs: 1.0,
            },
            tenant_watcher_endpoint: "".to_string(),
            auth_central_endpoint: "".to_string(),

            token_info_cache_ttl_secs: 60.0,
            service_token_cache_ttl_secs: ttl_secs,
            migrated_tenant_ids: vec![],
            max_concurrent_streams: 128,
            max_connection_age_secs: 300,
        }
    }

    fn make_auth_query(ttl_secs: f32, fake: FakeTokenExchangeClient) -> AuthQueryImpl {
        let config = make_config(ttl_secs);
        let api_token_db = Arc::new(api_token_db::ApiTokenDb::new(
            &[],
            Arc::new(DummyTenantCache),
        ));
        let tenant_cache: Arc<dyn tenant_watcher_client::TenantCacheClient + Send + Sync> =
            Arc::new(DummyTenantCache);
        let auth_central_client: Arc<dyn AuthCentralClient + Send + Sync> =
            Arc::new(DummyAuthCentralClient);
        AuthQueryImpl::new(
            config,
            api_token_db,
            Arc::new(fake),
            tenant_cache,
            auth_central_client,
        )
    }
    #[derive(Clone)]
    struct FakeClock {
        base: Instant,
        offset: Arc<std::sync::Mutex<Duration>>,
    }

    impl FakeClock {
        fn new() -> Self {
            Self {
                base: Instant::now(),
                offset: Arc::new(std::sync::Mutex::new(Duration::from_secs(0))),
            }
        }
        fn advance(&self, d: Duration) {
            let mut off = self.offset.lock().unwrap();
            *off += d;
        }
    }

    impl Clock for FakeClock {
        fn now(&self) -> Instant {
            let off = self.offset.lock().unwrap();
            self.base + *off
        }
    }

    #[tokio::test]
    async fn test_service_token_cache_concurrent_fetches_only_once() {
        let calls = Arc::new(AtomicUsize::new(0));
        let fake = FakeTokenExchangeClient {
            token: SecretString::new("svc_token".to_string()),
            calls: calls.clone(),
        };
        let auth_query = make_auth_query(100.0, fake);
        let rc = RequestContext::new_for_test();

        // Issue several concurrent requests in the same task
        let futures = (0..10)
            .map(|_| auth_query.get_cached_service_token(&rc))
            .collect::<Vec<_>>();
        let results = futures::future::join_all(futures).await;

        // All should succeed and equal the same token
        for token in results {
            let token = token.unwrap();
            assert_eq!(token.expose_secret(), "svc_token");
        }
        // Only one underlying fetch should have occurred
        assert_eq!(calls.load(Ordering::SeqCst), 1);
    }

    #[tokio::test]
    async fn test_service_token_cache_expiration_triggers_new_fetch() {
        let calls = Arc::new(AtomicUsize::new(0));
        let fake = FakeTokenExchangeClient {
            token: SecretString::new("svc_token".to_string()),
            calls: calls.clone(),
        };
        // Use non-zero TTL and advance fake clock instead of TTL 0
        let mut auth_query = make_auth_query(100.0, fake);
        let rc = RequestContext::new_for_test();

        let clock = Arc::new(FakeClock::new());
        auth_query = auth_query.with_clock(clock.clone());

        let t1 = auth_query.get_cached_service_token(&rc).await.unwrap();
        assert_eq!(t1.expose_secret(), "svc_token");
        assert_eq!(calls.load(Ordering::SeqCst), 1);

        // Still cached
        let t2 = auth_query.get_cached_service_token(&rc).await.unwrap();
        assert_eq!(t2.expose_secret(), "svc_token");
        assert_eq!(calls.load(Ordering::SeqCst), 1);

        // Advance time to expire the cached token
        clock.advance(Duration::from_secs(101));

        // Should fetch new token
        let t2 = auth_query.get_cached_service_token(&rc).await.unwrap();
        assert_eq!(t2.expose_secret(), "svc_token");
        assert_eq!(calls.load(Ordering::SeqCst), 2);
    }
}
