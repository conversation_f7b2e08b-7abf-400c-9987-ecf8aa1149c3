package main

import (
	"context"
	"fmt"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authcentralclient "github.com/augmentcode/augment/services/auth/central/client"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	chathostclient "github.com/augmentcode/augment/services/chat_host/client/go"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	triggerentitiesproto "github.com/augmentcode/augment/services/remote_agent_actions/server/trigger_entities_proto"
	remoteagentsclient "github.com/augmentcode/augment/services/remote_agents/client"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// ScheduleTriggerConfig contains dependencies for the schedule trigger task
type ScheduleTriggerConfig struct {
	BigtableClient      bigtableproxy.BigtableProxyClient
	RemoteAgentsClient  remoteagentsclient.RemoteAgentsClient
	SchedulerStorage    *SchedulerStorage
	CronEvaluator       *CronEvaluator
	Server              *RemoteAgentActionsServer
	TokenExchangeClient tokenexchangeclient.TokenExchangeClient
	AuthCentralClient   authcentralclient.AuthClient
	TenantCache         tenantwatcherclient.TenantCache
	FeatureFlagHandle   featureflags.FeatureFlagHandle
	Namespace           string
	ChatHostClient      *chathostclient.ChatHostClient
}

// TriggerExecutionInfo holds information about a trigger that needs to be executed
type TriggerExecutionInfo struct {
	TriggerEntity    *triggerentitiesproto.TriggerEntity
	ScheduledTrigger *triggerentitiesproto.ScheduledTrigger
	Context          context.Context
	ScheduledTime    time.Time
	IsCatchUp        bool
}

// NewScheduleTriggerCronTask creates a new schedule trigger cron task function
func NewScheduleTriggerCronTask(config *ScheduleTriggerConfig) CronTaskFunc {
	return func(ctx context.Context) {
		executeScheduledTriggers(ctx, config)
	}
}

// getServiceRequestContext creates a service request context for background operations
func getServiceRequestContext(ctx context.Context, config *ScheduleTriggerConfig, tenantID string) (*requestcontext.RequestContext, error) {
	// Scopes needed for scheduler operations (BigTable access and trigger execution)
	scopes := []tokenscopesproto.Scope{
		tokenscopesproto.Scope_CONTENT_RW,
		tokenscopesproto.Scope_SETTINGS_RW,
	}

	serviceToken, err := config.TokenExchangeClient.GetSignedTokenForService(ctx, tenantID, scopes)
	if err != nil {
		return nil, err
	}

	return requestcontext.New(
		requestcontext.NewRandomRequestId(),
		requestcontext.NewRandomRequestSessionId(),
		"schedule-trigger-task",
		serviceToken,
	), nil
}

// executeScheduledTriggers is the main function that processes all scheduled triggers
// It discovers tenants in this shard namespace and processes each tenant's scheduler state separately
func executeScheduledTriggers(ctx context.Context, config *ScheduleTriggerConfig) {
	startTime := time.Now()
	ctx = log.Ctx(ctx).With().Time("task_start_time", startTime).Logger().WithContext(ctx)

	log.Ctx(ctx).Info().Msg("ScheduleTriggerTask: Starting scheduled trigger execution")

	// Get all tenants in this shard namespace
	tenants, err := config.TenantCache.GetAllTenants()
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get tenants from cache")
		return
	}
	if len(tenants) == 0 {
		log.Ctx(ctx).Debug().Msg("No tenants found in this shard namespace")
		return
	}

	log.Ctx(ctx).Info().
		Int("tenant_count", len(tenants)).
		Msg("Processing scheduled triggers for all tenants in shard")

	totalExecutedCount := 0
	totalTriggersCount := 0

	// Process each tenant's scheduler state separately
	for _, tenant := range tenants {
		tenantCtx := log.Ctx(ctx).With().
			Str("tenant_id", tenant.Id).
			Str("tenant_name", tenant.Name).
			Logger().WithContext(ctx)

		log.Ctx(tenantCtx).Debug().Msg("Processing tenant for scheduled triggers")
		executedCount, triggersCount := processTenantsScheduledTriggers(tenantCtx, config, tenant.Id)
		log.Ctx(tenantCtx).Debug().Int("executed_count", executedCount).Int("triggers_count", triggersCount).Msg("Completed processing tenant")
		totalExecutedCount += executedCount
		totalTriggersCount += triggersCount
	}

	duration := time.Since(startTime)
	log.Ctx(ctx).Info().
		Int("executed_triggers", totalExecutedCount).
		Int("total_triggers", totalTriggersCount).
		Int("processed_tenants", len(tenants)).
		Dur("duration", duration).
		Msg("ScheduleTriggerTask: Completed scheduled trigger execution for all tenants")
}

// processTenantsScheduledTriggers processes scheduled triggers for a single tenant
func processTenantsScheduledTriggers(ctx context.Context, config *ScheduleTriggerConfig, tenantID string) (executedCount, totalTriggers int) {
	// Create service request context for this tenant
	tenantServiceRequestContext, err := getServiceRequestContext(ctx, config, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to create service request context for tenant")
		return 0, 0
	}

	// Get all schedule triggers for the tenant directly from the triggers table
	scheduleTriggersEntities, err := getScheduleTriggersForTenant(ctx, tenantServiceRequestContext, config.BigtableClient, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to get schedule triggers for tenant")
		return 0, 0
	}

	if len(scheduleTriggersEntities) == 0 {
		log.Ctx(ctx).Debug().Str("tenant_id", tenantID).Msg("No scheduled triggers found for tenant")
		return 0, 0
	}

	// Get current scheduler state
	schedulerState, err := config.SchedulerStorage.GetSchedulerState(ctx, tenantServiceRequestContext, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to get scheduler state for tenant")
		return 0, 0
	}

	now := time.Now()
	tolerance := 30 * time.Second // Normal tolerance - extended 15-minute grace period is handled in IsTimeToExecute
	executedCount = 0

	log.Ctx(ctx).Info().
		Str("tenant_id", tenantID).
		Int("scheduled_triggers", len(scheduleTriggersEntities)).
		Time("current_time", now).
		Msg("Processing scheduled triggers for tenant")

	// Create a map of existing scheduled triggers for quick lookup
	schedulerStateMap := make(map[string]*triggerentitiesproto.ScheduledTrigger)
	for _, scheduledTrigger := range schedulerState.ScheduledTriggers {
		schedulerStateMap[scheduledTrigger.TriggerId] = scheduledTrigger
	}

	// PHASE 1: COLLECT - Identify triggers that need execution
	var triggersToExecute []TriggerExecutionInfo
	var updatedScheduledTriggers []*triggerentitiesproto.ScheduledTrigger
	stateChanged := false

	// Process each schedule trigger from the triggers table to collect execution candidates
	for _, triggerEntity := range scheduleTriggersEntities {
		scheduleConditions := triggerEntity.Configuration.Conditions.Schedule
		if scheduleConditions == nil {
			continue
		}
		// Check if this trigger exists in scheduler state
		scheduledTrigger, existsInState := schedulerStateMap[triggerEntity.TriggerId]

		if !existsInState {
			// New trigger - calculate initial next execution time
			timezone := ""
			if scheduleConditions.Timezone != nil {
				timezone = *scheduleConditions.Timezone
			}
			nextExecution, err := config.CronEvaluator.GetNextExecutionWithBounds(
				scheduleConditions.CronExpression,
				timezone,
				now,
				timeFromTimestamp(scheduleConditions.StartDate),
				timeFromTimestamp(scheduleConditions.EndDate),
			)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).
					Str("tenant_id", tenantID).
					Str("trigger_id", triggerEntity.TriggerId).
					Str("cron_expression", scheduleConditions.CronExpression).
					Msg("Failed to calculate next execution time for new scheduled trigger")
				continue
			}

			// If no next execution (beyond end date), skip
			if nextExecution.IsZero() {
				continue
			}

			// Create new scheduled trigger entry
			scheduledTrigger = &triggerentitiesproto.ScheduledTrigger{
				TriggerId:      triggerEntity.TriggerId,
				UserId:         triggerEntity.UserId,
				NextExecution:  timestamppb.New(nextExecution),
				CronExpression: scheduleConditions.CronExpression,
				Timezone:       scheduleConditions.Timezone,
				StartDate:      scheduleConditions.StartDate,
				EndDate:        scheduleConditions.EndDate,
			}
			stateChanged = true

			log.Ctx(ctx).Info().
				Str("tenant_id", tenantID).
				Str("trigger_id", triggerEntity.TriggerId).
				Time("next_execution", nextExecution).
				Msg("Added new trigger to scheduler state")
		}

		// Check if it's time to execute this trigger (with 15-minute grace period)
		if config.CronEvaluator.IsTimeToExecute(scheduledTrigger.NextExecution.AsTime(), now, tolerance) {
			triggerCtx := log.Ctx(ctx).With().
				Str("tenant_id", tenantID).
				Str("trigger_id", triggerEntity.TriggerId).
				Str("user_id", triggerEntity.UserId).
				Time("scheduled_time", scheduledTrigger.NextExecution.AsTime()).
				Logger().WithContext(ctx)

			// Determine if this is a catch-up execution (overdue by more than normal tolerance)
			timeDiff := now.Sub(scheduledTrigger.NextExecution.AsTime())
			normalTolerance := 30 * time.Second
			isCatchUp := timeDiff > normalTolerance

			// Add to execution list
			triggersToExecute = append(triggersToExecute, TriggerExecutionInfo{
				TriggerEntity:    triggerEntity,
				ScheduledTrigger: scheduledTrigger,
				Context:          triggerCtx,
				ScheduledTime:    scheduledTrigger.NextExecution.AsTime(),
				IsCatchUp:        isCatchUp,
			})
		}

		// Add to updated list (all triggers, whether executed or not)
		updatedScheduledTriggers = append(updatedScheduledTriggers, scheduledTrigger)
	}

	// PHASE 2: EXECUTE - Run all collected triggers
	log.Ctx(ctx).Info().
		Str("tenant_id", tenantID).
		Int("triggers_to_execute", len(triggersToExecute)).
		Msg("Executing collected scheduled triggers")

	for _, triggerInfo := range triggersToExecute {
		if triggerInfo.IsCatchUp {
			log.Ctx(triggerInfo.Context).Warn().
				Dur("overdue_by", now.Sub(triggerInfo.ScheduledTime)).
				Msg("Executing overdue scheduled trigger (catch-up execution)")
		} else {
			log.Ctx(triggerInfo.Context).Info().Msg("Executing scheduled trigger")
		}

		// Execute the trigger
		if err := executeTriggerEntity(triggerInfo.Context, config, tenantID, triggerInfo.TriggerEntity); err != nil {
			log.Ctx(triggerInfo.Context).Error().Err(err).Msg("Failed to execute scheduled trigger")
		} else {
			executedCount++
		}
	}

	// PHASE 3: UPDATE - Update scheduler state for all executed triggers
	for _, triggerInfo := range triggersToExecute {
		// Find the scheduled trigger in our updated list to modify
		var targetScheduledTrigger *triggerentitiesproto.ScheduledTrigger
		for _, st := range updatedScheduledTriggers {
			if st.TriggerId == triggerInfo.ScheduledTrigger.TriggerId {
				targetScheduledTrigger = st
				break
			}
		}

		if targetScheduledTrigger == nil {
			log.Ctx(triggerInfo.Context).Error().Msg("Could not find scheduled trigger to update")
			continue
		}

		// Get schedule conditions for next execution calculation
		scheduleConditions := triggerInfo.TriggerEntity.Configuration.Conditions.Schedule
		if scheduleConditions == nil {
			continue
		}

		// Calculate next execution time (regardless of whether execution succeeded or failed)
		timezone := ""
		if scheduleConditions.Timezone != nil {
			timezone = *scheduleConditions.Timezone
		}
		nextExecution, err := config.CronEvaluator.GetNextExecutionWithBounds(
			scheduleConditions.CronExpression,
			timezone,
			now,
			timeFromTimestamp(scheduleConditions.StartDate),
			timeFromTimestamp(scheduleConditions.EndDate),
		)
		if err != nil {
			log.Ctx(triggerInfo.Context).Error().Err(err).Msg("Failed to calculate next execution time after execution")
			continue
		}

		// If no next execution (beyond end date), remove from scheduler
		if nextExecution.IsZero() {
			log.Ctx(triggerInfo.Context).Info().Msg("Trigger has reached end date, removing from scheduler")
			// Remove from updatedScheduledTriggers
			for i, st := range updatedScheduledTriggers {
				if st.TriggerId == triggerInfo.ScheduledTrigger.TriggerId {
					updatedScheduledTriggers = append(updatedScheduledTriggers[:i], updatedScheduledTriggers[i+1:]...)
					break
				}
			}
			stateChanged = true
			continue
		}

		// Update next execution time and last execution time
		targetScheduledTrigger.NextExecution = timestamppb.New(nextExecution)
		targetScheduledTrigger.LastExecution = timestamppb.New(now)
		stateChanged = true

		log.Ctx(triggerInfo.Context).Info().
			Time("next_execution", nextExecution).
			Msg("Updated next execution time for trigger")
	}

	// Save updated scheduler state if there were changes
	if stateChanged {
		schedulerState.ScheduledTriggers = updatedScheduledTriggers
		if err := config.SchedulerStorage.SaveSchedulerState(ctx, tenantServiceRequestContext, tenantID, schedulerState); err != nil {
			log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to save updated scheduler state")
		} else {
			log.Ctx(ctx).Debug().
				Str("tenant_id", tenantID).
				Int("updated_triggers", len(updatedScheduledTriggers)).
				Msg("Updated scheduler state for tenant")
		}
	}

	return executedCount, len(scheduleTriggersEntities)
}

// executeTriggerEntity executes a single scheduled trigger from a trigger entity
func executeTriggerEntity(ctx context.Context, config *ScheduleTriggerConfig, tenantID string, triggerEntity *triggerentitiesproto.TriggerEntity) error {
	// Create a service request context for this tenant
	serviceRequestContext, err := getServiceRequestContext(ctx, config, tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).
			Str("tenant_id", tenantID).
			Str("trigger_id", triggerEntity.TriggerId).
			Msg("Failed to create service request context for trigger execution")
		return err
	}

	// Get the full trigger configuration from storage
	trigger, err := getTrigger(ctx, serviceRequestContext, config.BigtableClient, tenantID, triggerEntity.UserId, triggerEntity.TriggerId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).
			Str("trigger_id", triggerEntity.TriggerId).
			Msg("Failed to get trigger configuration")
		return fmt.Errorf("failed to get trigger configuration: %w", err)
	}

	// Create a synthetic entity ID for schedule triggers (since they don't have a real entity)
	// Use human-readable timestamp format: scheduled-execution-2025-07-06T23-47-22Z
	now := time.Now()
	timestamp := now.UTC().Format("2006-01-02T15-04-05Z")
	entityID := fmt.Sprintf("scheduled-execution-%s", timestamp)

	// Execute the scheduled trigger - executeTriggermanually will automatically detect
	// this is a scheduled execution from the entityID and generate appropriate tokens
	executionID, agentID, err := executeTriggermanually(
		ctx,
		serviceRequestContext,
		config.BigtableClient,
		config.RemoteAgentsClient,
		nil, // githubProcessorClient - not needed for scheduled triggers
		config.TokenExchangeClient,
		config.AuthCentralClient,
		tenantID,
		triggerEntity.UserId, // trigger owner's user ID
		trigger,
		entityID,
		"schedule", // entityType for schedule triggers
		nil,        // overrideAgentConfig
		"",         // extraPrompt
		config.FeatureFlagHandle,
		config.ChatHostClient,
		nil, // linearIntegration not needed for schedule triggers
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).
			Str("trigger_id", triggerEntity.TriggerId).
			Msg("Failed to execute scheduled trigger")
		return fmt.Errorf("failed to execute scheduled trigger: %w", err)
	}

	log.Ctx(ctx).Info().
		Str("trigger_id", triggerEntity.TriggerId).
		Str("execution_id", executionID).
		Str("agent_id", func() string {
			if agentID != nil {
				return *agentID
			}
			return ""
		}()).
		Msg("Successfully executed scheduled trigger")

	return nil
}

// timeFromTimestamp converts a protobuf timestamp to a time pointer
func timeFromTimestamp(ts *timestamppb.Timestamp) *time.Time {
	if ts == nil {
		return nil
	}
	t := ts.AsTime()
	return &t
}
