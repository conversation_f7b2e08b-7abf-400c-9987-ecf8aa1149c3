package useridresolver

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/rs/zerolog/log"

	authclient "github.com/augmentcode/augment/services/auth/central/client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
)

// Config holds configuration for the UserIDResolver
type Config struct {
	// UserResolutionCacheDuration specifies how long to cache successful user resolution results
	// Default: 1 minute
	UserResolutionCacheDuration time.Duration
}

// DefaultConfig returns a Config with default values
func DefaultConfig() *Config {
	return &Config{
		UserResolutionCacheDuration: time.Minute,
	}
}

// cacheEntry represents a cached user resolution result
type cacheEntry struct {
	resolution *UserResolution
	expiresAt  time.Time
}

// UserIDResolver provides functionality to resolve opaque user IDs to tenant information
type UserIDResolver interface {
	// ResolveUser takes an opaque user ID and returns the corresponding tenant ID and namespace
	ResolveUser(ctx context.Context, opaqueUserID string, requestContext *requestcontext.RequestContext) (*UserResolution, error)

	// Close releases resources associated with the resolver
	Close()
}

// UserResolution contains the resolved user information
type UserResolution struct {
	UserID         string
	TenantID       string
	ShardNamespace string
	User           *auth_entities.User
	Tenant         *tenantproto.Tenant
}

type userIDResolverImpl struct {
	authClient  authclient.AuthClient
	tenantCache tenantwatcherclient.TenantCacheSync

	// Configuration
	config *Config

	// User resolution cache
	cacheMutex      sync.RWMutex
	resolutionCache map[string]*cacheEntry

	// Background cleanup
	stopCleanup chan struct{}
	cleanupDone chan struct{}
}

// NewUserIDResolver creates a new UserIDResolver with the given configuration and gRPC clients
func NewUserIDResolver(config *Config, authClient authclient.AuthClient, tenantCache tenantwatcherclient.TenantCacheSync) UserIDResolver {
	if config == nil {
		config = DefaultConfig()
	}

	resolver := &userIDResolverImpl{
		authClient:      authClient,
		tenantCache:     tenantCache,
		config:          config,
		resolutionCache: make(map[string]*cacheEntry),
		stopCleanup:     make(chan struct{}),
		cleanupDone:     make(chan struct{}),
	}

	// Start background cleanup goroutine
	go resolver.cleanupExpiredEntries()

	return resolver
}

// cleanupExpiredEntries runs in the background to remove expired cache entries
func (r *userIDResolverImpl) cleanupExpiredEntries() {
	defer close(r.cleanupDone)

	// Run cleanup every 30 seconds
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-r.stopCleanup:
			return
		case <-ticker.C:
			r.cacheMutex.Lock()
			now := time.Now()
			expiredKeys := make([]string, 0)

			// Find expired entries
			for key, entry := range r.resolutionCache {
				if now.After(entry.expiresAt) {
					expiredKeys = append(expiredKeys, key)
				}
			}

			// Remove expired entries
			for _, key := range expiredKeys {
				delete(r.resolutionCache, key)
			}

			if len(expiredKeys) > 0 {
				log.Debug().Int("expired_count", len(expiredKeys)).Msg("Cleaned up expired user resolution cache entries")
			}

			r.cacheMutex.Unlock()
		}
	}
}

// ResolveUser resolves an opaque user ID to tenant information
func (r *userIDResolverImpl) ResolveUser(ctx context.Context, opaqueUserID string, requestContext *requestcontext.RequestContext) (*UserResolution, error) {
	if requestContext == nil {
		log.Error().Msg("Request context not found in context")
		return nil, fmt.Errorf("request context not found in context")
	}

	// Check cache first
	r.cacheMutex.RLock()
	if entry, exists := r.resolutionCache[opaqueUserID]; exists {
		if time.Now().Before(entry.expiresAt) {
			// Cache hit - return cached result
			resolution := entry.resolution
			r.cacheMutex.RUnlock()
			log.Debug().Ctx(ctx).Str("user_id", opaqueUserID).Msg("Returning cached user resolution")
			return resolution, nil
		}
		// Entry expired, will be cleaned up by background goroutine
	}
	r.cacheMutex.RUnlock()

	// Cache miss or expired - resolve user
	log.Debug().Ctx(ctx).Str("user_id", opaqueUserID).Msg("Resolving user from auth central (cache miss)")

	user, err := r.authClient.GetUser(ctx, requestContext, opaqueUserID, nil)
	if err != nil {
		log.Error().Ctx(ctx).Err(err).Str("user_id", opaqueUserID).Msg("Failed to get user from auth central")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("user not found: %s", opaqueUserID)
	}
	log.Info().Ctx(ctx).Str("user_id", opaqueUserID).Msg("Found user in auth central")

	// Get tenant from tenant cache
	tenant, err := r.tenantCache.GetTenant(ctx, user.Tenants[0])
	if err != nil {
		log.Error().Ctx(ctx).Err(err).Str("tenant_id", user.Tenants[0]).Msg("Failed to get tenant from cache")
		return nil, fmt.Errorf("failed to get tenant: %w", err)
	}
	if tenant == nil {
		return nil, fmt.Errorf("tenant not found: %s", user.Tenants[0])
	}

	// Create resolution result
	resolution := &UserResolution{
		UserID:         user.Id,
		TenantID:       tenant.Id,
		ShardNamespace: tenant.ShardNamespace,
		User:           user,
		Tenant:         tenant,
	}

	// Cache the successful resolution
	r.cacheMutex.Lock()
	r.resolutionCache[opaqueUserID] = &cacheEntry{
		resolution: resolution,
		expiresAt:  time.Now().Add(r.config.UserResolutionCacheDuration),
	}
	r.cacheMutex.Unlock()

	log.Debug().Ctx(ctx).
		Str("user_id", opaqueUserID).
		Str("tenant_id", resolution.TenantID).
		Str("namespace", resolution.ShardNamespace).
		Dur("cache_duration", r.config.UserResolutionCacheDuration).
		Msg("Cached fresh user resolution")

	return resolution, nil
}

// Close releases resources associated with the resolver
func (r *userIDResolverImpl) Close() {
	// Stop background cleanup goroutine
	close(r.stopCleanup)
	<-r.cleanupDone

	// Clear cache
	r.cacheMutex.Lock()
	r.resolutionCache = nil
	r.cacheMutex.Unlock()

	// Close clients
	if r.authClient != nil {
		r.authClient.Close()
	}

	log.Debug().Msg("UserIDResolver closed and resources cleaned up")
}
