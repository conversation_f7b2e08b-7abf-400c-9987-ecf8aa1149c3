"""Test signature chunker."""

from textwrap import dedent

import pytest

from models.retrieval.chunking import chunking, signature_chunker


def test_signature_chunker():
    """Tests the basic chunking behavior."""
    text = dedent(
        """
        import itertools
        import abc

        class AClass:
            def __init__(self):
                pass

            def function(foo, bar):
                print(foo)
                print(bar)
                print('another)
                return None

        def foo():
            pass

        def bar():
            pass

        a_var = 1
    """
    ).strip()
    doc = chunking.Document(blob_name="1", text=text, path="foo.py")
    chunker = signature_chunker.RobustSignatureChunker(show_private_members=False)
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 7
    expected = [
        dedent(
            """
            In file: foo.py
            CLASS: AClass
            FUNCTION: foo, bar
            VARIABLE: a_var
            """
        ).strip(),
        dedent(
            """
            from: foo.py
            class AClass:
            methods: function
                def __init__(self): ...
            """
        ).strip(),
        dedent(
            """
            from: foo.py/AClass
                def __init__(self):
            """
        ).strip(),
        dedent(
            """
            from: foo.py/AClass
                def function(foo, bar):
                    return None
            """
        ).strip(),
        dedent(
            """
            from: foo.py
            def foo():
            """
        ).strip(),
        dedent(
            """
            from: foo.py
            def bar():
            """
        ).strip(),
        dedent(
            """
            from: foo.py
            a_var = 1
            """
        ).strip(),
    ]
    for value, expected_value in zip(chunks, expected):
        assert value.text == expected_value


@pytest.mark.parametrize(
    "text, path",
    [
        ("Hello world", "foo.README"),
    ],
)
def test_signature_chunker_on_invalid_file(text, path):
    """Tests the behavior on an invalid file."""
    doc = chunking.Document(blob_name="a blob name", text=text, path=path)
    chunker = signature_chunker.RobustSignatureChunker(show_private_members=False)
    assert not list(chunker.split_into_chunks(doc))


def test_signature_chunker_on_ts_file():
    """Tests the behavior on another example."""
    doc = chunking.Document(
        blob_name="a bad blob",
        text="""\
function f() {
  return (
        <A
          a-a={x ? '' : 42}
          b={() => c()}
          c={() => c()}
        />
  );
}
""",
        path="test.ts",
    )
    chunker = signature_chunker.RobustSignatureChunker(show_private_members=False)
    chunks = list(chunker.split_into_chunks(doc))

    assert len(chunks) == 2
    expected = [
        dedent(
            """
            In file: test.ts
            FUNCTION: f
            """
        ).strip(),
        dedent(
            """
            from: test.ts
            function f() {
            }
            """
        ).strip(),
    ]
    for value, expected_value in zip(chunks, expected):
        assert value.text == expected_value
