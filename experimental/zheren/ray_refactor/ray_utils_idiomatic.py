"""
Idiomatic Ray Data pipeline implementation that keeps data in memory
and passes Dataset objects between stages.
"""

import ray
from typing import Any, Generic, TypeVar, Type
from abc import ABC, abstractmethod
from dataclasses_json import DataClassJsonMixin
from termcolor import colored

Input = TypeVar("Input", bound=DataClassJsonMixin)
Output = TypeVar("Output", bound=DataClassJsonMixin)


class AbstractRayActor(ABC, Generic[Input, Output]):
    """
    An actor that processes data and returns results.
    Each input row is specified as a dataclass instance of type Input,
    and each output row is specified as a dataclass instance of type Output.
    """

    def __init__(self, input_cls: type[Input], output_cls: type[Output]):
        self.input_cls = input_cls
        self.output_cls = output_cls

    @abstractmethod
    def process(self, row: Input) -> list[Output]:
        """Process a single instance of data. May return multiple instances."""
        raise NotImplementedError()

    def process_batch(self, batch: dict[str, Any]) -> dict[str, Any]:
        """Process a batch of rows from Ray Dataset."""
        # Input batch has columns as keys, values are lists
        results = []

        # Assuming the batch has a single column with serialized objects
        for item in batch["item"]:
            if isinstance(item, str):
                # If coming from text file
                obj = self.input_cls.from_json(item)
            elif isinstance(item, dict):
                # If coming from previous stage
                obj = self.input_cls.from_dict(item)
            else:
                obj = item

            outputs = self.process(obj)
            results.extend([output.to_dict() for output in outputs])

        return {"item": results}


class IdiomaticRayPipeline:
    """
    A pipeline that chains multiple Ray actors together without intermediate file I/O.
    """

    def __init__(self, num_workers: int = 4, local: bool = False):
        self.num_workers = num_workers
        self.local = local
        self.stages = []

    def add_stage(
        self,
        actor_cls: Type[AbstractRayActor],
        actor_args: dict[str, Any],
        num_cpus: int = 1,
        num_gpus: int = 0,
        stage_name: str = None,
    ):
        """Add a processing stage to the pipeline."""
        stage_name = stage_name or f"Stage_{len(self.stages) + 1}"
        self.stages.append(
            {
                "name": stage_name,
                "actor_cls": actor_cls,
                "actor_args": actor_args,
                "num_cpus": num_cpus,
                "num_gpus": num_gpus,
            }
        )
        return self

    def run(self, input_data: ray.data.Dataset) -> ray.data.Dataset:
        """
        Run the entire pipeline, passing Dataset objects between stages.
        """
        current_data = input_data

        for stage in self.stages:
            print(f"\nRunning {colored(stage['name'], 'green')}")
            print(f"Actor: {colored(stage['actor_cls'].__name__, 'light_blue')}")

            if self.local:
                # Local execution for debugging
                actor = stage["actor_cls"](**stage["actor_args"])
                current_data = current_data.map_batches(
                    actor.process_batch,
                    batch_size=100,
                )
            else:
                # Distributed execution
                current_data = current_data.map_batches(
                    stage["actor_cls"],
                    fn_constructor_kwargs=stage["actor_args"],
                    batch_size=100,
                    num_cpus=stage["num_cpus"],
                    num_gpus=stage["num_gpus"],
                    concurrency=self.num_workers,
                )

            # Optional: Add progress tracking
            print(f"Completed {stage['name']}")

        return current_data


# # Example usage showing the idiomatic pattern
# def example_pipeline():
#     """
#     Example of how to use the idiomatic pipeline.
#     """
#     # Initialize Ray
#     ray.init()

#     # Create pipeline
#     pipeline = IdiomaticRayPipeline(num_workers=4)

#     # Add stages - no intermediate files!
#     pipeline.add_stage(
#         actor_cls=Stage1Actor,
#         actor_args={"config": "value1"},
#         stage_name="BigQuery Fetch",
#     ).add_stage(
#         actor_cls=Stage2Actor,
#         actor_args={"threshold": 0.5},
#         stage_name="Filter Requests",
#     ).add_stage(
#         actor_cls=Stage3Actor,
#         actor_args={"model": "gemini"},
#         stage_name="Classify Queries",
#     )

#     # Read initial data
#     input_data = ray.data.read_text("input/*.jsonl")

#     # Run pipeline - data stays in memory!
#     output_data = pipeline.run(input_data)

#     # Only write final results to disk
#     output_data.write_json("output/final_results")

#     # Or iterate over results without writing
#     for batch in output_data.iter_batches(batch_size=1000):
#         process_final_batch(batch)


# # Alternative: Using Ray Data's native operations
# def native_ray_data_pipeline():
#     """
#     Even more idiomatic: use Ray Data's native operations when possible.
#     """
#     import pyarrow.compute as pc

#     # Read data
#     ds = ray.data.read_json("input/*.jsonl")

#     # Chain operations - all in memory!
#     result = (
#         ds.filter(lambda row: row["score"] > 0.5)  # Native filter
#         .map_batches(preprocess_batch, batch_size=1000)  # Custom transform
#         .groupby("category")
#         .map_groups(aggregate_group)  # Group operations
#         .sort("timestamp")  # Native sort
#         .repartition(10)  # Rebalance partitions
#     )

#     # Only materialize when needed
#     return result


# # For the Vanguard-to-Binks pipeline specifically
# class VanguardToBinksPipeline:
#     """
#     Idiomatic implementation of the Vanguard-to-Binks pipeline.
#     """

#     def __init__(self, config: dict):
#         self.config = config

#     def run(self, start_date: str, end_date: str, tenants: list) -> ray.data.Dataset:
#         """
#         Run the entire pipeline without intermediate files.
#         """
#         # Stage 1: Fetch from BigQuery (this might still need to read from BQ)
#         requests = self._fetch_requests(start_date, end_date, tenants)

#         # Stage 2: Filter requests - in memory!
#         filtered = requests.map_batches(
#             FilterRequestsActor,
#             fn_constructor_kwargs={"category": 3},
#             batch_size=1000,
#         )

#         # Stage 2.5: Classify with Gemini - in memory!
#         classified = filtered.map_batches(
#             GeminiClassificationActor,
#             fn_constructor_kwargs={"model": "gemini-1.5-flash"},
#             batch_size=100,  # Smaller batches for API calls
#         )

#         # Stage 3: Fetch blobs - in memory!
#         with_blobs = classified.map_batches(
#             BlobContentFetcherActor,
#             fn_constructor_kwargs={"gcs_project": "augment-production"},
#             batch_size=1,  # Process one request at a time
#         )

#         # Stage 4: Process files - in memory!
#         processed = with_blobs.map_batches(
#             FileProcessorActor,
#             batch_size=10,
#         )

#         # Stage 5: Assemble repositories - in memory!
#         final = processed.groupby("workspace_id").map_groups(
#             lambda group: assemble_repository(group)
#         )

#         return final

#     def _fetch_requests(self, start_date, end_date, tenants):
#         """Initial data fetch - this might need to query external source."""
#         # Could use ray.data.read_bigquery() if available
#         # Or create a custom datasource
#         pass


# Benefits of this approach:
# 1. No disk I/O between stages
# 2. Data stays in Ray's distributed object store
# 3. Automatic memory management and spilling
# 4. Better performance (10-100x faster for many workloads)
# 5. Type safety preserved
# 6. Streaming execution
# 7. Fault tolerance built-in
# 8. Can inspect intermediate results without materializing
