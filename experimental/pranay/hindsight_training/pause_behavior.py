#!/usr/bin/env python3
"""
Pause behavior analysis for hindsight training data.

This module converts the pause_behavior.ipynb notebook into reusable functions
and implements a prefix line removal strategy to handle error nodes in the tree.
"""

from pathlib import Path
from typing import List

from research.eval.harness.utils import read_jsonl_zst
from base.datasets.hindsight_completion import HindsightCompletionDatum
from colorama import Fore, Style
from base.static_analysis.common import guess_lang_from_fp
import tree_sitter as ts

# Pause analysis imports
from research.fim.fim_sampling import CSTFimSampler
from base.static_analysis.parsing import TsParsedFile
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>


def load_hindsight_data(
    data_path: str, model_filter: str = "qweldenv1-1-14b"
) -> List[HindsightCompletionDatum]:
    """Load and filter hindsight completion data."""
    print(f"Loading data from: {data_path}")
    data = read_jsonl_zst(Path(data_path))

    # Filter by model
    filtered_data = [
        datum
        for datum in data
        if datum["completion"]["response"]["model"] == model_filter
    ]

    print(f"Found {len(filtered_data)} entries for model {model_filter}")

    # Convert to hindsight datum
    hindsight_data = [
        HindsightCompletionDatum.from_dict(datum) for datum in filtered_data
    ]

    return hindsight_data


def ground_truth_starts_with_generated_text(datum: HindsightCompletionDatum) -> bool:
    """Filter for ground truth starting with generated text datums."""
    text = datum.completion.response.text
    ground_truth = datum.ground_truth

    # Check if ground truth begins with text and is longer
    return (
        ground_truth.startswith(text)
        and ground_truth != text
        and len(ground_truth.splitlines(keepends=True)) > 1
    )


def filter_datums(
    hindsight_data: List[HindsightCompletionDatum],
    filter_fn=ground_truth_starts_with_generated_text,
) -> List[HindsightCompletionDatum]:
    """Filter for datums."""
    if filter_fn is None:
        return hindsight_data

    interest_datums = [datum for datum in hindsight_data if filter_fn(datum)]
    print(f"Found {len(interest_datums)} interesting datums")
    return interest_datums


def print_datum_info(datum: HindsightCompletionDatum, idx):
    lang = guess_lang_from_fp(datum.completion.request.path)

    print(f"\n{'='*80}")
    print(f"RequestID={datum.completion.request_id} | {idx=} | {lang=}\n")

    print(f"{Fore.GREEN}Generated text:{Style.RESET_ALL}")
    print(repr(datum.completion.response.text))

    mini_prefix = "".join(
        datum.completion.request.prefix.splitlines(keepends=True)[-5:]
    )
    mini_suffix = "".join(datum.completion.request.suffix.splitlines(keepends=True)[:5])

    print(f"\n{Fore.BLUE}Context with ground truth:{Style.RESET_ALL}")
    print(mini_prefix + Fore.RED + datum.ground_truth + Style.RESET_ALL + mini_suffix)


def tree_contains_error_before_middle(node: ts.Node, middle_end_byte_pos: int):
    # recursively check if any child contains an error
    if node.type == "ERROR" and node.end_byte <= middle_end_byte_pos:
        return True
    for child in node.children:
        if tree_contains_error_before_middle(child, middle_end_byte_pos):
            return True
    return False


def _get_pause_spans(
    datum: HindsightCompletionDatum, updated_prefix: str | None = None
):
    fim_sampler = CSTFimSampler()

    path = datum.completion.request.path
    ground_truth = datum.ground_truth
    request = datum.completion.request

    updated_prefix = updated_prefix or request.prefix
    lang = guess_lang_from_fp(path)
    assert lang, f"Could not determine language for {path}"

    full_content = updated_prefix + ground_truth + request.suffix
    pfile = TsParsedFile.parse(path=Path(path), lang=lang, code=full_content)

    early_suffix = "".join(request.suffix.splitlines(keepends=True)[:4])

    middle_start = len(updated_prefix)
    middle_end = middle_start + len(ground_truth) + len(early_suffix)
    middle_range = CharRange(middle_start, middle_end)

    pause_spans = fim_sampler._compute_pause_spans(
        pfile=pfile,
        middle_range=middle_range,
        min_span_size=fim_sampler.min_size_to_pause,
        max_span_size=fim_sampler.max_first_span_size,
    )

    return pause_spans, tree_contains_error_before_middle(
        pfile.ts_tree.root_node, pfile.bmap.char_to_byte(middle_end)
    )


def get_new_prefix(datum: HindsightCompletionDatum, skip_prefix_lines: int = 0):
    prefix = datum.completion.request.prefix
    prefix_lines = prefix.splitlines(keepends=True)
    new_prefix = "".join(prefix_lines[skip_prefix_lines:])
    if skip_prefix_lines == 0:
        assert new_prefix == datum.completion.request.prefix, "Prefix mismatch"
    return new_prefix


def mark_pause_spans(datum: HindsightCompletionDatum, prefix: str | None = None):
    new_prefix = prefix or datum.completion.request.prefix
    pause_spans, tree_contains_error_before_middle = _get_pause_spans(datum, prefix)

    ground_truth = datum.ground_truth
    middle_start = len(new_prefix)
    marked_text = ""
    if pause_spans:
        last_pos = 0

        for _, span in enumerate(pause_spans):
            relative_start = max(0, span.start - middle_start)
            relative_end = min(len(ground_truth), span.stop - middle_start)

            if relative_start >= len(ground_truth):
                break

            # Add text up to this span
            if relative_start > last_pos:
                marked_text += ground_truth[last_pos:relative_start]

            # Add the span with markers
            if relative_end > relative_start:
                span_content = ground_truth[relative_start:relative_end]
                marked_text += f"{Fore.GREEN}[PAUSE_START]{Style.RESET_ALL}{span_content}{Fore.RED}[PAUSE_END]{Style.RESET_ALL}"
                last_pos = relative_end

        # Add remaining text
        if last_pos < len(ground_truth):
            marked_text += ground_truth[last_pos:]

    return marked_text, tree_contains_error_before_middle
