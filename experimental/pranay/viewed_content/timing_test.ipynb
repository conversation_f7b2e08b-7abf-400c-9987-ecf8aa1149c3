#%% md
# Viewed Content Processing Performance Tests

This notebook runs timing tests for individual functions in the recency processing pipeline to identify performance bottlenecks.

## Functions tested:
2. `limit_viewed_content_chunk_size` - Around median 2-4ms with 10 viewed content of 5 KB
3. `deduplicate_viewed_content_against_replacements` - Can be slow when the last replacement text overlaps with viewed content. Tested with 10 viewed content and 150 replacement texts (worst case) and results are median 2-3 ms.
#%%
import logging
import structlog

logging.basicConfig(level=logging.INFO)
logger = structlog.get_logger(__name__)
#%%
%load_ext autoreload
%autoreload 2

# Import the timing test functions
from timing_test import (
    test_limit_viewed_content_performance,
    test_deduplicate_viewed_content_performance,
)
#%% md
## Test 2: limit_viewed_content_chunk_size Performance

This function can be slow with large content requiring smart chunking.
#%%
# Test with real code content (default)
chunking_results = test_limit_viewed_content_performance(
    num_viewed_content=10,
    content_size_kb=5,
    max_chunk_size=1024,
    content_strategy="real_code",
    num_runs=100,
)
#%%
# Test with random text content
chunking_random_results = test_limit_viewed_content_performance(
    num_viewed_content=10,
    content_size_kb=5,
    max_chunk_size=1024,
    content_strategy="random_text",
    num_runs=100,
)
#%% md
## Test 3: deduplicate_viewed_content_against_replacements Performance

This function can be slow when many replacements overlap with viewed content, especially when the overlapping replacement is at a late position (forces iteration through many replacements).
#%%
# Test worst-case scenario (overlap at end)
dedup_results = test_deduplicate_viewed_content_performance(
    num_viewed_content=10, num_replacements=150, num_runs=100
)
#%%
# Test with more replacements (even worse case)
dedup_large_results = test_deduplicate_viewed_content_performance(
    num_viewed_content=10, num_replacements=5000, num_runs=100
)
#%%
